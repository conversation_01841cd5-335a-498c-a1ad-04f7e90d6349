<script setup>
import AppMenu from "./AppMenu.vue";
import { useLayout } from "@/layouts/partials/composables/layout";
const { onMenuToggle } = useLayout();
</script>

<template>
  <div
    class="layout-sidebar border-r-[1px]"
    :class="{ 'border-sidebar layout-sidebar': $route.name !== 'CourseShow',
              'layout-sidebar-course': $route.name === 'CourseShow' }"
  >
    <app-menu />
  </div>
</template>

<style lang="scss" scoped></style>
