<template>
  <Dialog
    :visible="isVisible"
    modal
    :header="!isEdit ? 'Add Role' : 'Edit Role'"
    :style="{ width: '40rem' }"
    @update:visible="isVisible = $event"
  >
    <form
      class="space-y-3"
      @submit.prevent="submit"
    >
      <div>
        <label
          for="name"
          class="block text-sm font-medium text-gray-700"
        >{{ $t('Name') }}</label>
        <div class="mt-1">
          <InputText
            v-model="role.name"
            autofocus
            :type="`text`"
            :value="role.name"
            class="w-full"
          />
        </div>
      </div>

      <div>
        <label
          for="description"
          class="block text-sm font-medium text-gray-700"
        >{{ $t('Description') }}</label>
        <div class="mt-1">
          <InputText
            v-model="role.description"
            :type="`text`"
            :value="role.description"
            class="w-full"
          />
        </div>
      </div>

      <div>
        <label
          for="right"
          class="block text-sm font-medium text-gray-700"
        >{{ $t('Right') }}</label>
        <div class="mt-1">
          <Select
            v-model="role.right"
            :options="rights"
            optionLabel="name"
            optionValue="id"
            :placeholder="$t('Select a right')"
            class="w-full"
          />
        </div>
      </div>
      
      <div class="flex justify-end">
        <Button
          :disabled="isSaving"
          outlined
          type="button"
          :label="$t('Cancel')"
          class="mr-2"
          @click="close"
        />
        <Button
          type="button"
          :loading="isSaving"
          :disabled="isSaving || !isFormValid"
          :label="isEdit ? $t('Save Changes') : $t('Save')"
          @click="submit"
        />
      </div>
    </form>
  </Dialog>
</template>

<script>
import { ROLE_DEFAULT, ROLE_RIGHT_DEFAULT } from '@/databags/role';
import roleApi from '@/api/role';

export default {
  components: {
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    item: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      isSaving: false,
      role: this.__duplicateVar(ROLE_DEFAULT),
      rights: this.__duplicateVar(ROLE_RIGHT_DEFAULT),
    };
  },
  computed: {
    isVisible: {
      get() {
        return this.visible;
      },
      set(value) {
        this.$emit('update:visible', value);
      }
    },
    isEdit() {
      return this.item && this.item.id;
    },
    params() {
      const params = {
        name: this.role.name,
        description: this.role.description,
        right: this.role.right,
      };
      return params;
    },

    isFormValid() {
      return (
        this.role.description
				&& this.role.name
				&& this.role.right
      );
    },
  },
  watch: {
    isShow() {
      this.resetForm();
    },
    item() {
      this.resetForm();
      this.setData();
    },
  },
  methods: {
    close() {
      this.$emit('onClose');
    },
    setData() {
      if (this.item) {
        this.role = this.__duplicateVar(this.item);
      }
    },
    resetForm() {
      this.role = this.__duplicateVar(ROLE_DEFAULT);
    },
    submit() {
      if (this.isEdit) this.update();
      else this.save();
    },
    update() {
      const params = this.params;

      this.isSaving = true;
      const callback = (response) => {
        const item = response.data;
        this.$emit('onUpdate', item);
        this.isSaving = false;

        const message = response.message;
        this.__showNotif('success', this.$t('User'), message);

        this.resetForm();
      };
      const errorCallback = (error) => {
        const message = error.response.data.message;
        this.__showNotif('error', this.$t('Error'), message);
        this.isSaving = false;
      };
      roleApi.update(this.role.id, params, callback, errorCallback);
    },
    save() {
      const params = this.params;

      this.isSaving = true;
      const callback = (response) => {
        const item = response.data;
        this.$emit('onCreate', item);
        this.isSaving = false;

        const message = response.message;
        this.__showNotif('success', this.$t('User'), message);

        this.resetForm();
      };
      const errorCallback = (error) => {
        const message = error.response.data.message;
        this.__showNotif('error', this.$t('Error'), message);
        this.isSaving = false;
      };
      roleApi.create(this.params, callback, errorCallback);
    },
  },
};
</script>
