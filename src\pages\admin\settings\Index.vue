<template>
  <div>
    <!-- Heading -->
    <div class="px-4 sm:px-0 mb-4 mt-2">
      <h3 class="text-base/7 font-semibold text-gray-900">
        {{ $t("Settings") }}
      </h3>
      <SubHeaderForm :type="'settings'" />
    </div>

    <div class="card min-h-[80vh]">
      <Tabs v-model:value="activeRightTab" class="mb-4" value="0">
        <TabList>
          <Tab
            v-for="(tab, index) in tabDefault"
            :key="index"
            :value="tab"
            @click="onTabChange(tab)"
          >
            {{ $t(tab) }}
          </Tab>
          <div class="flex items-center ml-auto">
            <Button
              v-if="activeRightTab === 'General' || activeRightTab === 'Message'"
              type="button"
              class="ml-auto mr-4 mb-2"
              :loading="isUploadFile"
              :disabled="!siteLogo || !siteFavicon || isUploadFile"
              :label="$t('Save Changes')"
              @click="updateSettings"
            />
            <Button
              v-if="rolePermission.can_write && activeRightTab === 'Roles'"
              class="ml-auto mr-4 mb-2"
              type="button"
              :label="$t('Add Role')"
              @click="showCreate()"
            />
            <div
              v-if="
                activeRightTab !== 'Backup' &&
                  activeRightTab !== 'General' &&
                  activeRightTab !== 'Message'
              "
              class="flex space-x-4 mb-2"
            >
              <IconField>
                <InputIcon>
                  <i class="pi pi-search" />
                </InputIcon>
                <InputText
                  v-model="keyword"
                  :placeholder="$t('Keyword Search')"
                  class="min-w-[246px]"
                />
                <!-- Clear Icon -->
                <InputIcon v-if="keyword" @click="keyword = ''">
                  <i class="pointer pi pi-times" />
                </InputIcon>
              </IconField>
            </div>
          </div>
        </TabList>
      </Tabs>
      <DataTable
        v-if="
          activeRightTab !== 'Backup' &&
            activeRightTab !== 'General' &&
            activeRightTab !== 'Message' && !isFetching
        "
        ref="myTable"
        :value="items"
        tableStyle="min-width: 50rem"
        @update:sortOrder="updateSortOrder"
        @update:sortField="updatesortField"
      >
        <template #empty>
          <div class="text-center py-20">
            No data available.
          </div>
        </template>
        <template v-if="!isReset">
          <Column :header="parseLabel('No.')" headerStyle="width:3rem">
            <template #body="slotProps">
              {{ slotProps.index + 1 + (currentPage - 1) * limit }}.
            </template>
          </Column>
          <Column field="name" :header="parseLabel('Name')" style="width: 50%" />
          <Column
            v-if="activeRightTab === 'Roles'"
            field="right"
            :header="parseLabel('Right')"
            style="width: 50%"
          />
          <Column
            v-if="activeRightTab === 'Platform Feature'"
            field="description"
            :header="parseLabel('Description')"
            style="width: 28%"
          />
          <Column
            v-if="activeRightTab === 'Platform Feature' && rolePermission.can_write"
            field="is_enable"
            :header="parseLabel('Enabled')"
            style="width: 28%"
            :class="{ 'pointer-events-none': !rolePermission.can_write }"
          >
            <template #body="slotProps">
              <ToggleSwitch
                v-model="slotProps.data.is_enable"
                @change="onChangeEnabled(slotProps.data, slotProps.data.is_enable)"
              />
            </template>
          </Column>
          <Column
            v-if="
              (rolePermission.can_write || rolePermission.can_delete) &&
                activeRightTab === 'Roles'
            "
            :header="parseLabel('Actions')"
            style="width: 20%"
          >
            <template #body="slotProps">
              <div class="flex justify-content-left">
                <Button
                  v-if="rolePermission.can_write"
                  icon="pi pi-lock"
                  severity="warn"
                  variant="text"
                  aria-label="Edit"
                  class="mr-2"
                  @click="editRoleItem(slotProps)"
                />
                <Button
                  v-if="rolePermission.can_write"
                  icon="pi pi-pencil"
                  variant="text"
                  aria-label="Edit"
                  class="mr-2"
                  @click="editItem(slotProps)"
                />

                <Button
                  v-if="rolePermission.can_delete"
                  icon="pi pi-trash"
                  severity="danger"
                  variant="text"
                  aria-label="Delete"
                  @click="deleteItem(slotProps.data)"
                />
              </div>
            </template>
          </Column>
        </template>
      </DataTable>
      <div v-if="activeRightTab === 'Backup'" class="space-y-4">
        <Card>
          <template #content>
            <div class="py-32 text-center">
              {{ $t("No data available.") }}
            </div>
          </template>
        </Card>
      </div>
      <div v-if="activeRightTab === 'General'" class="space-y-4">
        <div>
          <h6 class="font-semibold text-gray-900 mb-2">
            {{ $t("Application Logo") }}
          </h6>
          <div class="lg:w-1/3" :class="{ disabled: isUploadLogo }">
            <Uploader
              id="fileUploadInput"
              class="mt-2 w-full"
              :preview="true"
              :multiple="false"
              :type="'image'"
              :titlePlaceHolder="'image'"
              :progressUploadFile="progressUploadFile"
              :existingPreviewImageOrVideo="siteLogo"
              @remove="onRemoveFile"
              @upload="onImportSelected($event, 'siteLogo')"
            />
          </div>
          <p class="text-xs text-gray-500">
            {{ $t("File must be transparent PNG, 812x300 pixels in size") }}
          </p>
          <h6 class="font-semibold text-gray-900 mb-2 mt-4">
            {{ $t("Application Favicon") }}
          </h6>
          <div class="lg:w-1/3" :class="{ disabled: isUploadFavicon }">
            <Uploader
              id="fileUploadFaviconInput"
              class="mt-2 w-full"
              :preview="true"
              :multiple="false"
              :type="'image'"
              :titlePlaceHolder="'Image'"
              :progressUploadFile="progressUploadFaviconFile"
              :existingPreviewImageOrVideo="siteFavicon"
              @remove="onRemoveFileFavicon"
              @upload="onImportSelected($event, 'siteFavicon')"
            />
          </div>
          <p class="text-xs text-gray-500">
            {{ $t("File must be transparent PNG, 180x180 pixels in size") }}
          </p>
          <div class="lg:w-1/3 mt-8">
            <h6 class="font-semibold text-gray-900 mb-4">
              {{ $t("Language") }}
            </h6>
            <Dropdown
              v-model="selectedLanguage"
              class="w-full"
              :options="languageOption"
              optionLabel="name"
              placeholder="Select a Filter"
            />
          </div>
        </div>
      </div>
      <div v-if="activeRightTab === 'Message'" class="space-y-4">
        <div>
          <div class="">
            <!-- welcoming section -->
            <h6 class="font-semibold text-gray-900 mb-4">
              {{ $t("Welcome Message") }}
            </h6>
            <DescriptionBox
              class="mb-4 md:w-2/3 lg:w-2/4"
              :description="welcomingMessage"
              :rows="8"
              :isMediumHeight="true"
              @update:description="updateWelcoming"
            />

            <h6 class="font-semibold text-gray-900 mb-4 mt-8">
              {{ $t("Welcome Video/Image") }}
            </h6>
            <div class="md:w-2/3 lg:w-2/4" :class="{ disabled: isUploadVideo }">
              <Uploader
                id="fileUploadInputVideo"
                class="mt-2"
                :preview="true"
                :multiple="false"
                :customFileTypes="'video,image'"
                :progressUploadFile="progressUploadFileWelcome"
                :existingPreviewImageOrVideo="welcomingMedia"
                @remove="onRemoveFileMessage"
                @upload="onImportSelected($event, 'welcome')"
              />
            </div>

            <h6
              v-if="isVideo(welcomingMedia)"
              class="font-semibold text-gray-900 mb-4 mt-8"
            >
              {{ $t("Thumbnail Video") }}
              <span class="text-xs text-gray-400">{{
                $t("Recommended image size is 1280x720 (16:9)")
              }}</span>
            </h6>
            <div v-if="isVideo(welcomingMedia)" class="md:w-2/3 lg:w-2/4" :class="{ disabled: isUploadImage }">
              <Uploader
                id="fileUploadInputThumbnail"
                class="mt-2"
                :preview="true"
                :multiple="false"
                :type="'image'"
                :titlePlaceHolder="'Image'"
                :progressUploadFile="progressUploadFileWelcomeThumbnail"
                :existingPreviewImageOrVideo="welcomingThumbnail"
                @remove="onRemoveFileThumbnail"
                @upload="onImportSelected($event, 'welcomeThumbnail')"
              />
            </div>
          </div>
        </div>
      </div>
      <div
        v-if="
          activeRightTab !== 'Backup' &&
            activeRightTab !== 'General' &&
            activeRightTab !== 'Message'
        "
        class="grid grid-cols-3 px-0 mx-0 mt-8"
      >
        <div />
        <div v-if="!isFetching && items.length > 0">
          <Paginator
            :rows="limit"
            :first="(currentPage - 1) * limit"
            :totalRecords="total"
            :rowsPerPageOptions="[10, 20, 30]"
            template="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport"
            :currentPageReportTemplate="`({first} ${$t('to')} {last} ${$t('of')} {totalRecords})`"
            class="mt-8"
            @page="pageUpdate"
          />
        </div>
        <div v-if="!isFetching && items.length > 0" class="flex items-center w-full justify-end px-0 mx-0">
          <div class="pt-7">
            {{ $t("Record(s) per page") }}
          </div>
          <div>
            <Paginator
              :rows="limit && items.length > 0 "
              :first="(currentPage - 1) * limit"
              :totalRecords="total"
              :rowsPerPageOptions="[10, 20, 30]"
              template="RowsPerPageDropdown"
              :currentPageReportTemplate="`({first} ${$t('to')} {last} ${$t('of')} {totalRecords})`"
              class="mt-8 pr-0 mr-0"
              @page="pageUpdate"
            />
          </div>
        </div>
      </div>
    </div>
    <ModalForm
      v-model:visible="isModalVisible"
      :item="selectedItem"
      @onCreate="onCreate"
      @onUpdate="onUpdate"
      @onClose="closeModal"
    />
    <ModalRoleForm
      v-model:visible="isModaRolelVisible"
      :item="selectedItem"
      @onUpdate="onUpdate"
      @onClose="closeModal"
    />
  </div>
</template>

<script>
import featureApi from "@/api/platform-feature";
import roleApi from "@/api/role";
import { delay } from "@/libraries/helper";
import ModalForm from "@/components/roles/ModalForm.vue";
import ModalRoleForm from "@/components/roles/ModalRoleForm.vue";
import { mapGetters, mapActions } from "vuex";
import Tabs from "primevue/tabs";
import Card from "primevue/card";
import Uploader from "@/components/global/Uploader.vue";
import settingApi from "@/api/setting";
import fileApi from "@/api/files";
import TabList from "primevue/tablist";
import Tab from "primevue/tab";
import DescriptionBox from "@/components/form/DescriptionBox.vue";
import Dropdown from "primevue/dropdown";

export default {
  components: {
    ModalForm,
    ModalRoleForm,
    Tabs,
    TabList,
    Tab,
    Card,
    Uploader,
    DescriptionBox,
    Dropdown,
  },
  props: {},
  data() {
    return {
      isFetching: false,
      currentPage: 1,
      totalPage: 1,
      orderBy: "created_at",
      sortBy: "desc",
      limit: 10,
      keyword: "",
      items: [],
      total: 0,
      isReset: false,
      isModalVisible: false,
      isModaRolelVisible: false,
      selectedItem: null,
      activeRightTab: "General",
      isSaving: false,
      tabDefault: [],
      progressUploadFile: 0,
      progressUploadFaviconFile: 0,
      siteLogo: null,
      siteFavicon: null,
      isUploadFile: false,
      welcomingMessage: null,
      progressUploadFileWelcome: 0,
      progressUploadFileWelcomeThumbnail: 0,
      welcomingMedia: null,
      welcomingThumbnail: null,
      selectedLanguage: { id: "en", name: "English" },
      languageOption: [
        { id: "en", name: "English" },
        { id: "no", name: "Norge" },
      ],
      isUploadFavicon: false,
      isUploadLogo: false,
      isUploadVideo: false,
      isUploadImage: false,

    };
  },
  computed: {
    ...mapGetters({
      userFeatures: "auth/userFeatures",
      userRole: "auth/userRole",
    }),
    roleFeature() {
      return this.userFeatures.find((item) => item.name === "role");
    },
    rolePermission() {
      return this.roleFeature?.permission ? this.roleFeature.permission : {};
    },
    platformFeature() {
      return this.userFeatures.find((item) => item.name === "feature");
    },
    permissionPlatformFeature() {
      return this.platformFeature?.permission ? this.platformFeature.permission : {};
    },
  },
  watch: {
    keyword() {
      delay(() => {
        this.fetchList(true);
      }, 500);
    },
    isModalVisible() {
      if (!this.isModalVisible) {
        this.selectedItem = null;
      }
    },
    isModaRolelVisible() {
      if (!this.isModaRolelVisible) {
        this.selectedItem = null;
      }
    },
  },
  created() {
    this.fetchList();
    const isSuperAndAdmin = ["super_admin", "admin"].includes(this.userRole.right);
    if (isSuperAndAdmin) {
      this.tabDefault.push("General");
      this.tabDefault.push("Message");
    }
    if (this.roleFeature.is_enable && this.rolePermission.can_read) {
      this.tabDefault.push("Roles");
    }
    if ((this.platformFeature.is_enable && this.permissionPlatformFeature.can_read)) {
      this.tabDefault.push("Platform Feature");
    }
    // this.tabDefault.push("Backup");
  },
  mounted() {
    this.initSettings();
  },
  beforeUnmount() {},
  methods: {
    ...mapActions({
      updateSiteLogo: "auth/updateSiteLogo",
      updateSiteLocale: "auth/updateSiteLocale",
      updateSiteFavicon: "auth/updateSiteFavicon",
    }),
    isVideo(link) {
      if (!link) return false;
      const lowerLink = link.toLowerCase();
      return (
        lowerLink.endsWith(".mp4") ||
        lowerLink.endsWith(".webm") ||
        lowerLink.endsWith(".ogg") ||
        lowerLink.endsWith(".mov") ||
        lowerLink.endsWith(".avi")
      );
    },
    parseLabel(label) {
      return this.$t(label);
    },
    updateWelcoming(newValue) {
      // eslint-disable-next-line vue/no-mutating-props
      this.welcomingMessage = newValue || "";
    },
    onRemoveFile() {
      this.siteLogo = null;
      this.progressUploadFile = 0;
    },
    onRemoveFileFavicon() {
      this.siteFavicon = null;
      this.progressUploadFaviconFile = 0;
    },
    onRemoveFileMessage() {
      this.welcomingMedia = null;
      this.progressUploadFileWelcome = 0;
    },
    onRemoveFileThumbnail() {
      this.welcomingThumbnail = null;
      this.progressUploadFileWelcomeThumbnail = 0;
    },
    checkUploadType(type, folder) {
      const allowedImageTypes = [
        "image/jpeg",
        "image/gif",
        "image/png",
        "image/jpg",
        "image/webp",
      ];
      const allowedLogoImageTypes = ["image/png", "image/webp"];
      const allowedVideoTypes = [
        "video/mp4",
        "video/webm",
        "video/ogg",
        "video/quicktime", // .mov
      ];
      if (folder === "welcome" || folder === "welcomeThumbnail") {
        return allowedImageTypes.includes(type) || allowedVideoTypes.includes(type);
      } else {
        return allowedLogoImageTypes.includes(type);
      }
    },
    onImportSelected(event, folderType) {
      return new Promise((resolve, reject) => {
        const files = event;
        const file = files[0];
        const folderTypeMappings = {
          welcome: {
            mediaPathKey: 'welcomingMedia',
            uploadFlagKey: 'isUploadVideo',
          },
          siteLogo: {
            mediaPathKey: 'siteLogo',
            uploadFlagKey: 'isUploadLogo',
          },
          siteFavicon: {
            mediaPathKey: 'siteFavicon',
            uploadFlagKey: 'isUploadFavicon',
          },
          welcomeThumbnail: {
            mediaPathKey: 'welcomingThumbnail',
            uploadFlagKey: 'isUploadImage',
          },
        };
        const { mediaPathKey, uploadFlagKey } = folderTypeMappings[folderType];
        if (folderTypeMappings[folderType]) {
          this[uploadFlagKey] = true;
        }
        if (files.length > 0) {
          if (this.checkUploadType(file.type, folderType)) {
            if (!file) {
              this.__showNotif(
                "warning",
                this.$t('Upload File'),
                this.$t('Sorry, currently we can\'t upload the file')
              );
              if (folderTypeMappings[folderType]) {
                this[mediaPathKey] = null;
                this[uploadFlagKey] = true;
              }
              return;
            }
            const params = new FormData();
            params.append("file", file);
            params.append("folder", folderType);
            const callback = async (response) => {
              const mediaPath = response.fullPath;
              if (folderTypeMappings[folderType]) {
                this[mediaPathKey] = mediaPath;
                this[uploadFlagKey] = true;
              }
              resolve(response); // Resolve the promise with the response
            };

            if (document.querySelector("#fileUploadInput"))
              document.querySelector("#fileUploadInput").value = "";
            if (document.querySelector("#fileUploadInputVideo"))
              document.querySelector("#fileUploadInputVideo").value = "";
            if (document.querySelector("#fileUploadInputThumbnail"))
              document.querySelector("#fileUploadInputThumbnail").value = "";
            const errorCallback = () => {
              // this.isUploadingLottie = false;
              this.__showNotif(
                "warning",
                this.$t('Upload File'),
                this.$t('Sorry, currently we can\'t upload the file')
              );
              if (folderTypeMappings[folderType]) {
                this[uploadFlagKey] = false;
              }
              reject(error); // Reject the promise with the error
            };

            const progressCallback = (progress) => {
              if (folderType === "welcome") this.progressUploadFileWelcome = progress;
              if (folderType === "siteLogo") this.progressUploadFile = progress;
              if (folderType === "siteFavicon") this.progressUploadFaviconFile = progress;
              if (folderType === "welcomeThumbnail")
                this.progressUploadFileWelcomeThumbnail = progress;
            };
            fileApi.upload(params, callback, errorCallback, progressCallback);
          } else {
            if (folderTypeMappings[folderType]) {
              this[mediaPathKey] = null;
              this[uploadFlagKey] = false;
            }
            // this.isTemplateReady = false;
            this.__showNotif("warning", this.$t('Upload File'),  this.$t('Unsupported file'));
            // eslint-disable-next-line
            document.querySelector("#fileUploadFaviconInput").value = "";
            return;
          }
        }
      });
    },
    updateSettings() {
      const params = {
        settings: [
          {
            key: "site_logo",
            value: this.siteLogo,
            type: "image",
          },
          {
            key: "site_favicon",
            value: this.siteFavicon,
            type: "image",
          },
          {
            key: "site_lang",
            value: this.selectedLanguage.id,
            type: "text",
          },
          {
            key: "greeting_message",
            value: this.welcomingMessage,
            type: "text",
          },
          {
            key: "video_message",
            value: this.welcomingMedia,
            type: "video",
          },
          {
            key: "video_thumbnail",
            value: this.welcomingThumbnail,
            type: "video",
          },
        ],
      };
      localStorage.setItem("locale", this.selectedLanguage.id);
      localStorage.setItem("logo", this.siteLogo);
      localStorage.setItem("favicon", this.siteFavicon);
      this.updateSiteLocale(this.selectedLanguage.id);
      this.updateSiteLogo(this.siteLogo);
      this.updateSiteFavicon(this.siteFavicon);
      this.$i18n.locale = this.selectedLanguage.id;
      const callback = (response) => {
        this.isSaving = false;
        const message = response.message;
        this.__showNotif("success", "message", message);
      };
      const errorCallback = (error) => {
        const message = error.response.data.message;
        this.__showNotif("error", this.$t('Error'), message);
        this.isSaving = false;
      };
      settingApi.bulkCreateOrUpdate(params, callback, errorCallback);
    },
    getCurrentLang(id) {
      return this.languageOption.find((lang) => lang.id === id);
    },
    initSettings() {
      const params = {};
      this.isSaving = true;
      const callback = (response) => {
        this.isFetching = false;
        const data = response.data;
        for (let index = 0; index < data.length; index++) {
          const element = data[index];
          if (element.key === "greeting_message") this.welcomingMessage = element.value;
          if (element.key === "video_message") this.welcomingMedia = element.value;
          if (element.key === "video_thumbnail") this.welcomingThumbnail = element.value;
          if (element.key === "site_logo") this.siteLogo = element.value;
          if (element.key === "site_favicon") this.siteFavicon = element.value;
          if (element.key === "site_lang")
            this.selectedLanguage = this.getCurrentLang(element.value);
        }
      };
      const errorCallback = (error) => {
        const message = error.response.data.message;
        this.__showNotif("error", this.$t('Error'), message);
        this.isFetching = false;
      };
      settingApi.getList(params, callback, errorCallback);
    },
    onChangeEnabled(item, state) {
      const params = {
        name: item.name,
        description: item.description,
        is_enable: state,
      };
      this.isSaving = true;
      const callback = (response) => {
        this.isSaving = false;
        const message = response.message;
        this.__showNotif("success", this.$t('Settings'), message);
      };
      const errorCallback = (error) => {
        const message = error.response.data.message;
        this.__showNotif("error", this.$t('Error'), message);
        this.isSaving = false;
      };
      featureApi.update(item.id, params, callback, errorCallback);
    },
    onTabChange(tab) {
      if (tab !== "Backup") this.fetchList(true);
    },
    fetchList(isReset = false) {
      if (isReset) {
        this.currentPage = 1;
        this.items = [];
      }
      this.isFetching = true;
      const params = {
        order_by: 'name',
        sort_by: 'asc',
        limit: this.limit,
        page: this.currentPage,
      };
      if (this.keyword) params.keyword = this.keyword;
      const callback = (response) => {
        const data = response.data;
        this.totalPage = response.meta.last_page;
        this.total = response.meta.total;
        this.items = data;
        this.isFetching = false;
        if (this.activeRightTab === "Platform Feature") {
          for (let index = 0; index < this.items.length; index++) {
            const element = this.items[index];
            element.is_enable = element.is_enable === 1;
          }
        }
      };
      const errorCallback = (error) => {
        const message = error.response.data.message;
        this.__showNotif("error", this.$t('Error'), message);
        this.isFetching = false;
      };
      if (this.activeRightTab === "Roles")
        roleApi.getList(params, callback, errorCallback);
      if (this.activeRightTab === "Platform Feature")
        featureApi.getList(params, callback, errorCallback);
    },
    updatesortField(event) {
      this.orderBy = event;
    },
    updateSortOrder(event) {
      if (event === 1) {
        this.sortBy = "desc";
      } else {
        this.sortBy = "asc";
      }
      this.fetchList();
    },
    pageUpdate(event) {
      this.currentPage = event.page + 1;
      if (event.rows !== this.limit) this.currentPage = 1;
      this.limit = event.rows;
      this.fetchList();
    },
    resetAll() {
      this.isReset = true;
      // Reset sorting
      this.keyword = "";
      this.orderBy = "created_at"; // Default sort field
      this.sortBy = "desc";
      this.fetchList(true);
      setTimeout(() => {
        this.isReset = false;
      }, 100);
    },
    editItem(item) {
      this.isModalVisible = true;
      this.selectedItem = item.data;
    },
    editRoleItem(item) {
      this.isModaRolelVisible = true;
      this.selectedItem = item.data;
    },
    onRemove(selectedId) {
      const index = this.items.findIndex((curr) => curr.id === selectedId);
      if (index !== -1) this.items.splice(index, 1);
    },
    approveRemove(id) {
      // If no selected
      this.isDeleting = true;
      const callback = (response) => {
        this.onRemove(id);
        this.isDeleting = false;
        const message = response.message;
        this.__showNotif("success", this.$t('Success'), message);
      };
      const errorCallback = (error) => {
        const message = error.response.data.message;
        this.__showNotif("error", this.$t('Error'), message);
        this.isDeleting = false;
      };
      roleApi.delete(id, callback, errorCallback);
    },
    deleteItem(item) {
      this.$confirm.require({
        message: `${this.$t("Do you want to delete this")} ${item.name}?`,
        header: `${this.$t("Delete Role")}`,
        icon: "pi pi-info-circle",
        rejectProps: {
          label: `${this.$t("Cancel")}`,
          severity: "secondary",
          outlined: true,
        },
        acceptProps: {
          label: `${this.$t("Delete")}`,
          severity: "danger",
        },
        accept: () => {
          this.approveRemove(item.id);
        },
        reject: () => {},
      });
    },
    showCreate() {
      this.selectedItem = null;
      this.isModalVisible = true;
    },
    onUpdate(item) {
      const itemIndex = this.items.findIndex((curr) => curr.id === item.id);
      if (itemIndex !== -1) Object.assign(this.items[itemIndex], item);
      this.closeModal();
    },
    onCreate(item) {
      if (this.sortBy === "desc") this.items.unshift(item);
      else if (this.sortBy === "asc") this.items.push(item);
      this.closeModal();
    },
    closeModal() {
      this.isModalVisible = false;
      this.isModaRolelVisible = false;
      this.selected = null;
    },
  },
};
</script>
