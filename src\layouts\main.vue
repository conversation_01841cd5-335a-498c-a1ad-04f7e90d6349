<script setup>
import { useLayout } from "@/layouts/partials/composables/layout";
import { computed, ref, watch } from "vue";
import AppSidebar from "@/layouts/partials/AppSidebar.vue";
import AppTopbar from "@/layouts/partials/AppTopbar.vue";
import ProgressBar from 'primevue/progressbar';
import { useRoute } from "vue-router";
import { useStore } from 'vuex';
const store = useStore();
const { layoutConfig, layoutState, isSidebarActive, resetMenu } = useLayout();
const route = useRoute();
const outsideClickListener = ref(null);

watch(isSidebarActive, (newVal) => {
  if (newVal) {
    bindOutsideClickListener();
  } else {
    unbindOutsideClickListener();
  }
});

const isShowPage = computed(() => {
  return route.name === "CourseShow" || route.name === "LibraryDetails" || route.name === "SupportDetails" || route.name === "CoursePreviewLoggedInUser";
});

const containerClass = computed(() => {
  return {
    "layout-overlay": layoutConfig.menuMode === "overlay",
    "layout-static": layoutConfig.menuMode === "static",
    "layout-static-inactive":
      layoutState.staticMenuDesktopInactive && layoutConfig.menuMode === "static",
    "layout-overlay-active": layoutState.overlayMenuActive,
    "layout-mobile-active": layoutState.staticMenuMobileActive,
    "bg-white": isShowPage.value,
    "bg-gradient-to-b from-gray-100 to-gray-50": !isShowPage.value,
  };
});

function bindOutsideClickListener() {
  if (!outsideClickListener.value) {
    outsideClickListener.value = (event) => {
      console.log(isOutsideClicked(event), 'kia');
      if (isOutsideClicked(event)) {
        resetMenu();
      }
    };
    document.addEventListener("click", outsideClickListener.value);
  }
}

function unbindOutsideClickListener() {
  if (outsideClickListener.value) {
    document.removeEventListener("click", outsideClickListener);
    outsideClickListener.value = null;
  }
}

function isOutsideClicked(event) {
  const sidebarEl = document.querySelector(".layout-sidebar");
  const topbarEl = document.querySelector(".layout-menu-button");

  // return !(
  //   sidebarEl.isSameNode(event.target) ||
  //   sidebarEl.contains(event.target) ||
  //   topbarEl.isSameNode(event.target) ||
  //   topbarEl.contains(event.target)
  // );
}

const routeName = computed(() => route.name);

const isCourseEditorShow = computed(() => {
  return [
    "CoursePreviewAndPublish",
    "CourseAnalytics",
    "CourseOptions",
    "CourseLessons",
    "CourseBasicEdit",
    "CourseBasicCreate",
    "CoursePreviewLoggedInUser"
  ].includes(routeName.value);
});

// Map Getters using computed properties
const lessons = computed(() => store.getters['course/getLessons']);
const getGlobalProgressLoading = computed(() => store.getters['progress/getGlobalProgressLoading']);

const selectedLessonItem = computed(() => store.getters['course/getSelectedLessonItem']);

// Computed property for isQuiz
const isQuiz = computed(() => {
  if (selectedLessonItem.value && Object.prototype.hasOwnProperty.call(selectedLessonItem.value, 'running')) {
    return true;
  } else {
    return false;
  }
});
</script>

<template>
  <div class="layout-wrapper" :class="containerClass">
    <div v-if="getGlobalProgressLoading > 0 && getGlobalProgressLoading < 100" class="progress-bar w-full" style="height: 2px; z-index: 9999">
      <ProgressBar :showValue="false" :value="getGlobalProgressLoading" class="w-full" style="height: 6px; z-index: 9999" />
    </div> 
    <app-topbar v-if="route.name !== 'CourseShow'" />
    <app-topbar-course v-if="route.name === 'CourseShow'" class="z-[99]" />

    <app-sidebar />
    <Toast :closable="true" />
    <ConfirmDialog />
    <div
      class="layout-main-container"
      :class="{
        'course-show-padding layout-show': route.name === 'CourseShow',
        'lib-support-show-padding layout-show': isShowPage,
        'main-padding': !isShowPage,
      }"
    >
      <div
        :class="{
          'layout-main': !isCourseEditorShow && !isShowPage,
          'layout-main-wo-pb': isShowPage
        }"
      >
        <router-view />
      </div>
    </div>
    <div class="layout-mask animate-fadein" />
  </div>
  <Toast />
</template>
