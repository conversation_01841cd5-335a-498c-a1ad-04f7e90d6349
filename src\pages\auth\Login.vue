<template>
  <div class="min-h-screen flex flex-col justify-center overflow-hidden py-4 lg:py-0">
    <div class="mx-auto">
      <img :key="siteLogo" class="mx-auto h-12 mb-5 w-auto" :src="dynamicSiteLogo" alt="LMS">
    </div>

    <div class="mt-2 sm:mx-auto sm:w-full lg:max-w-md drop-shadow-xl">
      <div class="bg-white py-12 px-8 shadow sm:rounded-xl lg:px-14 md:px-14">
        <form class="space-y-3" @submit.prevent="loginSubmit">
          <div>
            <div class="mb-2">
              {{ $t("Email Address") }}
            </div>
            <t-input
              v-model="email"
              :dataTest="'email'"
              :type="`email`"
              :value="email"
              class="w-full"
            />
            <span
              v-if="!isValidEmailAddress && email && email.length !== 0"
              class="text-red-500 text-xs"
            >{{ $t("Invalid Email Address") }}</span>
          </div>
          <div>
            <div class="mb-2">
              {{ $t("Password") }}
            </div>
            <t-input
              v-model="password"
              :dataTest="'password'"
              :type="`password`"
              :value="password"
              class="w-full"
            />
          </div>
          <div class="flex items-center justify-between">
            <div class="flex items-center mt-4">
              <input
                id="remember-me"
                name="remember-me"
                type="checkbox"
                class="h-4 w-4 focus:ring-pink-500 border-gray-300 rounded"
              >
              <label for="remember-me" class="ml-2 block text-sm text-gray-900">
                {{ $t("Remember me") }}
              </label>
            </div>
            <div class="text-sm pt-4">
              <router-link to="/forgot-password" class="text-blue">
                {{ $t("Forgot your password?") }}
              </router-link>
            </div>
          </div>
          <div>
            <Button
              type="submit"
              :loading="isSubmitting"
              :disabled="isSubmitting || !isFormValid"
              :label="$t('Sign In')"
              class="w-full"
            />
          </div>
        </form>
        <div class="mt-8">
          <div class="relative">
            <div class="absolute inset-0 flex items-center">
              <div class="w-full border-t border-gray-300" />
            </div>
            <div class="relative flex justify-center text-sm">
              <span class="px-2 bg-white text-gray-500">
                {{ $t("Or login with") }}
              </span>
            </div>
          </div>

          <div class="mt-8">
            <div v-if="!isSignIn" class="flex items-center">
              <router-link
                to="/magic-link"
                class="flex items-center h-[50px] drop-shadow-sm w-full justify-left bg-[#EEEEEE] border border-gray-300 rounded-md shadow-sm bg-[#fff] text-sm font-medium hover:bg-gray-50"
              >
                <div
                  class="justify-center absolute flex right-0 left-0 mx-auto font-medium"
                >
                  {{ $t("Login With Magic Link") }}
                </div>
              </router-link>
            </div>
          </div>
        </div>
      </div>
      <div class="text-sm text-center mt-3 pt-0">
        <router-link to="/resend-email" class="font-medium">
          {{ $t("Resend Email Verification") }}
        </router-link>
      </div>
    </div>
  </div>
  <div
    class="mt-2 md:mr-12 md:fixed md:bottom-0 flex md:right-0 mb-4 justify-center text-center items-center"
  >
    <Menu as="div" class="relative z-10 mr-4">
      <div>
        <MenuButton
          class="max-w-xs flex items-center focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          <span class="sr-only">Open Lang Menu</span>
          <div v-if="$i18n.locale === 'no'">Norsk</div>
          <div v-if="$i18n.locale === 'en'">English</div>
        </MenuButton>
      </div>
      <transition
        enterActiveClass="transition ease-out duration-100"
        enterFromClass="transform opacity-0 scale-95"
        enterToClass="transform opacity-100 scale-100"
        leaveActiveClass="transition ease-in duration-75"
        leaveFromClass="transform opacity-100 scale-100"
        leaveToClass="transform opacity-0 scale-95"
      >
        <MenuItems
          class="origin-top-right absolute right-0 mt-2 w-48 mt-[-9em] rounded-md shadow-lg py-1 bg-white ring-1 ring-black ring-opacity-5 focus:outline-none"
        >
          <MenuItem
            v-for="item in langMenus"
            v-slot="{ active }"
            :key="item.name"
            @click="changeLocale(item)"
          >
            <div
              class="flex justify-between items-center px-2"
              :class="[active ? 'bg-gray-100' : '', 'block text-sm text-gray-700']"
            >
              <div>
                {{ item.name }}
              </div>
              <img
                v-if="item.name === 'English'"
                class="h-7 w-7 rounded-full my-1"
                src="../../assets/images/lang/icons8-english-48.png"
                alt="country-image"
              >
              <img
                v-if="item.name === 'Bahasa'"
                class="h-7 w-7 rounded-full my-1"
                src="../../assets/images/lang/icons8-indonesia-48.png"
                alt="country-image"
              >
              <img
                v-if="item.name === 'Norsk'"
                class="h-7 w-7 rounded-full my-1"
                src="../../assets/images/lang/icons8-norway-48.png"
                alt="country-image"
              >
            </div>
          </MenuItem>
        </MenuItems>
      </transition>
    </Menu>
    <a href="https://mymeet.no/privacy-policy/" target="_blank" class="ml-2">{{
      $t("Privacy Policy")
    }}</a>
  </div>
</template>

<script>
import authApi from "@/api/auth";
import TInput from "@/components/form/Input.vue";
import { isValidEmail } from "@/libraries/helper";
import { mapGetters, mapActions } from "vuex";
import { Menu, MenuButton, MenuItem, MenuItems } from "@headlessui/vue";
import settingApi from "@/api/setting";

export default {
  components: {
    TInput,
    Menu,
    MenuButton,
    MenuItem,
    MenuItems,
  },
  setup() {
    return {};
  },
  data() {
    return {
      email: null,
      password: null,
      isSubmitting: false,
      isSignIn: false,
      isInit: false,
      langMenus: [
        { name: "English", key: "en" },
        { name: "Norsk", key: "no" },
        { name: "Bahasa", key: "id" },
      ],
    };
  },
  computed: {
    ...mapGetters({
      siteLogo: "auth/siteLogo",
    }),
    dynamicSiteLogo() {
      return this.siteLogo ? this.siteLogo : "https://minio.beta.mylms.no/betalms/siteLogo/images/812-300.png";
    },
    isValidEmailAddress() {
      return isValidEmail(this.email);
    },
    isFormValid() {
      return this.isValidEmailAddress && this.password;
    },
  },
  created() {
    const { register } = this.$route.query ? this.$route.query : false;
    if (register) {
      this.__showNotif(
        "register",
        this.$t('User'),
        this.$t(
          "Thank you for your registration. We've sent an email to your email address. Please Follow the link to activate your account"
        )
      );
    }
  },
  mounted() {
    this.changeGlobalLocale();
  },
  methods: {
    ...mapActions({
      updateSiteLocale: "auth/updateSiteLocale",
    }),
    fetchSetting() {
      return new Promise((resolve, reject) => {
        const params = {};
        const callback = (response) => {
          const data = response.data;
          resolve(data);
        };
        const errorCallback = (error) => {
          reject(error);
        };
        settingApi.getList(params, callback, errorCallback);
      });
    },
    async changeGlobalLocale() {
      let locale = `en`;
      try {
        const data = await this.fetchSetting();
        locale = this.getSettingValue(data, "site_lang");
      } catch (e) {
        console.log(e);
        const message = e.response?.data?.message ;
        this.__showNotif("error", this.$t('Error'), message);
      }
      this.updateSiteLocale(locale);
    },
    getSettingValue(settings, key) {
      const item = settings.find((setting) => setting.key === key);
      return item ? item.value : null;
    },
    changeLocale(locale) {
      localStorage.setItem(`locale`, locale.key);
      this.$i18n.locale = locale.key;
    },
    loginSubmit() {
      this.isSubmitting = true;
      const params = {
        email: this.email,
        password: this.password,
        lang: "en",
      };
      const callback = (response) => {
        this.$store.dispatch("auth/clearAuth");
        const data = response.token;
        this.$store.dispatch("auth/setSession", data);
        localStorage.removeItem("renderList");
        window.location.href = "/";
        this.isSubmitting = false;
      };
      const errorCallback = (error) => {
        const message = error.response.data.message;
        this.__showNotif("error", this.$t('Error'), message);
        this.isSubmitting = false;
      };
      authApi.login(params, callback, errorCallback);
    },
  },
};
</script>
