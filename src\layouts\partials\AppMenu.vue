<template>
  <ul class="layout-menu">
    <template v-for="(item, i) in menus" :key="item">
      <app-menu-item
        v-if="
          !isFetchingMenu &&
            userRole &&
            !item.separator &&
            (item.roles.length === 0 || item.roles.includes(userRole.right))
        "
        :item="item"
        :index="i"
      />
      <li v-if="item.separator" class="menu-separator" />
    </template>
  </ul>
</template>

<script>
import { mapGetters } from "vuex";
import AppMenuItem from "./AppMenuItem.vue";
import { MAIN_MENUS } from "@/databags/menus";

export default {
  name: "AppMenu",
  components: {
    AppMenuItem,
  },
  data() {
    return {
      menus: [],
      features: [],
      isFetchingMenu: true,
    };
  },
  computed: {
    routeName() {
      return this.$route.name;
    },
    ...mapGetters({
      userRole: "auth/userRole",
      user: "auth/user",
    }),
  },
  watch: {
    user() {
      this.isFetchingMenu = true;
      setTimeout(() => {
        this.features = this.user?.features;
        if (this.features?.length > 0) {
          const readableFeatures = new Set(
            this.features
              .filter((f) => f.permission.can_read && f.is_enable)
              .map((f) => f.name.toLowerCase())
          );

          this.menus = this.filterMenusByFeatures(MAIN_MENUS, readableFeatures);
        } else {
          this.menus = MAIN_MENUS;
        }
        this.isFetchingMenu = false;
      }, 300);
    },
  },
  mounted() {},
  created() {
    this.isFetchingMenu = true;
    setTimeout(() => {
      this.features = this.user?.features;
      if (this.features?.length > 0) {
        const readableFeatures = new Set(
          this.features
            .filter((f) => f.permission.can_read && f.is_enable)
            .map((f) => f.name.toLowerCase())
        );

        this.menus = this.filterMenusByFeatures(MAIN_MENUS, readableFeatures);
      } else {
        this.menus = MAIN_MENUS;
      }
      this.isFetchingMenu = false;
    }, 300);
  },
  methods: {
    matchLabelToFeature(label) {
      const map = {
        courses: "course",
        webinars: "webinar",
        libraries: "library",
        groups: "group",
        badges: "badges",
        categories: "category",
        certifications: "certificate",
        analytics: "analytic",
        "platform features": "feature",
        roles: "role",
        users: "member",
        tags: "tags",
        dashboard: "analytic",
        backups: "media",
        settings: "setting",
      };

      return map[label.toLowerCase()] || label.toLowerCase();
    },
    filterMenusByFeatures(menus, featureSet) {
      const optionalFeatureAlwaysShown = ["settings"];
      return (
        menus
          .map((menu) => {
            const newItems = menu.items.filter((item) => {
              const featName = this.matchLabelToFeature(item.label);
              // add optional features to the returned value
              return (
                featureSet.has(featName) || optionalFeatureAlwaysShown.includes(featName)
              );
            });
            return { ...menu, items: newItems };
          })
          // Keep only menu sections that have items left
          .filter((menu) => menu.items.length > 0)
      );
    },
  },
};
</script>

<style lang="scss" scoped></style>
