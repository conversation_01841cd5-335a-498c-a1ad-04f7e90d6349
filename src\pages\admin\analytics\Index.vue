<template>
  <loader-circle v-if="isFetching" />
  <div v-if="!isFetching" class="px-4 sm:px-0 flex justify-between items-center">
    <div>
      <h3 class="text-base/7 font-semibold text-gray-900">
        {{ $t("Analytics") }}
      </h3>
      <SubHeaderForm :type="'analytics'" />
    </div>
    <div class="flex justify-between"> 
      <div class="w-[50%] mr-8">
        <label for="start_date" class="block text-sm font-medium text-gray-700">
          {{ $t('start_date') }}
        </label>
        <div class="mt-1">
          <date-picker
            v-model:value="start_date"
            class="w-full-important"
            format="YYYY-MM-DD"
            valueType="YYYY-MM-DD"
            type="date"
            @change="handleStartDateChange"
          />
        </div>
      </div>

      <div class="w-[50%]">
        <label for="end_date" class="block text-sm font-medium text-gray-700">
          {{ $t('end_date') }}
        </label>
        <div class="mt-1">
          <date-picker
            v-model:value="end_date"
            class="w-full-important"
            format="YYYY-MM-DD"
            valueType="YYYY-MM-DD"
            type="date"
            :disabledDate="disableEndDates"
          />
        </div>
      </div>
    </div>
  </div>
  <div v-if="!isFetching" class="mt-4 bg-white py-6 rounded-lg">
    <apexchart
      height="500"
      type="line"
      :options="chartOptions"
      :series="series"
    />
  </div>
</template>

<script>
import axios from "axios";
import VueApexCharts from "vue3-apexcharts";
import analyticApi from '@/api/analytic';
import dayjs from 'dayjs';
import DatePicker from 'vue-datepicker-next';
import 'vue-datepicker-next/index.css';

import { groupBy, generateDates } from '@/libraries/helper';
const dayFormat = 'YYYYMMDD';
const defaultDateFrom = -29;
const startDate = dayjs().add(defaultDateFrom, 'day');
const endDate = dayjs();
const dateRanges = generateDates(defaultDateFrom, 'day');

export default {
  name: "HelloWorld",
  components: {
    apexchart: VueApexCharts,
    DatePicker,
  },
  props: {
  },
  data() {
    const today = dayjs();
    return {
      isFetching: false,
      status: "loading",
      chartOptions: {
        dataLabels: {
          enabled: false,
        },
        stroke: {
          curve: 'straight',
        },
				
        yaxis: {
          // forceNiceScale: true,
          decimalsInFloat: 0,
          labels: {
            // formatter: value => value.toFixed(),
          },
        },
        tooltip: {
          x: {
            format: 'dd-MM-yyyy',
          },
          y: {
            formatter: value => value.toFixed(),
          },
        },
        colors: ['#29CB97', '#2E93fA', '#66DA26', '#546E7A', '#E91E63', '#FF9800'],
        chart: {
          id: 'vue-time-chart',
          zoom: {
            enabled: true
          }
        },
        xaxis: {
          type: 'datetime',
          categories: dateRanges, // Should now be timestamps
				
        }
      },
      series: [],
      isGraphGenerated: false,
      isFilterVisible: false,
      startDate,
      endDate,
      dateRanges: this.__duplicateVar(dateRanges),
      start_date: today.subtract(30, 'day').format('YYYY-MM-DD'), // 30 days before today
      end_date: today.format('YYYY-MM-DD'), // Current date and time
    };
  },
  watch: {
    start_date: {
      handler(newStartDate) {
        this.fetchData();
        this.updateDateRange();
      },
      immediate: true,
    },
    end_date: {
      handler(newEndDate) {
        this.fetchData();
        this.updateDateRange();

      },
      immediate: true,
    }
  },
  created() {
    this.fetchData();
  },
  methods: {
    handleStartDateChange(newStartDate) {
      // Ensure end_date is updated if it becomes invalid
      const startDate = dayjs(newStartDate);
      if (dayjs(this.end_date).isBefore(startDate)) {
        this.end_date = startDate.format('YYYY-MM-DD HH:mm');
      }
    },
    disableEndDates(date) {
      // Disable dates in the end date picker before the selected start_date
      if (this.start_date) {
        const startDate = dayjs(this.start_date);
        return dayjs(date).isBefore(startDate);
      }
      return false;
    },
    updateDateRange() {
      const start = dayjs(this.start_date); // Parse start_date
      const end = dayjs(this.end_date); // Parse end_date

      // Calculate the difference in days between start and end
      const diffDays = end.diff(start, 'day');

      // Generate the date ranges using your helper function
      const dateRanges = generateDates(
        diffDays, // Difference as the range
        'day', // Increment type (e.g., 'day', 'month', etc.)
        'YYYY-MM-DD', // Date format
        end // Use end date as the reference date
      );

      // Update x-axis categories in chart options
      this.chartOptions.xaxis.categories = dateRanges;

      // Generate series data dynamically
      const dataMap = {}; // Replace this with your actual data mapping logic
      const seriesData = dateRanges.map(date => [
        dayjs(date).valueOf(), // Convert date to timestamp
        dataMap[date] ?? 0, // Default to 0 if no data
      ]);

      // Update the series dynamically
      this.series = [
        {
          name: 'Total Courses Accessed',
          data: seriesData,
        },
      ];
    },
    async fetchData() {
      this.isFetching = true;
      const callback = (response) => {
        const data = response;
        // Create a map for existing data
        const dataMap = data.reduce((acc, item) => {
          const dateKey = dayjs(item.updated_at).format('YYYY-MM-DD');
          acc[dateKey] = item.total_courses;
          return acc;
        }, {});

        // Generate the series with empty values (0) if data is missing
        const seriesData = dateRanges.map(date => [
          date,
          dataMap[date] ?? 0 // If data exists, use it; otherwise, default to 0
        ]);

        // Final series for ApexCharts
        const series = [{
          name: 'Total Courses Accessed',
          data: seriesData
        }];

        this.series = series;
        this.isFetching = false;
      };
      const errorCallback = (error) => {
        const message = error.response.data.message;
        this.__showNotif('error', this.$t('Error'), message);
        this.isFetching = false;
      };
      const params = {
        start_date: this.start_date,
        end_date: this.end_date,
      };
      analyticApi.getGraphGlobal(params, callback, errorCallback);
    }
  }
};
</script>


<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
h3 {
  margin: 40px 0 0;
}
ul {
  list-style-type: none;
  padding: 0;
}
li {
  display: inline-block;
  margin: 0 10px;
}
a {
  color: #42b983;
}
pre {
  max-height: 200px;
  overflow: scroll;
}
.small {
  max-width: 600px;
  margin: 150px auto;
}
</style>
