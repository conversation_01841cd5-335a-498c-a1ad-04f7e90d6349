
<template>
  <div v-if="!isFetching">
    <!-- Heading -->
    <div class="px-4 sm:px-0 mb-4">
      <h3 class="text-base/7 font-semibold text-gray-900">
        {{ $t("Profile") }}
      </h3>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 md:gap-10">
      <div class="card">
        <form
          class="space-y-3"
          @submit.prevent="submit"
        >
          <div>
            <label
              for="avatar"
              class="block text-sm font-medium text-gray-700"
            >{{ $t('Avatar') }}</label>
            <uploader
              id="fileUploadInput"
              class="mt-2"
              :preview="true"
              :multiple="false"
              :type="'image'"
              :disabled="!isEdit"
              :progressUploadFile="progressUploadFile"
              :existingPreviewImageOrVideo="user.img_url"
              @remove="onRemoveFile"
              @upload="onImportSelected"
            />
          </div>
          <div>
            <label
              for="name"
              class="block text-sm font-medium text-gray-700"
            >{{ $t('Name') }}</label>
            <div class="mt-1">
              <InputText
                v-model="user.full_name"
                :type="`text`"
                :value="user.full_name"
                class="w-full"
                :disabled="!isEdit"
              />
            </div>
          </div>

          <div>
            <label
              for="email"
              class="block text-sm font-medium text-gray-700"
            >{{ $t('Email address') }}</label>
            <div class="mt-1">
              <InputText
                v-model="user.email"
                :type="`email`"
                :value="user.email"
                class="w-full"
                :disabled="!isEdit"
              />
              <span
                v-if="!isValidEmailAddress && user.email && user.email.length !== 0"
                class="text-red-500 text-xs"
              >{{ $t('Invalid Email Address') }}</span>
            </div>
          </div>

          <div>
            <label
              for="phone"
              class="block text-sm font-medium text-gray-700"
            >{{ $t('Mobile Phone') }}</label>
            <div class="mt-1">
              <InputText
                v-model="user.phone"
                :type="`number`"
                :value="user.phone"
                class="w-full"
                :disabled="!isEdit"
              />
            </div>
          </div>
        
          <div>
            <label
              for="location"
              class="block text-sm font-medium text-gray-700"
            >{{ $t('Location') }}</label>
            <div class="mt-1">
              <InputText
                v-model="user.location"
                :value="user.location"
                class="w-full"
                :disabled="!isEdit"
              />
            </div>
          </div>

          <div>
            <label
              for="company"
              class="block text-sm font-medium text-gray-700"
            >{{ $t('Company') }}</label>
            <div class="mt-1">
              <InputText
                v-model="user.company"
                :value="user.company"
                class="w-full"
                :disabled="!isEdit"
              />
            </div>
          </div>  

          <div>
            <label
              for="language"
              class="block text-sm font-medium text-gray-700"
            >{{ $t('Language') }}</label>
            <div class="mt-1">
              <Select
                v-model="user.language"
                :options="userLanguages"
                optionLabel="name"
                optionValue="id"
                :placeholder="$t('Select a Language')"
                class="w-full"
                :disabled="!isEdit"
              />
            </div>
          </div>

          <div>
            <label
              for="short_bio"
              class="block text-sm font-medium text-gray-700"
            >{{ $t('Bio') }}</label>
            <div class="mt-1">
              <Textarea
                v-model="user.short_bio"
                rows="3"
                cols="30"
                class="w-full"
                :disabled="!isEdit"
              />
            </div>
          </div>


          <div class="mt-5 flex justify-end">
            <Button
              v-if="!isEdit"
              :disabled="isSaving"
              type="button"
              :label="$t('Edit Profile')"
              class="mr-2"
              @click="isEdit = true"
            />
            <Button
              v-if="isEdit"
              :disabled="isSaving"
              outlined
              type="button"
              :label="$t('Cancel')"
              class="mr-2"
              @click="isEdit = false"
            />
            <Button
              v-if="isEdit"
              type="button"
              :loading="isSaving"
              :disabled="isSaving || !isFormValid"
              :label="$t('Save Changes')"
              @click="submit"
            />
          </div>
        </form>
      </div>

      <!-- Member Only -->
      <div class="space-y-6">
        <profile-badges v-if="badgesFeature.is_enable && badgesPermission.can_read" />
		
        <profile-certificates v-if="certificateFeature.is_enable && certificatePermission.can_read" />
      </div>
    </div>
  </div>
</template>

<script>
import { USER_DEFAULT, USER_LANGUAGES } from '@/databags/user';
import 'vue-multiselect/dist/vue-multiselect.css';
import authApi from '@/api/auth';
import { mapGetters, mapActions } from 'vuex';
import ProfileBadges from '@/components/profile/ProfileBadges.vue';
import ProfileCertificates from '@/components/profile/ProfileCertificates.vue';
import Uploader from '@/components/global/Uploader.vue';
import fileApi from '@/api/files';
export default {
  components: {
    ProfileBadges,
    ProfileCertificates,
    Uploader
  },
  props: {
  },
  data() {
    return {
      user: USER_DEFAULT,
      isSaving: false,
      userLanguages: this.__duplicateVar(USER_LANGUAGES),
      isFetching: false,
      isEdit: false,
      progressUploadFile: 0,
      isUploadFile: false
    };
  },
  computed: {
    ...mapGetters({
      userProfile: 'auth/user',
      userRole: 'auth/userRole',
      userFeatures: "auth/userFeatures",
      userRole: "auth/userRole",
    }),
    badgesFeature() {
      return this.userFeatures.find((item) => item.name === "badges");
    },
    badgesPermission() {
      return this.badgesFeature?.permission ? this.badgesFeature.permission : {};
    },
    certificateFeature() {
      return this.userFeatures.find((item) => item.name === "certificate");
    },
    certificatePermission() {
      return this.certificateFeature?.permission ? this.certificateFeature.permission : {};
    },
    isFormValid() {
      return (
        this.isValidEmailAddress
				&& this.user.full_name
				&& this.user.role_id
      );
    },
    isValidEmailAddress() {
      return true;
    },
    params() {
      const params = {
        email: this.user.email,
        password: this.user.password,
        full_name: this.user.full_name,
        phone: this.user.phone,
        role_id: this.user.role_id,
        language: this.user.language,
        img_url: this.__getDynamicPath(this.user.img_url),
        job_title: this.user.job_title,
        company: this.user.company,
        location: this.user.location,
        short_bio: this.user.short_bio,
        endpoint: this.right,
      };
      return params;
    },
  },
  watch: {},
  created() {
    this.fetch();
  },
  mounted() {},
  beforeUnmount() {},
  methods: {
    ...mapActions({
      updateSiteLocale: "auth/updateSiteLocale",
      fetchUser: "auth/fetchUser",
    }),
    onRemoveFile() {
      this.user.img_url = null;
      this.progressUploadFile = 0;
    },
    onImportSelected(event) {
      return new Promise((resolve, reject) => {
        const files = event;
        this.isUploadFile = true;
        if (files.length > 0) {
          // Check file size - add 5MB limit
          const maxSizeInBytes = 5 * 1024 * 1024; // 5MB
          if (files[0].size > maxSizeInBytes) {
            this.__showNotif(
              "warning",
              this.$t("Upload File"),
              this.$t(`File size exceeds ${maxSizeInBytes}MB limit`)
            );
            this.isUploadFile = false;
            if (document.querySelector("#fileUploadInput"))
              document.querySelector("#fileUploadInput").value = "";
            return;
          }
      
          if (
            files[0].type === "image/jpeg" ||
            files[0].type === "image/gif" ||
            files[0].type === "image/png" ||
            files[0].type === "image/jpg" ||
            files[0].type === "image/webp"
          ) {
            // this.isUploadingLottie = true;
            const file = files[0];
            if (!file) {
              this.__showNotif(
                "warning",
                this.$t("Upload File"),
                this.$t("Sorry, currently we can't upload the file")
              );
              return;
            }
            const params = new FormData();
            params.append("file", file);
            params.append("folder", "BadgeIcons");
            const callback = (response) => {
              this.user.img_url = response.fullPath;
              this.isUploadFile = false;
              resolve(response); // Resolve the promise with the response
            };

            if (document.querySelector("#fileUploadInput"))
              document.querySelector("#fileUploadInput").value = "";
            const errorCallback = (error) => {
              // this.isUploadingLottie = false;
              this.__showNotif(
                "warning",
                this.$t("Upload File"),
                this.$t("Sorry, currently we can't upload the file")
              );
              this.isUploadFile = false;
              reject(error); // Reject the promise with the error
            };

            const progressCallback = (progress) => {
              this.progressUploadFile = progress;
            };
            fileApi.upload(params, callback, errorCallback, progressCallback);
          } else {
            // this.isTemplateReady = false;
            this.__showNotif(
              "warning", 
              this.$t("Upload File"), 
              this.$t("Unsupported file")
            );
            // eslint-disable-next-line
        if (document.querySelector("#fileUploadInput"))
              document.querySelector("#fileUploadInput").value = "";
            return;
          }
        }
      });
    },
    fetch() {
      this.isFetching = true;
      const callback = (response) => {
        const data = response.data;
        this.user = data;
        this.isFetching = false;
      };
      const errorCallback = (error) => {
        if (error.message.includes('403')) {
          localStorage.clear();
        }
        const message = error.response.data.message;
        this.__showNotif('error', this.$t('Error'), message);
        this.isFetching = false;
      };
      authApi.getProfile(callback, errorCallback);
    },
    updateLanguage(lang) {
      let locale = lang || 'en';
      try {
        // Save to localStorage if available
        if (locale) {
          localStorage.setItem("locale", locale);
          this.$i18n.locale = locale;
        }
      } catch (e) {
        console.error(e);
        const message = e.response?.data?.message || "Failed to fetch favicon.";
        this.__showNotif("error", this.$t('Error'), message);
      }
      this.updateSiteLocale(locale);
    },
    submit() {
      const params = this.params;
      this.isSaving = true;
      this.updateLanguage(params.language);
      const callback = (response) => {
        this.isSaving = false;
        this.isEdit = false;
        const message = response.message;
        this.fetchUser();
        this.__showNotif('success', this.$t('User'), message);
      };
      const errorCallback = (error) => {
        const message = error.response.data.message;
        this.__showNotif('error', this.$t('Error'), message);
        this.isEdit = false;
        this.isSaving = false;
      };
      authApi.update(params, callback, errorCallback);
    },
  },
};
</script>