<template>
  <Dialog
    :visible="isVisible"
    modal
    :header="alreadyExistMember.length === 0 ? $t('Upload Members') : $t('Imports interrupted due to duplicates')"
    :style="{ width: '50rem' }"
    @update:visible="isVisible = $event"
  >
    <div v-if="!totalMembers && alreadyExistMember.length === 0" class="pointer text-primary-600">
      <a
        href="./../../example-file/DEFAULT_FORMAT_USERS_UPLOAD_NEW_2.xlsx"
        download
      >{{ $t('Download Template file') }}</a>
    </div>

    <div v-if="!totalMembers && alreadyExistMember.length === 0" class="mt-6">
      {{ $t('Please do not change the format columns Please remember, please do not change') }}
    </div>

    <div v-if="membersArray.length === 0 && alreadyExistMember.length === 0" class="flex my-4 items-center">
      <uploader
        id="fileUploadMedia"
        class="mt-2"
        :preview="true"
        :multiple="false"
        :type="'excel'"
        :progressUploadFile="progressUploadFile"
        @upload="onImportSelected"
        @remove="onRemoveFile"
      />
    </div>

    <div v-if="membersArray.length > 0 && alreadyExistMember.length === 0" class="mb-4">
      <div class="flex justify-between">
        <div class="font-bold mb-2">{{ $t('Preview Data') }}</div>
        <div v-if="totalMembers" class="font-bold mb-2 text-primary">
          {{ $t('Total Members') }}: {{ totalMembers }}
        </div>
      </div>
      <div class="overflow-x-auto overflow-y-auto max-h-[500px]">
        <table class="w-full border-collapse border border-slate-400">
          <thead style="position: sticky; top: 0; background-color: white; z-index: 1;">
            <tr>
              <th v-for="(header, key) in Object.keys(membersArray[0] || {})" :key="key" class="border border-slate-300 p-2 bg-slate-100 text-left">{{ formatHeader(header) }}</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(member, index) in membersArray" :key="index">
              <td v-for="(value, key) in member" :key="key" class="border border-slate-300 p-2">{{ value }}</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="text-gray-500 text-sm mt-4">
        {{ $t('Confirm that the preview is confirmed and that all  users imported will be sent an access invitation to gain access to the platform') }}
      </div>
    </div>

    <div v-if="alreadyExistMember.length > 0" class="mb-4">
      <p>{{ $t('The following email(s) already exist in the system and were not imported') }}:</p>
      <ul style="margin-top: 0.5rem; margin-bottom: 1rem; padding-left: 1rem; list-style: auto">
        <li v-for="(email, index) in alreadyExistMember" :key="index">
          {{ email }}
        </li>
      </ul>
      <p>{{ $t('Please look over the data and make sure to remove duplicates before trying again') }}.</p>
    </div>

    <div v-if="alreadyExistMember.length === 0" class="flex justify-end mt-6">
      <Button
        :disabled="isUploading"
        outlined
        type="button"
        :label="$t('Cancel')"
        class="mr-2"
        @click="close"
      />
      <Button
        type="button"
        :loading="isUploading"
        :disabled="isUploading || totalMembers === 0"
        :label="$t('Import users')"
        @click="bulkCreate()"
      />
    </div>

    <div v-if="alreadyExistMember.length > 0" class="flex justify-end">
      <Button
        :disabled="isUploading"
        type="button"
        label="OK"
        class="mr-2"
        @click="close"
      />
    </div>
  </Dialog>
</template>

<script>
import XLSX from 'xlsx';
import userApi from '@/api/user';
import Uploader from '@/components/global/Uploader.vue';

export default {
  components: {
    Uploader
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    item: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      progressUploadFile: 0,
      isUploading: false,
      totalMembers: 0,
      membersArray: [],
      alreadyExistMember: [],
    };
  },
  computed: {
    isVisible: {
      get() {
        return this.visible;
      },
      set(value) {
        this.$emit('update:visible', value);
      }
    },

    isFormValid() {
      return (
        this.question.content
      );
    },
  },
  watch: {
    visible() {
      this.totalMembers = 0;
      this.membersArray = [];
      this.progressUploadFile = 0;
    },
  },
  methods: {
    close() {
      this.$emit('onClose');
      this.membersArray = [];
      this.progressUploadFile = 0;
      this.isUploading = false;
      this.totalMembers = 0;
      this.alreadyExistMember = [];
    },
    submit() {
      if (this.isEdit) this.update();
      else this.save();
    },
    onRemoveFile() {
      this.totalMembers = 0;
      this.membersArray = [];
      this.progressUploadFile = 0;
    },
    onImportSelected(e) {
      const files = e;
      if (files.length > 0) {
        if (files[0].name.includes('.xlsx') || files[0].name.includes('.xls')) {
          const file = files[0];
          const reader = new FileReader();
          reader.onload = (e) => {
            const data = e.target.result;
            this.progressUploadFile = 0;
            try {
              const workbook = XLSX.read(data, { type: 'binary' });
              workbook.SheetNames.forEach((sheetName) => {
                // Convert sheet to row objects
                const XlRowObject = XLSX.utils.sheet_to_row_object_array(workbook.Sheets[sheetName]);

                // Validate and filter data
                const validatedData = [];
                const errors = [];
                const requiredColumns = ['first_name', 'email']; // 'last_name' is no longer strictly required

                XlRowObject.forEach((row, index) => {
                  // Check if all *strictly* required columns are present
                  const missingColumns = requiredColumns.filter((col) => !(col in row));
                  if (missingColumns.length > 0) {
                    errors.push(`Row ${index + 2}: Missing columns - ${missingColumns.join(', ')}`);
                    return;
                  }

                  // Validate individual fields
                  const isFirstNameValid = typeof row.first_name === 'string' && row.first_name.trim() !== '';
                  const isEmailValid = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(row.email);
                  const isLastNameValid = typeof row.last_name === 'string'; // Last name can be any string (including empty) or undefined

                  if (isFirstNameValid && isEmailValid && isLastNameValid) {
                    validatedData.push({
                      first_name: row.first_name,
                      last_name: row.last_name ? row.last_name.trim() : '', // Trim if exists, otherwise empty string
                      email: row.email,
                      job_title: row.job_title,
                      password: row.password
                    }); // Add valid row with first_name, last_name, and email
                  } else {
                    errors.push(`Row ${index + 2}: Invalid data`);
                  }
                });

                // Log errors or proceed
                if (errors.length > 0) {
                  console.error('Validation Errors:', errors);
                  this.__showNotif('error', this.$t('Error'), this.$t('Validation errors found. Check the console for details.'));
                  this.progressUploadFile = 0;
                  this.totalMembers = 0; // Reset total members on error
                  this.membersArray = []; // Reset preview data on error
                } else {
                  this.totalMembers = validatedData.length;
                  this.membersArray = validatedData;
                  this.progressUploadFile = 100;
                }
              });
            } catch (error) {
              this.__showNotif('error', this.$t('Error'), this.$t('File Corrupt, cannot read data'));
              this.progressUploadFile = 0;
              this.totalMembers = 0; // Reset total members on error
              this.membersArray = []; // Reset preview data on error
            }
          };
          reader.onerror = (ex) => {
            console.error('File reading error:', ex);
            this.progressUploadFile = 0;
            this.totalMembers = 0; // Reset total members on error
            this.membersArray = []; // Reset preview data on error
          };
          reader.readAsBinaryString(file);
        } else {
          this.__showNotif('warning', 'Warning', this.$t('Unsupported File'));
        }
      }
    },

    bulkCreate() {
      this.isUploading = true;
      const callback = (response) => {
        const item = response.data;
        const itemExist = response.already_exist;
        this.isUploading = false;
        const message = response.message;
        this.membersArray = [];
        this.totalMembers = 0; // Reset total members after successful upload
        this.__showNotif('success', this.$t('User'), message);
        this.$emit('onCreate', item);
        this.close();
      };
      const errorCallback = (error) => {
        const message = error.response.data.message;
        this.alreadyExistMember = error.response.data.data;
        this.__showNotif('error', this.$t('Error'), message);
        this.isUploading = false;
      };
      const ids = this.membersArray;
      const types = {};
      if (this.$route.params.group_id) types.group_id = this.$route.params.group_id;
      if (this.$route.params.course_id) types.course_id = this.$route.params.course_id;
      if (this.item?.id) types.group_id = this.item.id;
      userApi.bulkCreate(ids, types, callback, errorCallback);
    },

    formatHeader(header) {
      if (!header) return '';
      return header
        .replace(/_/g, ' ') // Replace underscores with spaces
        .replace(/\b\w/g, (match) => match.toUpperCase()); // Capitalize the first letter of each word
    },
  },
};
</script>

<style scoped>
/* Basic styling for the preview table */
.overflow-x-auto {
  overflow-x: auto;
}

.table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 1rem;
}

.table th,
.table td {
  border: 1px solid #ccc;
  padding: 0.5rem;
  text-align: left;
}

.table th {
  background-color: #f0f0f0;
  font-weight: bold;
}
</style>