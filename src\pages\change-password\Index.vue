
<template>
  <div>
    <!-- Heading -->
    <div class="px-4 sm:px-0 mb-4">
      <h3 class="text-base/7 font-semibold text-gray-900">
        {{ $t("Change Password") }}
      </h3>
    </div>

    <div class="max-w-xl">
      <div class="card">
        <form
          class="space-y-3"
          @submit.prevent="submit"
        >
          <div>
            <label
              for="password"
              class="block text-sm font-medium text-gray-700"
            >{{ $t('Current Password') }}</label>
            <div class="mt-1">
              <t-input
                v-model="oldPassword"
                :dataTest="'oldPassword'"
                :type="`password`"
                :value="oldPassword"
                class="w-full"
              />
            </div>
          </div>

          <div>
            <label
              for="password"
              class="block text-sm font-medium text-gray-700"
            >{{ $t('New Password') }}</label>
            <div class="mt-1">
              <t-input
                v-model="newPassword"
                :dataTest="'newPassword'"
                :type="`password`"
                :value="newPassword"
                class="w-full"
              />
            </div>
          </div>

          <div>
            <label
              for="password"
              class="block text-sm font-medium text-gray-700"
            >{{ $t('New Password Confirmation') }}</label>
            <div class="mt-1">
              <t-input
                v-model="newPasswordConfirmation"
                :dataTest="'newPasswordConfirmation'"
                :type="`password`"
                :value="newPasswordConfirmation"
                class="w-full"
              />
              <p
                v-if="newPassword && newPasswordConfirmation && newPassword !== newPasswordConfirmation"
                class="mt-1 text-sm text-red-600"
              >
                {{ $t('Passwords do not match') }}
              </p>
            </div>
          </div>
          

          <div class="mt-5 flex justify-end">
            <Button
              type="button"
              :loading="isSaving"
              :disabled="isSaving || !isFormValid"
              :label="$t('Change Password')"
              @click="submit"
            />
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import TInput from "@/components/form/Input.vue";
import authApi from '@/api/auth';
export default {
  components: {
    TInput
  },
  props: {
  },
  data() {
    return {
      oldPassword: '',
      newPassword: '',
      newPasswordConfirmation: '',
      isSaving: false,
      isEdit: false,
    };
  },
  computed: {
    isFormValid() {
      return (
        this.oldPassword &&
      this.newPassword &&
      this.newPasswordConfirmation &&
      this.newPassword === this.newPasswordConfirmation
      );
    },
  },
  watch: {},
  created() {},
  mounted() {},
  beforeUnmount() {},
  methods: {
    resetForm() {
      this.oldPassword = '';
      this.newPassword = '';
      this.newPasswordConfirmation = '';
    },
    submit() {
      const params = {
        old_password: this.oldPassword,
        new_password: this.newPassword,
        confirm_password: this.newPasswordConfirmation,
      };
            
      this.isSaving = true;
      const callback = (response) => {
        const item = response.data;
        this.isSaving = false;
        this.isEdit = false;
        const message = response.message;
        this.__showNotif('success', this.$t('User'), message);
        this.resetForm();
      };
      const errorCallback = (error) => {
        const message = error.response.data.message;
        this.__showNotif('error', this.$t('Error'), message);
        this.isEdit = false;
        this.isSaving = false;
      };
      authApi.changePassword(params, callback, errorCallback);
    },
  },
};
</script>