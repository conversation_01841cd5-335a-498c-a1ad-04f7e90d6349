.vuecal--no-time .vuecal__event {
  min-height: 60px !important;
}

.vuecal--no-time .vuecal__event-title {
  min-height: 60px !important;
}

.vuecal__time-column .vuecal__time-cell {
  text-align: center!important;
  padding: 0!important;
}
.vuecal {
  border: solid 1px #595959!important;
  border-top-left-radius: 0.75rem!important;
  border-top-right-radius: 0.75rem!important;
  border: 1px solid #DDDDDD;
  .vuecal__menu{
    border-top-left-radius: 0.75rem!important;
    border-top-right-radius: 0.75rem!important;
  }

  .vuecal__event {
    cursor: pointer;
    padding: 10px;
  }

  .vuecal__bg {
    background-color: white!important;
  }

  .vuecal__event-title {
    display: flex; /* Use flexbox for centering */
    align-items: center; /* Center vertically */
    justify-content: center; /* Center horizontally */
    height: 100%; /* Make sure it takes the full height of its container */
    max-height: 100%; /* Ensure it doesn't exceed the parent's height */
    line-height: 1; /* Adjust line height for better spacing */
    color: black;
    text-align: center; /* Center text horizontally */
  }
  
  
  
  .vuecal__event-time {
      display: inline-block;
      margin-bottom: 12px;
      padding-bottom: 12px;
      border-bottom: 1px solid rgba(0, 0, 0, 0.2);
  }

  .vuecal__event-content {
      font-style: italic;
  }

  // .vuecal__title-bar{background-color: white!important; color: black!important;}
  .vuecal__menu {background: #3887FF;}
  .vuecal__view-btn {color: white}
  // .vuecal__title button {color: white!important;}
  // .vuecal__arrow {color: white!important;}
  // .vuecal__today-btn {color: white!important;}
  .vuecal__cell--today, .vuecal__cell--current {background-color: #3887FF0e;}
  .vuecal__cell--selected:before {border-color: #3887FF; margin-right: -1px!important;}
  
  .vuecal__cell--selected {background-color: #3887FF0e;}
  .vuecal__cell-events-count {
    background-color: #3887FF; color: white;
    height: 24px;
    width: 24px;
    border-radius: 24px;
    font-size: 18px;
    padding-top: 6px;
  }
  .vuecal__cell-events  {
      padding: 0 4px 0 4px;
  }
  .vuecal__flex[column] {
      min-height: 100px;
  }

  .vuecal__event-title {margin: 4px 0px 0px 4px}
  .vuecal__event-content {font-size: 13px; font-weight: 200;}
  .vuecal__event-time{display: none; padding-top: 0px; border-bottom: none}
  
  .vuecal__cell-date {padding: 1.5em;}
  .vuecal__cell--out-of-scope {
      color: #595959;
  }
  
  .vuecal__cell-date {
      position: absolute;
      top:0;
      right: 0;
      padding: 10px;
  }

  .vuecal__cell--disabled {text-decoration: line-through;}
  

  .vuecal__now-line {display: none}
  // .vuecal__menu, .vuecal__cell-events-count {background-color: #42b983;}
  // .vuecal:not(.vuecal--day-view) .vuecal__cell--selected {background-color: rgba(235, 255, 245, 0.4);}
  /* Cells and buttons get highlighted when an event is dragged over it. */
  // .vuecal__cell--highlighted:not(.vuecal__cell--has-splits),
  // .vuecal__cell-split--highlighted {background-color: rgba(195, 255, 225, 0.5);}
  // .vuecal__arrow.vuecal__arrow--highlighted,
  // .vuecal__view-btn.vuecal__view-btn--highlighted {background-color: rgba(136, 236, 191, 0.25);}
}

.container-day-detail {
	height: 500px;
	overflow-y: auto;
}

.day-event-detail {
  color: black!important;
	width: 100%;
	height: 20px;
	height: 90px;
	margin-bottom: 6px;
	border-radius: 2px;
	padding: 10px;
	margin-top: 2px;
	color: white;

	.title {
		font-size: 16px;
		// max-width: 350px; white-space: nowrap; overflow: hidden; overflow: hidden; text-overflow: ellipsis;
	}

	&:hover {
		color: white;
	}
}

.vuecal .p-button {
  color: #334155!important;
}

.icon-detail-day {
	position: absolute;
	margin-top: 30px;
	right: 10px;
	top: 10px;
	z-index: 9999;
}

.vuecal {
  border: none!important;
}
