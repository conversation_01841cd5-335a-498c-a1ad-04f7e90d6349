/* Utils */
.clearfix:after {
    content: ' ';
    display: block;
    clear: both;
}

.card {
  background: var(--surface-card);
  padding: 2rem;
  margin-bottom: 2rem;
  border-radius: var(--content-border-radius);

  &:last-child {
    margin-bottom: 0;
  }

  /* Media query for mobile devices (max-width: 767px is a common breakpoint) */
  @media (max-width: 767px) {
    padding: 1rem; /* Reduce padding to 1rem on mobile */
  }
}
.p-toast {
    &.p-toast-top-right,
    &.p-toast-top-left,
    &.p-toast-top-center {
        top: 100px;
    }
}

.stat-number {
  font-size: 2rem;
  font-weight: bold;

  &-lg {
    font-size: 2.25rem;
  }
}

.stat-info {
  font-size: 75%;
}

.stat-box {
  position: relative;
  min-width: 125px;

  &-arrow {
    position: absolute;
    top: 1rem;
    right: 0;
  }

  &-info {
    font-size: 0.875rem;
    color: gray;
  }
}

.card-stat {
  min-height: 120px;
}

.text-primary {
  color: #3b82f6!important;
}
.card-profile {
  background: var(--surface-card);
  padding: 2rem;
  margin-bottom: 0rem;
  border-radius: var(--content-border-radius);

  &:last-child {
      margin-bottom: 0;
  }
}

.dp__cell_inner {
  max-height: 28px!important;
  max-height: 28px!important;
}