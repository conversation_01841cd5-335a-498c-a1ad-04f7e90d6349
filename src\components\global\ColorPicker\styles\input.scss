.inputs-controls {
  display: flex;
  font-size: 16px;
  margin-bottom: 5px;

  .formatBtn {
    position: relative;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    padding: 1px;
    border: 0;
    text-align: center;
    cursor: pointer;
    background-color: transparent;
    font-weight: 700;
    outline: none;
    margin-right: 5px;

    &:hover {
      color: #1a3aff;
    }
  }

  .format-group {
    display: flex;
    flex-grow: 1;

    input {
      padding: 5px;
      margin: 0 3px;
      min-width: 0;
      text-align: center;
      border-width: 0 0 1px 0;
      -webkit-appearance: none;
      appearance: none;
      -moz-appearance: textfield;
      outline: none;
      flex: 1;

      &::-webkit-outer-spin-button,
      &::-webkit-inner-spin-button {
        -webkit-appearance: none !important;
        margin: 0;
      }
    }
  }
}

