export default {
  namespaced: true,
  plugins: [],
  state: () => ({
    course: null,
    lessons: [],
    selectedLesson: null,
    selectedLessonItem: null,
    isQuizRunning: false,
    isQuizDone: false,
    isQuizAnswer: false,
    isQuizSolution: false,
    selectedQuizType: null,
    courseLessonVideo: null,
  }),
  mutations: {
    setCourse(state, course) {
      state.course = course;
    },
    setLessons(state, lessons) {
      state.lessons = lessons;
    },
    appendLessons(state, lessons) {
      for (let index = 0; index < lessons.length; index++) {
        const item = lessons[index];
        state.lessons.push(item);
      }
    },
    setSelectedLesson(state, selectedLesson) {
      state.selectedLesson = selectedLesson;
    },
    setSelectedLessonItem(state, selectedLessonItem) {
      state.selectedLessonItem = selectedLessonItem;
    },
    setQuizRunning(state, isQuizRunning) {
      state.isQuizRunning = isQuizRunning;
    },
    setQuizDone(state, isQuizDone) {
      state.isQuizDone = isQuizDone;
    },
    setIsUserInputTheAnswer(state, isQuizAnswer) {
      state.isQuizAnswer = isQuizAnswer;
    },
    setQuizSolution(state, isQuizSolution) {
      state.isQuizSolution = isQuizSolution;
    },
    setQuizType(state, selectedQuizType) {
      state.selectedQuizType = selectedQuizType;
    },
    setVideoPauseHistory(state, courseLessonVideo) {
      state.courseLessonVideo = courseLessonVideo;
    },
    resetState(state) {
      state.course = null;
      state.lessons = [];
      state.selectedLesson = null;
      state.selectedLessonItem = null;
      state.isQuizRunning = false;
      state.isQuizDone= false;
      state.isQuizAnswer = false;
      state.isQuizSolution = false;
      state.selectedQuizType = null;
      state.courseLessonVideo = null;
    },
  },
  actions: {
    setCourse({ commit }, course) {
      commit("setCourse", course);
    },
    setLessons({ commit }, lessons) {
      commit("setLessons", lessons);
    },
    appendLessons({ commit }, lessons) {
      commit("appendLessons", lessons);
    },
    setSelectedLesson({ commit }, selectedLesson) {
      commit("setSelectedLesson", selectedLesson);
    },
    setSelectedLessonItem({ commit }, selectedLessonItem) {
      commit("setSelectedLessonItem", selectedLessonItem);
    },
    setQuizRunning({ commit }, isQuizRunning) {
      commit("setQuizRunning", isQuizRunning);
    },
    setQuizDone({ commit }, isQuizDone) {
      commit("setQuizDone", isQuizDone);
    },
    setIsUserInputTheAnswer({ commit }, isQuizAnswer) {
      commit("setIsUserInputTheAnswer", isQuizAnswer);
    },
    setQuizSolution({ commit }, isQuizSolution) {
      commit("setQuizSolution", isQuizSolution);
    },
    setQuizType({ commit }, selectedQuizType) {
      commit("setQuizType", selectedQuizType);
    },
    setVideoPauseHistory({ commit }, courseLessonVideo) {
      commit("setVideoPauseHistory", courseLessonVideo);
    },
    resetState({ commit }) {
      commit("resetState");
    },
  },
  getters: {
    getCourse(state) {
      return state.course;
    },
    getLessons(state) {
      return state.lessons;
    },
    getAllLessonItems(state) {
      // const items = [];
      // for (let lessonIndex = 0; lessonIndex < state.lessons.length; lessonIndex++) {
      // 	const lesson = state.lessons[lessonIndex];

      // 	// Add lesson items
      // 	for (let lessonItemIndex = 0; lessonItemIndex < lesson.items.length; lessonItemIndex++) {
      // 		const lessonItem = lesson.items[lessonItemIndex];
      // 		items.push(lessonItem);
      // 	}

      // 	// Add quiz
      // 	if (lesson.quiz && lesson.quiz.is_active) {
      // 		items.push(lesson.quiz);
      // 	}
      // }

      // return items;

      return state.lessons.flatMap(lesson => [
        ...(lesson.items || []),
        ...(lesson.quiz?.is_active ? [lesson.quiz] : [])
      ]);
    },
    getSelectedLesson(state) {
      return state.selectedLesson;
    },
    getSelectedLessonItem(state) {
      return state.selectedLessonItem;
    },
    getQuizRunning(state) {
      return state.isQuizRunning;
    },
    getQuizDone(state) {
      return state.isQuizDone;
    },
    getQuizAnswer(state) {
      return state.isQuizAnswer;
    },
    getQuizSolution(state) {
      return state.isQuizSolution;
    },
    getQuizType(state) {
      return state.selectedQuizType;
    },
    getVideoPauseHistory(state) {
      return state.courseLessonVideo;
    },
  },
};