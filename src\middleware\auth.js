

export default ({ store, next, from, to }) => {
  const { path, name} = to;
  const query = to.query;
  if (query.hasOwnProperty("mode")) {
    var mode = query.mode;
    if (mode == "preview") {
      var token  = query.hasOwnProperty("token") ? query.token : "";
      var domain = query.hasOwnProperty("domain") ? query.domain : "";

      store.dispatch("app/preflightPreview", {
        token: token, domain: domain
      });
      return next('/login');
    }
  }

  // http://localhost:3000/#/?token=48072824D4413C2A29844F567BB7C6AE    
  if (query.hasOwnProperty("token")) {
    let token = query.token;
    store.dispatch("auth/setToken", token);

    // Then remove the query.
    // eslint-disable-next-line no-param-reassign
    delete to.query.token;
    return next(to);
  }

  // Has session
  const hasSession = store.getters['auth/hasSession'];

  // Allowed paths without login
  const allowedPaths = [
    '/login',
    '/logout',
    '/forgot-password',
    '/password/reset',
    '/register',
    '/magic-link',
    '/change-password',
    '/auto_login',
    '/user/verify',
    '/confirmation-email',
    '/resend-email',
    '/not-found',
    '/reset',
		
  ];
  const allowedPathName= [
    'WebinarsDetail',
    'CoursePreview'
  ];
  // Rules
  if (allowedPaths.includes(path) || allowedPathName.includes(name) ) {
    return next();
  } else if (!hasSession && path === '/') {
    return next('/login');
  } else if (!hasSession && !allowedPaths.includes(path)) {
    return next('/login');
  } else if (!hasSession) {
    return next('/login');
  }

  return next();
};