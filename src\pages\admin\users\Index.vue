<template>
  <div>
    <!-- Heading -->
    <div class="px-4 sm:px-0 mb-4">
      <div class="flex items-center justify-between">
        <div>
          <h3 class="text-base/7 font-semibold text-gray-900">
            {{ $t("Users") }}
          </h3>
          <SubHeaderForm :type="'users'" />
        </div>
        <div class="flex">
          <IconField>
            <InputIcon>
              <i class="pi pi-search" />
            </InputIcon>
            <InputText
              v-model="keyword"
              :placeholder="$t('Type in search term')"
              class="min-w-[246px]"
            />
            <!-- Clear Icon -->
            <InputIcon v-if="keyword" @click="keyword = ''">
              <i class="pointer pi pi-times" />
            </InputIcon>
          </IconField>
        </div>
      </div>
    </div>

    <div class="card">
      <div class="mb-4">
        <Button
          v-if="checkPermissionCanWrite(activeRightTab)"
          type="button"
          :label="`${$t('Create')} ${$t(formattedLabel)}`"
          class="mr-2"
          @click="showCreate()"
        />
        <Button
          v-if="checkPermissionCanWrite(activeRightTab) && activeRightTab === 'member'"
          type="button"
          :label="`${$t('Bulk Import')}`"
          class="mr-2"
          @click="showImport()"
        />
        <Button
          v-if="checkPermissionCanWrite(activeRightTab) && activeRightTab === 'member'"
          type="button"
          :label="`${$t('Export list of all users')}`"
          class="mr-2"
          @click="exportMember()"
        />
      </div>

      <Tabs v-model:value="activeRightTab" class="mb-4" value="0">
        <TabList>
          <div class="flex justify-between">
            <div>
              <Tab v-for="role in filteredRightDefault" :key="role.id" :value="role.id">
                {{ $t(role.name) }}
              </Tab>
            </div>
          </div>
        </TabList>
      </Tabs>
      <loader-circle v-if="isFetching" />
      <DataTable
        v-if="!isFetching"
        ref="myTable"
        :value="items"
        tableStyle="min-width: 50rem"
        @update:sortOrder="updateSortOrder"
        @update:sortField="updatesortField"
        @row-click="editItem"
      >
        <template #empty> No users found. </template>
        <template v-if="!isReset">
          <Column :header="$t('No.')" headerStyle="width:3rem" style="width: 5%">
            <template #body="slotProps">
              {{ slotProps.index + 1 + (currentPage - 1) * limit }}.
            </template>
          </Column>
          <Column sortable field="full_name" :header="$t('Name')" style="width: 25%" />
          <Column sortable field="email" :header="$t('Email')" style="width: 25%" />
          <Column field="roles" :header="$t('Roles')" style="width: 15%">
            <template #body="slotProps">
              {{ $t(slotProps.data.role.name) }}
            </template>
          </Column>
          <Column sortable field="created_at" :header="$t('Date added')" style="width: 12%">
            <template #body="slotProps">
              {{ __dateAddedFormatISO(slotProps.data.created_at) }}
            </template>
          </Column>
          <Column
            v-if="
              checkPermissionCanWrite(activeRightTab) ||
                checkPermissionCanDelete(activeRightTab)
            "
            :header="$t('Actions')"
            style="width: 10%"
          >
            <template #body="slotProps">
              <div class="flex justify-content-left">
                <Button
                  v-if="checkPermissionCanWrite(activeRightTab)"
                  icon="pi pi-pencil"
                  variant="text"
                  aria-label="Edit"
                  class="mr-2"
                  @click="editItem(slotProps)"
                />
                <Button
                  v-if="checkPermissionCanDelete(activeRightTab)"
                  icon="pi pi-trash"
                  severity="danger"
                  variant="text"
                  aria-label="Delete"
                  @click="deleteItem(slotProps.data)"
                />
              </div>
            </template>
          </Column>
        </template>
      </DataTable>
      <div v-if="!isFetching && items.length > 0" class="grid grid-cols-3 px-0 mx-0 mt-8">
        <div />
        <div>
          <Paginator
            :rows="limit"
            :first="(currentPage - 1) * limit"
            :totalRecords="total"
            :rowsPerPageOptions="[10, 20, 30]"
            template="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport"
            :currentPageReportTemplate="`({first} ${$t('to')} {last} ${$t('of')} {totalRecords})`"
            class="mt-8"
            @page="pageUpdate"
          />
        </div>
        <div class="flex items-center w-full justify-end px-0 mx-0">
          <div class="pt-7">
            {{ $t("Record(s) per page") }}
          </div>
          <div>
            <Paginator
              :rows="limit"
              :first="(currentPage - 1) * limit"
              :totalRecords="total"
              :rowsPerPageOptions="[10, 20, 30]"
              template="RowsPerPageDropdown"
              :currentPageReportTemplate="`({first} ${$t('to')} {last} ${$t('of')} {totalRecords})`"
              class="mt-8 pr-0 mr-0"
              @page="pageUpdate"
            />
          </div>
        </div>
      </div>
    </div>
    <ModalForm
      v-model:visible="isModalVisible"
      :roles="roles"
      :right="activeRightTab"
      :item="selectedItem"
      @onCreate="onCreate"
      @onUpdate="onUpdate"
      @onClose="closeModal"
    />
    <ModalImport
      v-model:visible="isModalImportVisible"
      :item="item"
      @onCreate="onCreateImport"
      @onClose="closeModal"
    />
  </div>
</template>

<script>
import { USER_ROLES } from "@/databags/user";
import userApi from "@/api/user";
import roleApi from "@/api/role";
import { delay, downloadFile } from "@/libraries/helper";
import ModalForm from "@/components/users/ModalForm.vue";
import Tabs from "primevue/tabs";
import TabList from "primevue/tablist";
import Tab from "primevue/tab";
import { mapGetters } from "vuex";
import ModalImport from "@/components/members/ModalImport.vue";

export default {
  components: {
    ModalImport,
    ModalForm,
    Tabs,
    TabList,
    Tab,
  },
  props: {},
  data() {
    return {
      isFetching: false,
      currentPage: 1,
      totalPage: 1,
      orderBy: "created_at",
      sortBy: "desc",
      limit: 10,
      keyword: "",
      items: [],
      total: 0,
      isReset: false,
      isModalVisible: false,
      selectedItem: null,
      roles: null,
      activeRightTab: "member",
      rightDefault: USER_ROLES,
      isModalImportVisible: false,
    };
  },
  computed: {
    ...mapGetters({
      userFeatures: "auth/userFeatures",
    }),
    formattedLabel() {
      return this.activeRightTab
        .replace(/_/g, " ")
        .replace(/\b\w/g, (char) => char);
    },
    filteredRightDefault() {
      const readableFeatures = new Set(
        this.userFeatures
          .filter(
            (f) =>
              f.is_enable && 
              f.permission.can_read &&
              ["super_admin", "admin", "manager", "member"].includes(f.name)
          )
          .map((f) => f.name.toLowerCase())
      );
      return this.rightDefault.filter((role) =>
        readableFeatures.has(role.id.toLowerCase())
      );
    },
  },
  watch: {
    keyword() {
      delay(() => {
        this.fetchList(true);
      }, 500);
    },
    isModalVisible() {
      if (!this.isModalVisible) {
        this.selectedItem = null;
      }
    },
    activeRightTab() {
      this.fetchList(true);
      this.fetchRoles();
      this.resetAll();
    },
  },
  created() {
    this.fetchRoles();
    this.fetchList();
  },
  mounted() {},
  beforeUnmount() {},
  methods: {
    onCreateImport(items) {
      this.fetchList();
    },
    showImport() {
      this.isModalImportVisible = true;
    },
    checkPermissionCanWrite(role) {
      return this.userFeatures.find(
        (f) => f.permission.can_write && f.name?.toLowerCase() === role
      );
    },
    checkPermissionCanDelete(role) {
      return this.userFeatures.find(
        (f) => f.permission.can_delete && f.name?.toLowerCase() === role
      );
    },
    fetchList(isReset = false) {
      if (isReset) {
        this.currentPage = 1;
        this.items = [];
      }
      this.isFetching = true;
      const params = {
        order_by: this.orderBy,
        sort_by: this.sortBy,
        limit: this.limit,
        page: this.currentPage,
        endpoint: this.activeRightTab,
      };
      if (this.keyword) params.keyword = this.keyword;
      const callback = (response) => {
        const data = response.data;
        this.totalPage = response.meta.last_page;
        this.total = response.meta.total;
        this.items = data;
        this.isFetching = false;
      };
      const errorCallback = (error) => {
        const message = error.response.data.message;
        this.__showNotif("error", this.$t('Error'), message);
        this.isFetching = false;
      };
      userApi.getList(params, callback, errorCallback);
    },
    fetchRoles() {
      this.isFetching = true;
      const params = {
        limit: 9999,
        right: this.activeRightTab,
      };
      const callback = (response) => {
        const data = response.data;
        this.roles = data;
        this.isFetching = false;
      };
      const errorCallback = (error) => {
        const message = error.response.data.message;
        this.__showNotif("error", this.$t('Error'), message);
        this.isFetching = false;
      };
      roleApi.getList(params, callback, errorCallback);
    },
    updatesortField(event) {
      this.orderBy = event;
    },
    updateSortOrder(event) {
      if (event === 1) {
        this.sortBy = "desc";
      } else {
        this.sortBy = "asc";
      }
      this.fetchList();
    },
    pageUpdate(event) {
      this.currentPage = event.page + 1;
      if (event.rows !== this.limit) this.currentPage = 1;
      this.limit = event.rows;
      this.fetchList();
    },
    resetAll() {
      this.isReset = true;
      // Reset sorting
      this.keyword = "";
      this.orderBy = "created_at"; // Default sort field
      this.sortBy = "desc";
      this.fetchList(true);
      setTimeout(() => {
        this.isReset = false;
      }, 100);
    },
    editItem(item) {
      this.isModalVisible = true;
      this.selectedItem = item.data;
    },
    onRemove(selectedId) {
      const index = this.items.findIndex((curr) => curr.id === selectedId);
      if (index !== -1) this.items.splice(index, 1);
    },
    approveRemove(id) {
      // If no selected
      this.isDeleting = true;
      const callback = (response) => {
        this.onRemove(id);
        this.isDeleting = false;
        const message = response.message;
        this.__showNotif("success", this.$t('Success'), message);
      };
      const errorCallback = (error) => {
        const message = error.response.data.message;
        this.__showNotif("error", this.$t('Error'), message);
        this.isDeleting = false;
      };
      const params = {
        id,
        endpoint: this.activeRightTab,
      };
      userApi.delete(params, callback, errorCallback);
    },
    deleteItem(item) {
      this.$confirm.require({
        message: `${this.$t("Do you want to delete this")} ${item.full_name}?`,
        header: this.$t("Delete User"),
        icon: "pi pi-info-circle",
        rejectProps: {
          label: this.$t("Cancel"),
          severity: "secondary",
          outlined: true,
        },
        acceptProps: {
          label: this.$t("Delete"),
          severity: "danger",
        },
        accept: () => {
          this.approveRemove(item.id);
        },
        reject: () => {
          this.$toast.add({
            severity: "error",
            summary: "Rejected",
            detail: "You have rejected",
            life: 3000,
          });
        },
      });
    },
    showCreate() {
      this.selectedItem = null;
      this.isModalVisible = true;
    },
    onUpdate(item) {
      const itemIndex = this.items.findIndex((curr) => curr.id === item.id);
      if (itemIndex !== -1) Object.assign(this.items[itemIndex], item);
      this.closeModal();
    },
    onCreate(item) {
      if (this.sortBy === "desc") this.items.unshift(item);
      else if (this.sortBy === "asc") this.items.push(item);
      this.closeModal();
    },
    closeModal() {
      this.isModalVisible = false;
      this.selected = null;
      this.isModalImportVisible = false;
    },
    exportMember() {
      const callback = (response) => {
        const item = response.data;
        downloadFile(item);
        this.isFetching = false;
        const message = response.message;
        this.__showNotif("success", this.$t('User'), message);
      };
      const errorCallback = (error) => {
        const message = error.response.data.message;
        this.__showNotif("error", this.$t('Error'), message);
        this.isFetching = false;
      };
      const types = {};
      userApi.exportMember(types, callback, errorCallback);
    },
  },
};
</script>
