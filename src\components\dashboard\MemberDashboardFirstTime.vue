<template>
  <!-- custom welcome -->
  <div class="card grid grid-cols-12 gap-4 mt-6">
    <div class="col-span-6">
      <DescriptionBox
        style="margin-left: -14px"
        :description="greeting_message"
        :isDisplay="true"
        :isLong="true"
      />
    </div>

    <!-- video or image or thumbnail -->
    <div class="col-span-6 items-center justify-center">
      <div v-if="video_message">
        <div
          v-if="isImage(video_message)"
          class="aspect-w-16 aspect-h-9 w-full overflow-hidden rounded-lg"
        >
          <img
            :src="video_message"
            alt="Greeting Media"
            class="object-cover w-full h-full"
          >
        </div>
        <div v-else-if="isVideo(video_message)" class="relative card-container">
          <div
            v-show="video_thumbnail && !isVideoPlaying"
            class="aspect-w-16 aspect-h-9 w-full overflow-hidden rounded-lg cursor-pointer tilt-card"
            @click="playVideo"
          >
            <img
              :src="video_thumbnail"
              alt="Video Thumbnail"
              class="object-cover w-full h-full"
            >
          </div>

          <video-player
            v-if="isVideoPlaying"
            ref="videoWelcome"
            :src="video_message"
            controls
            :options="videoOptions"
            :loop="false"
            class="w-full rounded-lg overflow-hidden vjs-fluid"
            @ready="onPlayerReady"
          />
        </div>
        <p v-else class="text-sm text-gray-500">Unsupported media type.</p>
      </div>
    </div>
  </div>
  <div>
    <div class="mt-8">
      <div class="px-4 sm:px-0 mb-4">
        <div class="flex items-center justify-between">
          <div>
            <div class="font-bold text-xl">
              {{ $t("Suggested Course") }}
            </div>
          </div>
          <div class="flex space-x-4">
            <IconField>
              <InputIcon>
                <i class="pi pi-search" />
              </InputIcon>
              <InputText
                v-model="keyword"
                :placeholder="$t('Type in search term')"
                class="min-w-[246px]"
              />
              <!-- Clear Icon -->
              <InputIcon v-if="keyword" @click="keyword = ''">
                <i class="pointer pi pi-times" />
              </InputIcon>
            </IconField>
          </div>
        </div>
      </div>
    </div>

    <!-- content -->
    <div v-if="!isFetching" class="mt-4">
      <Card>
        <template #content>
          <!-- List -->
          <DataView :value="items">
            <template #list="slotProps">
              <div class="pb-16 flex flex-col md:grid md:grid-cols-2 lg:grid-cols-3 gap-28">
                <div
                  v-for="(item, index) in slotProps.items"
                  :key="index"
                  class="relative group card-container"
                >
                  <div class="gap-4">
                    <div
                      class="tilt-card pointer relative overflow-hidden rounded-2xl flex items-center bg-gray-100 group border-hover-card"
                      @click="showPreview(item)"
                    >
                      <div class="relative w-full" style="padding-bottom: 56.25%">
                        <img
                          v-if="item.thumbnail"
                          class="absolute top-0 left-0 w-full h-full object-cover rounded-2xl"
                          :src="item.thumbnail"
                          :alt="item.name"
                        >
                        <img
                          v-else
                          class="absolute top-0 left-0 w-full h-full object-cover rounded-2xl"
                          :src="__getRandomImage()"
                          :alt="item.name"
                        >
                      </div>

                      <!-- button actions member -->
                      <div class="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300" @click.stop="">
                        <Button :label="$t('Overview')" icon="pi pi-eye" iconPos="left" class="mr-2" style="color: #3b82f6; background: white" @click.stop="showPreview(item)" />
                        <Button :label="$t('Start Course')" class="start-course-button" @click="startCourse(item)" />
                      </div>
                    </div>
                    <div class="justify-between gap-6">
                      <div class="items-start">
                        <div>
                          <div class="text-lg font-medium mt-2 truncate">
                            {{ item.name }}
                          </div>
                          <div>
                            <div
                              class="text-gray-500 dark:text-gray-400 text-sm max-w-[40vw] truncate"
                              v-html="item.tagline"
                            />
                          </div>
                        </div>
                      </div>
                      <ProgressBar
                        v-if="item"
                        class="mt-6"
                        style="height: 2px"
                        :showValue="false"
                        :value="
                          __calculatePercentageWatched(
                            item?.histories?.completed_items,
                            item?.histories?.total_items
                          )
                        "
                      />
                      <div class="flex justify-between gap-8 mt-2">
                        <!-- Member -->
                        <div
                          class="font-bold text-primary-600 pointer"
                          @click="startCourse(item)"
                        >
                          <span
                            v-if="item?.histories && !item?.histories.is_completed"
                          >{{ $t("Resume Course") }}
                          </span>
                          <span
                            v-else
                          >{{ $t("Start Course") }}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </template>
            <template #empty>
              <div class="py-32 text-center">
                {{ $t("Keep your eyes peeled – more exciting content is on the way!") }}
              </div>
            </template>
          </DataView>

          <div
            v-if="currentPage * limit < total && totalPage > 1"
            class="flex justify-end mt-4 font-bold text-primary-600 mt-14 pointer"
            @click="viewMore()"
          >
            {{ $t("View more") }}
          </div>
        </template>
      </Card>
    </div>
  </div>
</template>

<script>
import Card from "primevue/card";
import DataView from "primevue/dataview";
import { delay } from "@/libraries/helper";
import ProgressBar from "primevue/progressbar";
import DescriptionBox from "@/components/form/DescriptionBox.vue";
import settingApi from "@/api/setting";
import { VideoPlayer } from "@videojs-player/vue";
import "video.js/dist/video-js.css";

export default {
  components: {
    Card,
    DataView,
    ProgressBar,
    DescriptionBox,
    VideoPlayer,
  },
  props: {},
  data() {
    return {
      isFetching: false,
      currentPage: 1,
      totalPage: 1,
      orderBy: "updated_at",
      sortBy: "desc",
      limit: 6,
      keyword: "",
      items: [],
      total: 0,
      isShowPreview: false,
      selectedItem: null,
      video_message: null,
      video_thumbnail: null,
      greeting_message: null,
      isVideoPlaying: false,
      player: null,
      videoOptions: {
        playbackRates: [0.5, 1, 1.5, 2], // Add playback speed options
        aspectRatio: "16:9",
        autoplay: true,
      },
    };
  },
  computed: {},
  watch: {
    keyword() {
      delay(() => {
        this.fetchList(true);
      }, 500);
    },
  },
  created() {},
  mounted() {
    this.fetchList();
    this.initSettings();
  },
  beforeUnmount() {},
  methods: {
    startCourse(item) {
      this.$router.push(`/courses/${item.id}/show`);
    },
    closeModal() {
      this.isShowPreview = false;
    },
    showPreview(item) {
      this.selectedItem = item;
      this.isShowPreview = true;
    },
    resetFilter() {
      // Reset sorting
      this.keyword = "";
      this.orderBy = "updated_at"; // Default sort field
      this.sortBy = "desc";
      this.fetchList(true);
    },
    viewMore() {
      this.currentPage++;
      this.fetchList();
    },
    pageUpdate(event) {
      this.currentPage = event.page + 1;
      if (event.rows !== this.limit) this.currentPage = 1;
      this.limit = event.rows;
      this.fetchList();
    },
    playVideo() {
      this.isVideoPlaying = true;
    },
    onPlayerReady(player) {
      console.log("Player instance:", this.$refs.videoWelcome);
    },
    isImage(link) {
      if (!link) return false;
      const lowerLink = link.toLowerCase();
      return (
        lowerLink.endsWith(".jpg") ||
        lowerLink.endsWith(".jpeg") ||
        lowerLink.endsWith(".png") ||
        lowerLink.endsWith(".gif") ||
        lowerLink.endsWith(".webp") ||
        lowerLink.endsWith(".svg")
      );
    },
    isVideo(link) {
      if (!link) return false;
      const lowerLink = link.toLowerCase();
      return (
        lowerLink.endsWith(".mp4") ||
        lowerLink.endsWith(".webm") ||
        lowerLink.endsWith(".ogg") ||
        lowerLink.endsWith(".mov") ||
        lowerLink.endsWith(".avi")
      );
    },
    initSettings(key, data) {
      const params = {};
      this.isSaving = true;
      const callback = response => {
        this.isFetching = false;
        const data = response.data;
        for (let index = 0; index < data.length; index++) {
          const element = data[index];
          if (element.key === "greeting_message") this.greeting_message = element.value;
          if (element.key === "video_message") this.video_message = element.value;
          if (element.key === "video_thumbnail") this.video_thumbnail = element.value;
        }
      };
      const errorCallback = error => {
        const message = error.response.data.message;
        this.__showNotif("error", this.$t('Error'), message);
        this.isFetching = false;
      };
      settingApi.getList(params, callback, errorCallback);
    },
  },
};
</script>
