<template>
  <Dialog
    :visible="isVisible"
    modal
    :header="$t('Preview Certificate')"
    :style="{ width: '1304px'}"
    @update:visible="isVisible = $event"
  >
    <CertificatePreview
      :certification="item"
      :show="isVisible"
    />
  </Dialog>	
</template>

<script>
import CertificatePreview from '@/components/profile/CertificatePreview.vue';
import * as htmlToImage from 'html-to-image';


export default {
  components: {
    CertificatePreview
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    item: {
      type: Object,
      default: () => ({}),
    }
  },
  data() {
    return {
    };
  },
  computed: {
    isVisible: {
      get() {
        return this.visible;
      },
      set(value) {
        this.$emit('update:visible', value);
      }
    },
    idCertificate() {
      return this.item.id;
    },
    bgImage() {
      return this.item?.file_url ? this.item.file_url : '';
    },
  },
  watch: {
  },
  created() {
		
  },
  methods: {
    downloadCert(id) {
      htmlToImage.toJpeg(document.getElementById(id), { quality: 0.95, skipFonts: true, })
        .then((dataUrl) => {
          const link = document.createElement('a');
          link.download = `${this.item.name}.jpeg`;
          link.href = dataUrl;
          link.click();
          this.$emit('onClose');
        });
    },
  },
};
</script>
