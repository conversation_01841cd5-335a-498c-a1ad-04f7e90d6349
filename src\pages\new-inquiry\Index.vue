<script setup>
import { ref } from 'vue';
import { useRouter } from 'vue-router';

const router = useRouter();

const form = ref({
  title: '',
  category: '',
  priority: 'medium',
  description: ''
});

const categories = ref([
  'Technical Support',
  'Account Issues',
  'Billing',
  'Feature Request',
  'Bug Report',
  'General Question'
]);

const priorities = ref([
  { label: 'Low', value: 'low' },
  { label: 'Medium', value: 'medium' },
  { label: 'High', value: 'high' }
]);

const isSubmitting = ref(false);

const submitInquiry = async () => {
  if (!form.value.title || !form.value.category || !form.value.description) {
    alert('Please fill in all required fields');
    return;
  }

  isSubmitting.value = true;
  
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Reset form
    form.value = {
      title: '',
      category: '',
      priority: 'medium',
      description: ''
    };
    
    alert('Inquiry submitted successfully!');
    router.push('/inquiries');
  } catch (error) {
    alert('Error submitting inquiry. Please try again.');
  } finally {
    isSubmitting.value = false;
  }
};
</script>

<template>
  <div class="new-inquiry-page">
    <div class="new-inquiry-container">
      <form @submit.prevent="submitInquiry" class="inquiry-form">
        <div class="form-group">
          <label for="title" class="form-label">Title *</label>
          <input
            id="title"
            v-model="form.title"
            type="text"
            class="form-input"
            placeholder="Enter inquiry title"
            required
          />
        </div>

        <div class="form-group">
          <label for="category" class="form-label">Category *</label>
          <select
            id="category"
            v-model="form.category"
            class="form-select"
            required
          >
            <option value="">Select a category</option>
            <option 
              v-for="category in categories" 
              :key="category" 
              :value="category"
            >
              {{ category }}
            </option>
          </select>
        </div>

        <div class="form-group">
          <label for="priority" class="form-label">Priority</label>
          <select
            id="priority"
            v-model="form.priority"
            class="form-select"
          >
            <option 
              v-for="priority in priorities" 
              :key="priority.value" 
              :value="priority.value"
            >
              {{ priority.label }}
            </option>
          </select>
        </div>

        <div class="form-group">
          <label for="description" class="form-label">Description *</label>
          <textarea
            id="description"
            v-model="form.description"
            class="form-textarea"
            placeholder="Describe your inquiry in detail"
            rows="6"
            required
          ></textarea>
        </div>

        <div class="form-actions">
          <button
            type="button"
            class="btn btn-secondary"
            @click="router.push('/inquiries')"
          >
            Cancel
          </button>
          <button
            type="submit"
            class="btn btn-primary"
            :disabled="isSubmitting"
          >
            {{ isSubmitting ? 'Submitting...' : 'Submit Inquiry' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<style scoped>
.new-inquiry-page {
  padding: 20px 16px;
  padding-bottom: 80px; /* Space for bottom navigation */
  min-height: 100vh;
  background-color: #f9fafb;
}

.new-inquiry-container {
  max-width: 100%;
  margin: 0 auto;
}

.inquiry-form {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 24px;
}

.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 6px;
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s ease;
  box-sizing: border-box;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-textarea {
  resize: vertical;
  min-height: 120px;
}

.form-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 24px;
}

.btn {
  padding: 12px 24px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.btn-secondary {
  background: #f3f4f6;
  color: #374151;
}

.btn-secondary:hover {
  background: #e5e7eb;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #2563eb;
}

.btn-primary:disabled {
  background: #d1d5db;
  cursor: not-allowed;
}

/* Desktop styles */
@media (min-width: 992px) {
  .new-inquiry-page {
    padding: 40px;
    padding-bottom: 40px;
  }
  
  .new-inquiry-container {
    max-width: 600px;
  }
  
  .inquiry-form {
    padding: 32px;
  }
  
  .form-input,
  .form-select,
  .form-textarea {
    font-size: 16px;
    padding: 14px 18px;
  }
  
  .btn {
    padding: 14px 28px;
    font-size: 16px;
  }
}
</style>
