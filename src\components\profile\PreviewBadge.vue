<template>
  <Dialog
    :visible="isVisible"
    modal
    :header="$t('Badge Preview')"
    :style="dialogStyle"
    @update:visible="isVisible = $event"
  >
    <div class="preview-data">
      <img
        :src="item.file_url"
        alt="Badge Preview"
        class="w-full max-h-[300px] object-contain mb-4"
      >
      <p class="text-gray-700  text-center my-6 mb-12">{{ item.description }}</p>
    </div>
  </Dialog>
</template>


<script>

export default {
  components: {
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    item: {
      type: Object,
      default: () => ({}),
    }
  },
  data() {
    return {
    };
  },
  computed: {
    isVisible: {
      get() {
        return this.visible;
      },
      set(value) {
        this.$emit('update:visible', value);
      }
    },
    idCertificate() {
      return this.item.id;
    },
    bgImage() {
      return this.item?.file_url ? this.item.file_url : '';
    },
    dialogStyle() {
      const lgMinWidth = 1024; // Tailwind's lg breakpoint
      if (window.innerWidth >= lgMinWidth) {
        return { width: '25rem' };
      } else {
        return { width: '1304px' };
      }
    },
  },
  watch: {
  },
  created() {
		
  },
  methods: {
  },
};
</script>

<style>
.p-dialog-content	{
	height: 100%;	
}
</style>
