
<template>
	<div>
		<!-- Heading -->
		<div class="px-4 sm:px-0 mb-4">
			<h3 class="text-base/7 font-semibold text-gray-900">
				{{ $t("Backups") }}
			</h3>
			<div class="flex items-center justify-between">
				<p class="mt-1 max-w-2xl text-sm/6 text-gray-500">
					{{ $t("Safeguard your data with the Backups feature, allowing administrators to schedule automated backups, manage restore points, and ensure reliable data recovery for platform continuity and security.") }}
				</p>
			</div>
		</div>

		<div class="space-y-4">
			<Card v-for="(item, index) in items" :key="index">
				<template #title>
					<div class="text-base">
						{{ item.name }}
					</div>
				</template>
				<template #content>
					<div class="flex flex-col md:items-end gap-8">
						<div class="flex flex-row-reverse md:flex-row gap-2">
							<Button icon="pi pi-download" />
						</div>
					</div>
				</template>
			</Card>

			<Card v-if="items.length === 0">
				<template #content>
					<div class="py-32 text-center">
						{{ $t("No data available.") }}
					</div>
				</template>
			</Card>
		</div>
	</div>
</template>

<script>
import Card from 'primevue/card';

export default {
	components: {
		Card,
	},
	props: {
	},
	data() {
		return {
			items: [],
			isFetching: false,
		};
	},
	computed: {},
	watch: {},
	created() {},
	mounted() {},
	beforeUnmount() {},
	methods: {},
};
</script>