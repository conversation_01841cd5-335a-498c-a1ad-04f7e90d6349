.layout-main-container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    justify-content: space-between;
    transition: margin-left var(--layout-section-transition-duration);
}

.margin-left-main {
  transition: margin-left var(--layout-section-transition-duration);
}

.layout-main {
    flex: 1 1 auto;
    padding-bottom: 2rem;
}

.layout-main-wo-pb {
  flex: 1 1 auto;
}

.main-padding {
  padding: 6rem 2rem 0 2rem;
}


.course-show-padding {
  padding: 6rem 0 0 2rem;
}

.lib-support-show-padding {
  padding: 4rem 0 0 2rem;
}

 /* Media query for mobile devices (max-width: 767px is a common breakpoint) */
@media (max-width: 767px) {
  .lib-support-show-padding {
    padding: 1rem 0 0 1rem; /* Reduce padding to 1rem on mobile */
  }
}