// import createPersistedState from "vuex-persistedstate";

export default {
  namespaced: true,
  // plugins: [createPersistedState()]
  state: () => ({
    onlineUsers: {},
    progress: 0,
    isLoading: false,
  }),
  mutations: {
    setOnlineUsers(state, items) {
      state.onlineUsers = items;
    },
    setProgress(state, value) {
      state.progress = value;
    },
    startLoading(state) {
      state.isLoading = true;
      state.progress = 0; // Reset progress when starting
    },
    stopLoading(state) {
      state.isLoading = false;
      state.progress = 100; // Set to 100% when finished
    },
  },
  actions: {
    clearOnlineUsers({ commit }) {
      commit("setOnlineListManually", {});
    },
    setProgress({ commit }, value) {
      commit('setProgress', value);
    },
    startLoading({ commit }) {
      commit('startLoading');
    },
    stopLoading({ commit }) {
      commit('stopLoading');
    },
		
  },
  getters: {
    getOnlineUsers(state) {
      return state.onlineUsers;
    },
    getGlobalProgressLoading(state) {
      return state.progress;
    }

  },
};