import NProgress from "@/libraries/nprogress"; // Import the NProgress instance

import { createRouter, createWebHistory, createWebHashHistory } from 'vue-router';

// Routes
const routes = [
  // Auth
  { name: 'Login', path: '/login', component: () => import('@/pages/auth/Login.vue'), meta: { title: 'Login', layout: 'auth' }, },
  { name: 'Logout', path: '/logout', component: () => import('@/pages/auth/Logout.vue'), meta: { title: 'Logout', layout: 'auth' }, },
  { name: 'ForgotPassword', path: '/forgot-password', component: () => import('@/pages/auth/ForgotPassword.vue'), meta: { title: 'Forgot Password', layout: 'auth' }, },
  { name: 'ResetPassword', path: '/password/reset', component: () => import('@/pages/auth/ResetPassword.vue'), meta: { title: 'Reset Password', layout: 'auth' }, },
  { name: 'Resend Email', path: '/resend-email', component: () => import('@/pages/auth/ResendEmail.vue'), meta: { title: 'Resend Email', layout: 'auth' }, },
  { name: 'Verify', path: '/user/verify', component: () => import('@/pages/auth/Verify.vue'), meta: { title: 'Verify', layout: 'auth' }, },
  { name: 'Reset', path: '/reset', component: () => import('@/pages/auth/Reset.vue'), meta: { title: 'Reset', layout: 'auth' }, },
  { name: 'NotFound', path: '/:path(.*)', component: () => import('@/pages/errors/NotFound.vue'), meta: { title: 'Not Found', layout: 'auth' }, },
  { name: 'MagicLink', path: '/magic-link', component: () => import('@/pages/auth/MagicLink.vue'), meta: { title: 'Magic Link', layout: 'auth' }, },
  { name: 'Verify', path: '/auth/autoLogin', component: () => import('@/pages/auth/Verify.vue'), meta: { title: 'Verify', layout: 'auth' }, },

  // Main
  { name: 'Index', path: '/', component: () => import('@/pages/dashboard/Index.vue'), meta: { title: 'Dashboard', layout: 'main' }, },
  { name: 'Profile', path: '/profile', component: () => import('@/pages/profile/Index.vue'), meta: { title: 'Profile', layout: 'main' }, },
  { name: 'ChangePassword', path: '/change-password', component: () => import('@/pages/change-password/Index.vue'), meta: { title: 'Profile', layout: 'main' }, },

  // Mobile Navigation Pages
  { name: 'NewsFeed', path: '/news-feed', component: () => import('@/pages/news-feed/Index.vue'), meta: { title: 'News Feed', layout: 'main' }, },
  { name: 'Inquiries', path: '/inquiries', component: () => import('@/pages/inquiries/Index.vue'), meta: { title: 'Inquiries', layout: 'main' }, },
  { name: 'Chat', path: '/chat', component: () => import('@/pages/chat/Index.vue'), meta: { title: 'Chat', layout: 'main' }, },
  { name: 'NewInquiry', path: '/new-inquiry', component: () => import('@/pages/new-inquiry/Index.vue'), meta: { title: 'New Inquiry', layout: 'main' }, },

  // Admin
  { name: 'AdminAnalytics', path: '/admin/analytics', component: () => import('@/pages//admin/analytics/Index.vue'), meta: { title: 'AdminAnalytics', layout: 'main' }, },
  { name: 'AdminBackups', path: '/admin/backups', component: () => import('@/pages//admin/backups/Index.vue'), meta: { title: 'AdminBackups', layout: 'main' }, },
  { name: 'AdminRoles', path: '/admin/roles', component: () => import('@/pages//admin/roles/Index.vue'), meta: { title: 'AdminRoles', layout: 'main' }, },
  { name: 'AdminSettings', path: '/admin/settings', component: () => import('@/pages/admin/settings/Index.vue'), meta: { title: 'AdminSettings', layout: 'main' }, },

  { name: 'AdminPlatformFeatures', path: '/admin/platform-features', component: () => import('@/pages/admin/platform-features/Index.vue'), meta: { title: 'AdminPlatformFeatures', layout: 'main' }, },
  { name: 'AdminUsers', path: '/admin/users', component: () => import('@/pages//admin/users/Index.vue'), meta: { title: 'AdminUsers', layout: 'main' }, },
];

// Middleware here
import vueMiddleware from "@grafikri/vue-middleware";
import authMiddleware from '@/middleware/auth.js';
const middleware = [authMiddleware];

// Apply middleware.
routes.forEach(route => {
  // eslint-disable-next-line no-param-reassign
  route.meta.middleware = middleware;
});

// @todo: move this somewhere else
let mode = 'spa';
const history = (mode == 'spa') ? createWebHistory() : createWebHashHistory();

export default {
  createRouter(_store) {
    let router = createRouter({
      history: history,
      linkActiveClass: "active",
      linkExactActiveClass: "exact-active",
      scrollBehavior() {
        return { x: 0, y: 0 };
      },
      routes
    });

    // Start NProgress before navigation
    router.beforeEach((to, from, next) => {
      NProgress.start();
      next();
    });

    // Stop NProgress after navigation completes
    router.afterEach(() => {
      NProgress.done();
    });

    router.beforeEach(vueMiddleware({ store: _store }));
    return router;
  }
};