{
    "vetur.validation.style": false,
    "editor.codeActionsOnSave": {
        "source.fixAll.eslint": "explicit"
    },
    "eslint.timeBudget.onFixes": {
        "warn": 3000,
        "error": 6000
    },
		"editor.tabSize": 2,
		"editor.insertSpaces": true,
		"editor.detectIndentation": false, // VERY IMPORTANT!
    "vue3snippets.enable-compile-vue-file-on-did-save-code": false,
    "files.exclude": {
        "**/node_modules": true, // Exclude node_modules in any directory
        "**/bower_components": true, // Exclude bower_components
        "**/.git": true,          // Exclude the .git folder
        "**/.svn": true,          // Exclude .svn folders
        "**/.hg": true,           // Exclude .hg folders
        "**/.DS_Store": true,       // Exclude .DS_Store files (macOS)
        "**/dist": true,           // Exclude dist folders
        "**/build": true,          // Exclude build folders
        "**/coverage": true,       // Exclude coverage folders
        "**/temp": true,           // Exclude temporary files/folders
        "**/logs": true,           // Exclude log files/folders
        // "**/*.min.js": true,      // Exclude minified JavaScript files
        "**/src/assets/contentBox.js": true,     // Example: Exclude the assets folder
        "**/src/assets/contentBuilder.js": true,     // Example: Exclude the assets folder
        "specific-folder/": true,  // Exclude a folder named "specific-folder" at the root of the workspace
        "another-folder/*": true   // Exclude the contents of "another-folder" but not the folder itself
    },
}