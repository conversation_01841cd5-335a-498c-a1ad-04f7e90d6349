import moment from 'moment';
import localforage from 'localforage';
import { getMediaBlob, has } from '@/libraries/helper';
import img1 from '@/assets/images/placeholders/img1.jpg';
import img2 from '@/assets/images/placeholders/img2.jpg';
import img3 from '@/assets/images/placeholders/img3.jpg';
import img4 from '@/assets/images/placeholders/img4.jpg';
import img5 from '@/assets/images/placeholders/img5.jpg';
import img6 from '@/assets/images/placeholders/img6.jpg';

export const __duplicateVar = value => JSON.parse(JSON.stringify(value));
export default {
  computed: {
    __isShowLeftNavigation() {
      return this.$store.getters['navigation/isShowLeftNavigation'];
    },
  },
  methods: {
    __duplicateVar,
    __checkCache(key) {
      return new Promise((resolve, reject)=> {
        localforage.getItem(key).then(async (value) => {
          if (value) {
            resolve(value);
          } else {
            resolve(false);
          }
        });
      });
    },
    __cacheTemplate(key, url, isReset=false) {
      return new Promise((resolve, reject)=> {
        localforage.getItem(key).then(async (value) => {
          if (value && !isReset) {
            resolve(value);
          } else {
            const data = await (await fetch(url)).json();
            localforage.setItem(key, data).then(() => localforage.getItem(key)).then((value) => {
              resolve(value);
            }).catch((err) => {
              reject(err);
            });
          }
        });
      });
    },
    __cacheFont(key, url, isReset=false) {
      return new Promise((resolve, reject)=> {
        localforage.getItem(key).then(async (value) => {
          if (value && !isReset) {
            resolve(value);
          } else {
            const response = await fetch(url);
            const fontBlob = await response.blob();
            localforage.setItem(key, fontBlob).then(() => localforage.getItem(key)).then((value) => {
              resolve(value);
            }).catch((err) => {
              reject(err);
            });
          }
        });
      });
    },
    __resetCache(key) {
      return new Promise((resolve, reject)=> {
        localforage.removeItem(key);
        resolve();
      });
			
    },
    __cacheMedia(key, url, isReset=false) {
      return new Promise((resolve, reject)=> {
        localforage.getItem(key).then(async (value) => {
          if (value && !isReset) {
            const mediaSource = URL.createObjectURL(value);
            resolve(mediaSource);
          } else {
            const data = await getMediaBlob(url);
            localforage.setItem(key, data).then(() => localforage.getItem(key)).then((value) => {
              const mediaSource = URL.createObjectURL(value);
              resolve(mediaSource);
            }).catch((err) => {
              reject(err);
            });
          }
        });
      });
    },
    __cacheMediaVideo(key, url, isReset=false, blob, expirationDate) {
      return new Promise((resolve, reject)=> {
        localforage.getItem(key).then(async (value) => {
          if (value && !isReset) {
            const mediaSource = URL.createObjectURL(value);
            resolve(mediaSource);
          } else {
            let data = null;
            if (blob) {
              data = blob;
            } else {
              data = await getMediaBlob(url);
            }
            localforage.setItem(key, {
              data: data && has(data, 'data') ? data.data : data,
              expires: expirationDate.getTime() // Store the expiration date as a timestamp
            }).then(() => localforage.getItem(key)).then((value) => {
              try {
                const mediaSource = URL.createObjectURL(value.data);
                resolve(mediaSource);
              } catch (error) {
              }
            }).catch((err) => {
              reject(err);
            });
          }
        });
      });
    },
		
    __toTitleCase(text) {
      const result = text ? text.toUpperCase() : '';
      return result;
    },
    __showLeftNavigation() {
      this.$store.dispatch("navigation/setShowLeftNavigation", !this.__isShowLeftNavigation);
    },
    __showNotif(type = 'info', title = '', message = '', duration = 5000) { //default duration 5 sec
      console.log(title);
      this.$notify({
        group: "app",
        type: type,
        title: title,
        text: message,
        duration: duration,
      });
    },
    __showNotifWButton(type = 'info', title = '', message = '', btnCaption='', url='') {
      this.$notify({
        group: "button",
        type: type,
        title: title,
        text: message,
        data: {
          btnCaption,
          url
        }
      });
    },
    __formatCreatedAndModified(createdAt, modifiedAt) {
      if (typeof moment === 'undefined') {
        console.error('Moment.js is not loaded. Make sure to include it in your project.');
        return "Moment.js not loaded";
      }
    
      if (!createdAt || !modifiedAt) {
        console.error('formatCreatedAndModified: Missing createdAt or modifiedAt parameters.');
        return "Missing date parameters";
      }
    
      try {
        const createdString = moment(createdAt).format('DD.MM.YYYY - HH:mm');
        const modifiedString = moment(modifiedAt).format('DD.MM.YYYY - HH:mm');
    
        return `Created date ${createdString} - Last modified ${modifiedString}`;
      } catch (error) {
        console.error('formatCreatedAndModified: Invalid createdAt or modifiedAt:', error);
        return "Invalid date input";
      }
    },
    __dateFormat(dateTime) {
      return moment(dateTime).format('Do MMMM YYYY');
    },
    __dateFormatISO(dateTime) {
      return moment(dateTime).format('YYYY-MM-DD');
    },
    __dateAddedFormatISO(dateTime) {
      return moment(dateTime).format('DD-MM-YYYY');
    },
    __dateFormatDefaultProject(dateTime) {
      return moment(dateTime).format('DD.MM.YY');
    },
    __dateTimeFormat(dateTime) {
      return moment(dateTime).format('Do MMMM YYYY, hh:mm:ss');
    },
    __dateTimeFormatISO(dateTime) {
      return moment(dateTime).format('YYYY-MM-DD hh:mm:ss');
    },
    __dateTimeFormatISO(dateTime) {
      return moment(dateTime).format('DD.MM.YY - hh.mm');
    },
    __parseStringToArray(text) {
      const items = text && typeof text === 'string' ? JSON.parse(text) : [];
      return items;
    },
    __intToTime(seconds) {
      if (seconds < 60) {
        return new Date(seconds * 1000).toISOString().slice(17, 19);
      } else if (seconds >= 60 && seconds < 3600) {
        return new Date(seconds * 1000).toISOString().slice(14, 19);
      } else {
        return new Date(seconds * 1000).toISOString().slice(11, 19);
      }
    },
    __timeFormat(seconds) {
      if (seconds < 3600) { 
        return new Date(seconds * 1000).toISOString().slice(14, 19);
      } else {
        return new Date(seconds * 1000).toISOString().slice(11, 19);
      }
    },
    __timeToSecond(time) {
      const hmsDuration = time;
      // check if hmsDuration split by : is 3
      if (hmsDuration.split(':').length === 3) { 
        const [hrsDu, minutesDu, secondsDu] = hmsDuration.split(':');
        return (+hrsDu)*60*60 + (+minutesDu) * 60 + (+secondsDu);
      } else {
        const [minutesDu, secondsDu] = hmsDuration.split(':');
        return (+minutesDu) * 60 + (+secondsDu);
      }
    },

    __getDynamicPath(url) {
      try {
        // Create a new URL object
        const parsedUrl = new URL(url);
        // Return the pathname which contains the dynamic part of the URL
        return parsedUrl.pathname;
      } catch (error) {
        return url;
      }
    },
    
    __getGreet() {
      // Get the current hour
      const currentHour = moment().hour();

      // Determine the time of day
      let timeOfDay;

      if (currentHour >= 5 && currentHour < 12) {
        timeOfDay = 'Morning';
      } else if (currentHour >= 12 && currentHour < 17) {
        timeOfDay = 'Afternoon';
      } else if (currentHour >= 17 && currentHour < 21) {
        timeOfDay = 'Evening';
      } else {
        timeOfDay = 'Evening';
      }
      return timeOfDay;
    },

    __dateFormatHome() {
      const today = moment();
      return today.format('dddd, D MMMM');
    },

    __dateFormatWebinar(startDate, endDate) {
      const start = moment(startDate);
      const end = moment(endDate);
  
      // Format the output: "Thursday 09:00 - 11:00 12.12.2024"
      return `${start.format('dddd')} ${start.format('HH:mm')} - ${end.format('HH:mm')} ${start.format('DD.MM.YYYY')}`;
    },
    
    __updateQueryStringParameter(key, value) {
      const uri = window.location.href;
      const re = new RegExp(`([?&])${key}=.*?(&|$)`, 'i');
      const separator = uri.indexOf('?') !== -1 ? '&' : '?';
      if (uri.match(re)) return uri.replace(re, `$1${key}=${value}$2`);
      const newURL = `${uri + separator + key}=${value}`;
      return newURL;
    },

    __removeURLParam(key) {
      const sourceURL = window.location.href;
      const queryString = (sourceURL.indexOf('?') !== -1) ? sourceURL.split('?')[1] : '';
      let rtn = sourceURL.split('?')[0];
      let param = '';
      let paramsArray = [];
      if (queryString !== '') {
        paramsArray = queryString.split('&');
        for (let i = paramsArray.length - 1; i >= 0; i -= 1) {
          param = paramsArray[i].split('=')[0];
          if (param === key) {
            paramsArray.splice(i, 1);
          }
        }
        rtn = `${rtn}?${paramsArray.join('&')}`;
      }
      return rtn;
    },
    __setNewURL(newURL) {
      window.history.pushState(null, null, newURL);
    },

    __formatDuration(duration) {
      const hours = Math.floor(duration / 3600); // Convert seconds to hours
      const minutes = Math.floor((duration % 3600) / 60); // Remaining minutes
      const seconds = duration % 60; // Remaining seconds

      let formattedTime = '';

      if (hours > 0) {
        formattedTime += `${hours} h `;
      }
      if (minutes > 0) {
        formattedTime += `${minutes} minutes `;
      }
      formattedTime += `${seconds} secs`;

      return formattedTime.trim();
    },

    __calculatePercentageWatched(watchedDuration = 0, totalDuration = 0) {
      if (totalDuration === 0) {
        return 0; // Avoid division by zero
      }
    
      if (watchedDuration >= totalDuration) {
        return 100;
      }
    
      const percentage = (watchedDuration / totalDuration) * 100;
    
      // Round to the nearest whole number
      return Math.round(percentage);
    },

    // Group By
    __groupBy(arr, property) {
      const grouped = arr.reduce((memo, x) => {
        const temp = memo;
        if (!temp[x[property]]) {
          temp[x[property]] = [];
        }
        temp[x[property]].push(x);
        return temp;
      }, {});
      return grouped;
    },

    // Generate Dates
    __generateDates(value, type, format = 'YYYY-MM-DD', endDate) {
      const dates = [];
      const firstIndex = value > 0 ? 0 : value;
      const lastIndex = value > 0 ? value : 0;
      for (let index = firstIndex; index <= lastIndex; index++) {
        const day = endDate || dayjs();
        const date = day.add(index, type).format(format);
        dates.push(date);
      }
      return dates;
    },

    __truncateText40(text) {
      if (!text) return '';
      return text.length > 40 ? text.slice(0, 40) + '...' : text;
    },

    __truncateText60(text) {
      if (!text) return '';
      return text.length > 60 ? text.slice(0, 60) + '...' : text;
    },

    __truncateText230(text) {
      if (!text) return '';
      return text.length > 230 ? text.slice(0, 230) + '...' : text;
    },

    __getRandomImage() {
      const images = [img1, img2, img3, img4, img5, img6];
      const randomIndex = Math.floor(Math.random() * images.length);
      return images[randomIndex];
    }
  },
  mounted() {
  }
};
