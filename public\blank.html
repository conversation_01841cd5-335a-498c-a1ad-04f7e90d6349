<!DOCTYPE HTML>
<html>
<head>
    <meta charset="utf-8">
    <title></title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="A drag & drop HTML editor Javascript library for web applications.">  
    <link rel="shortcut icon" href="#"> 

    <link href="assets/minimalist-blocks/content.css" rel="stylesheet">
    <link href="box/box-flex.css" rel="stylesheet">
    
</head>
<body>

<div class="is-wrapper">
</div>

<!-- Slider -->
<link href="assets/scripts/glide/css/glide.core.css" rel="stylesheet">
<link href="assets/scripts/glide/css/glide.theme.css" rel="stylesheet">
<script src="assets/scripts/glide/glide.js"></script>

<!-- Navbar -->
<link href="assets/scripts/navbar/navbar.css" rel="stylesheet">
<script src="assets/scripts/navbar/navbar.min.js"></script>

<script src="box/box-flex.js"></script>

<!-- Optional: smooth scrolling -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/smoothscroll/1.4.10/SmoothScroll.min.js"></script>
<script>
SmoothScroll({
    frameRate: 150,
    animationTime: 800,
    stepSize: 120,
    pulseAlgorithm: 1,
    pulseScale: 4,
    pulseNormalize: 1,
    accelerationDelta: 300,
    accelerationMax: 2,
    keyboardSupport: 1,
    arrowScroll: 50,
    fixedBackground: 0
});
</script>
</body>
</html>