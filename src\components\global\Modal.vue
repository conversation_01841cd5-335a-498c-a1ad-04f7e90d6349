<template>
	<TransitionRoot
		appear
		as="template"
		:show="isShow"
	>
		<div
			as="div"
			class="fixed z-50 inset-0 overflow-y-auto"
			:class="dialogClass"
		>
			<div
				class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0" 
			>
				<TransitionChild
					as="template"
					enter="ease-out duration-300"
					enterFrom="opacity-0"
					enterTo="opacity-100"
					leave="ease-in duration-200"
					leaveFrom="opacity-100"
					leaveTo="opacity-0"
				>
					<div class="fixed inset-0 bg-[#333333] opacity-90 transition-opacity" />
				</TransitionChild>

				<!-- This element is to trick the browser into centering the modal contents. -->
				<span
					class="hidden sm:inline-block sm:align-middle sm:h-screen"
					aria-hidden="true"
				>&#8203;</span>
				<TransitionChild
					as="template"
					enter="ease-out duration-300"
					enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
					enterTo="opacity-100 translate-y-0 sm:scale-100"
					leave="ease-in duration-200"
					leaveFrom="opacity-100 translate-y-0 sm:scale-100"
					leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
				>
					<div
						:class="customClass"
						class="modal-global inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:align-middle sm:max-w-lg sm:w-full w-full p-6"
						:style="customStyle"
					>
						<slot />
					</div>
				</TransitionChild>
			</div>
		</div>
	</TransitionRoot>
</template>

<script>
import { Dialog, DialogOverlay, DialogTitle, TransitionChild, TransitionRoot } from '@headlessui/vue';
import { ExclamationIcon } from '@heroicons/vue/outline';

export default {
	components: {
		Dialog,
		DialogOverlay,
		TransitionChild,
		TransitionRoot,
	},
	props: {
		isShow: {
			type: Boolean,
			default: () => false,
		},
		customClass: {
			type: String,
			default: () => 'default',
		},
		dialogClass: {
			type: String,
			default: () => 'default',
		},
		customStyle: {
			type: String,
			default: () => 'default',
		}
	},
	data() {
		return {
		};
	},
	watch: {
		isShow() {
			if (this.isShow) {
				this.$notify({
					group: 'app',
					clean: true
				});
				this.$notify({
					group: 'button',
					clean: true
				});
			}
		},
	},
	mounted() {
	},
	methods: {
		close() {
			this.$emit('onClose');
		},
	},
};
</script>