import { client } from '@/libraries/http-client';
import { buildQuery } from '@/libraries/helper';


export default {
  // Get List
  getList(params, cb, errorCb) {
    const responseHandler = (response) => {
      if (cb) cb(response.data);
    };
    const errorHandler = (e) => {
      if (errorCb) errorCb(e);
    };
    const query = buildQuery(params);
    const url = `${params.endpoint}?${query}`;
    client.get(url)
      .then(responseHandler)
      .catch(errorHandler);
  },

  // Create
  create(params, cb, errorCb) {
    const responseHandler = (response) => {
      if (cb) cb(response.data);
    };
    const errorHandler = (e) => {
      if (errorCb) errorCb(e);
    };
    const url = `${params.endpoint}`;
    client.post(url, params)
      .then(responseHandler)
      .catch(errorHandler);
  },

  // Update
  update(id, params, cb, errorCb) {
    const url = `${params.endpoint}/${id}`;
    client.put(url, params)
      .then((response) => {
        cb(response.data);
      })
      .catch((e) => {
        if (errorCb) {
          errorCb(e);
        }
      });
  },

  // Get details
  get(id, cb, errorCb) {
    const url = `${params.endpoint}/${id}`;
    client.get(url)
      .then((response) => {
        cb(response.data);
      })
      .catch((e) => {
        errorCb(e);
      });
  },

  // Delete
  delete(params, cb, errorCb) {
    const url = `${params.endpoint}/${params.id}`;
    client.delete(url)
      .then((response) => {
        cb(response.data);
      })
      .catch((e) => {
        if (errorCb) errorCb(e);
      });
  },

  // Update User Profile
  updateProfile(user, cb, errorCb) {
    client.put(params.endpoint, user)
      .then((response) => {
        cb(response.data);
      })
      .catch((e) => {
        if (errorCb) errorCb(e);
      });
  },

  bulkCreate(ids, types, cb, errorCb) {
    const members = JSON.stringify(ids);
    const payload = {
      members, 
    };
    if (types?.group_id) payload.group_id = types.group_id;
    if (types?.course_id) payload.course_id = types.course_id;
    const url = `/member/bulk`;
    const responseHandler = (response) => {
      if (cb) cb(response.data);
    };
    const errorHandler = (e) => {
      if (errorCb) errorCb(e);
    };
    client.post(url, payload )
      .then(responseHandler)
      .catch(errorHandler);
  },

  exportMember(types, cb, errorCb) {
    const payload = {};
    if (types?.group_id) payload.group_id = types.group_id;
    if (types?.course_id) payload.course_id = types.course_id;
  
    const url = `/member/export/excel`;
    client.get(url, { params: payload })
      .then((response) => {
        cb(response.data);
      })
      .catch((e) => {
        errorCb(e);
      });
  },
  
};
