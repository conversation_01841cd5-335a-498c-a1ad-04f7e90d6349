import { createApp, defineCustomElement } from 'vue';
import '@/tailwind.css';
import App from './App.vue';
import vue3GoogleLogin from 'vue3-google-login';
import Aura from '@primevue/themes/aura';
import PrimeVue from 'primevue/config';
import ConfirmationService from 'primevue/confirmationservice';
import ToastService from 'primevue/toastservice';
import Menu from 'primevue/menu';
import Avatar from 'primevue/avatar';
import DataTable from 'primevue/datatable';
import Column from 'primevue/column';
import InputIcon from 'primevue/inputicon';
import Button from 'primevue/button';
import IconField from 'primevue/iconfield';
import InputText from 'primevue/inputtext';
import Paginator from 'primevue/paginator';
import Toast from 'primevue/toast';
import ConfirmDialog from 'primevue/confirmdialog';
import Dialog from 'primevue/dialog';
import ToggleSwitch from 'primevue/toggleswitch';
import Select from 'primevue/select';
import Textarea from 'primevue/textarea';
import Tooltip from 'primevue/tooltip';
import ProgressSpinner from 'primevue/progressspinner';
import Breadcrumb from 'primevue/breadcrumb';
import { definePreset } from '@primevue/themes';
import Uploader from '@/components/global/Uploader.vue';
import SubHeaderForm from "@/components/other/SubHeaderForm.vue";
import FocusTrap from 'primevue/focustrap';

import '@/assets/styles.scss';
import '@/assets/tailwind.css';
import VueCookieAcceptDecline from 'vue-cookie-accept-decline';
import 'vue-cookie-accept-decline/dist/vue-cookie-accept-decline.css';

const app = createApp(App);
app.component('VueCookieAcceptDecline', VueCookieAcceptDecline);
app.config.warnHandler =  (msg, vm, trace) => {
  return null;
};

// google 
const gAuthOptions = { clientId: '137700338442-917aiqtlqm6v9noam00vfo0bl7rlams0.apps.googleusercontent.com', 
  scope: 'email profile openid', 
  prompt: 'consent', 
  fetch_basic_profile: false,
  prompt: false,
  autoLogin: false,
  popupType: 'TOKEN' };
app.use(vue3GoogleLogin, gAuthOptions);

// Store
import store from "@/store/index";
app.use(store);

// Routes
import routes from '@/router.js';
const router = routes.createRouter(store);
app.use(router);

// Add global mixin
import globalMixin from '@/mixins/global.js';
app.mixin(globalMixin);

// I18n
import i18n from "@/libraries/i18n.js";
app.use(i18n);

// Notifications
import Notifications from '@kyvg/vue3-notification';
import velocity from 'velocity-animate';
app.use(Notifications, { velocity });

// Fontawesome
import { library } from '@fortawesome/fontawesome-svg-core';
import { faBold, faUnderline, faItalic, faImage, faAlignCenter, faAlignLeft, faAlignRight, faAlignJustify, faHighlighter, faStrikethrough } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';
library.add(faBold, faUnderline, faItalic, faImage, faAlignCenter, faAlignLeft, faAlignRight, faAlignJustify, faHighlighter, faStrikethrough);
app.component('FontAwesomeIcon', FontAwesomeIcon);

// component
import LoaderCircle from "@/components/loader/LoaderCircle.vue";
import LoaderFullPage from "@/components/loader/LoaderFullPage.vue";
app.component('LoaderCircle', LoaderCircle);
app.component('LoaderFullPage', LoaderFullPage);

app.directive('tooltip', Tooltip);

// masonry
import {VueMasonryPlugin} from 'vue-masonry';
app.use(VueMasonryPlugin);

// meta
import { createMetaManager } from 'vue-meta';
app.use(createMetaManager());

app.use(router);
const MyPreset = definePreset(Aura, {
  semantic: {
    primary: {
      50: '{blue.50}',
      100: '{blue.100}',
      200: '{blue.200}',
      300: '{blue.300}',
      400: '{blue.400}',
      500: '{blue.500}',
      600: '{blue.600}',
      700: '{blue.700}',
      800: '{blue.800}',
      900: '{blue.900}',
      950: '{blue.950}'
    }
  }
});
app.use(PrimeVue, {
  theme: {
    preset: MyPreset,
    options: {
      darkModeSelector: '.app-dark'
    }
  }
});

import { CkeditorPlugin } from '@ckeditor/ckeditor5-vue';
app.use(CkeditorPlugin);

app.component('Menu', Menu);
app.component('Avatar', Avatar);
app.component('InputText', InputText);
app.component('IconField', IconField);
app.component('Button', Button);
app.component('InputIcon', InputIcon);
app.component('Column', Column);
app.component('DataTable', DataTable);
app.component('Paginator', Paginator);
app.component('Toast', Toast);
app.component('ConfirmDialog', ConfirmDialog);
app.component('Dialog', Dialog);
app.component('ToggleSwitch', ToggleSwitch);
app.component('Select', Select);
app.component('Textarea', Textarea);
app.component('ProgressSpinner', ProgressSpinner);
app.component('Breadcrumb', Breadcrumb);
app.component('Uploader', Uploader);
app.component('SubHeaderForm', SubHeaderForm);

app.use(ToastService);
app.use(ConfirmationService);

app.directive('focustrap', FocusTrap);

// Mount
app.mount('#app');

function updateAppOverflow() {
  const appElement = document.getElementById('body-app');
  if (appElement) {
    if (router.currentRoute.value.name !== 'CourseShow' && router.currentRoute.value.name !== "CourseShow" && router.currentRoute.value.name !== "LibraryDetails" && router.currentRoute.value.name !== "SupportDetails" && router.currentRoute.value.name !== "CoursePreviewLoggedInUser") {
      appElement.classList.add('overflow-y-scroll');
    } else {
      appElement.classList.remove('overflow-y-scroll');
    }
  }
}

// Call the function initially to set the class based on the initial route
updateAppOverflow();

// Listen for route changes and update the class accordingly
router.afterEach(() => {
  updateAppOverflow();
});