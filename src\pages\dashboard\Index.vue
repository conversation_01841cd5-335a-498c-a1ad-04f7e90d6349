
<template>
  <div>
    <!-- Heading -->
    <div
      v-if="userRole?.right !== 'member'"
      class="px-4 sm:px-0 mb-4 mt-2"
    >
      <h3 class="text-base/7 font-semibold text-gray-900">
        {{ $t("Dashboard") }}
      </h3>
      <SubHeaderForm :type="'dasboard'" />
    </div>
    <div v-else>
      <div class="font-medium text-2xl">
        {{ $t('Welcome') }}, {{ user?.full_name }}!
      </div>
    </div>

    <!-- Loader -->
    <loader-circle v-if="isFetching" />

    <!-- Body -->
    <div v-if="analytics && userRole && !isFetching">
      <SubHeaderForm v-if="userRole?.right === 'member'" :type="'dasboard'" />

      <super-admin-dashboard
        v-if="userRole.right === 'super_admin'"
        :analytics="analytics"
      />
      <admin-dashboard
        v-if="userRole.right === 'admin'"
        :analytics="analytics"
      />
      <manager-dashboard
        v-if="userRole.right === 'manager'"
        :analytics="analytics"
      />
      <member-dashboard
        v-if="userRole.right === 'member' && isRunningCourse"
      />
      <member-dashboard-first-time
        v-if="userRole.right === 'member' && !isRunningCourse && isShowCourseFirstTime" 
      />
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';

import SuperAdminDashboard from '@/components/dashboard/SuperAdminDashboard.vue';
import AdminDashboard from '@/components/dashboard/AdminDashboard.vue';
import ManagerDashboard from '@/components/dashboard/ManagerDashboard.vue';
import MemberDashboard from '@/components/dashboard/MemberDashboard.vue';
import MemberDashboardFirstTime from '@/components/dashboard/MemberDashboardFirstTime.vue';

import analyticApi from '@/api/analytic';

export default {
  components: {
    SuperAdminDashboard,
    AdminDashboard,
    ManagerDashboard,
    MemberDashboard,
    MemberDashboardFirstTime
  },
  props: {
  },
  data() {
    return {
      analytics: null,
      isFetching: false,
      isRunningCourse: false,
      isShowCourseFirstTime: false,
    };
  },
  computed: {
    ...mapGetters({
      userRole: 'auth/userRole',
      user: 'auth/user',
    }),
  },
  watch: {},
  created() {
    this.fetch();
  },
  mounted() {},
  beforeUnmount() {},
  methods: {
    fetch() {
      this.isFetching = true;
      const params = {};
      if (this.keyword) params.keyword = this.keyword;
      const callback = (response) => {
        const data = response;
        this.analytics = data;
        this.isFetching = false;
      };
      const errorCallback = (error) => {
        const message = error.response.data.message;
        this.__showNotif('error', this.$t('Error'), message);
        this.isFetching = false;
      };
      analyticApi.get(params, callback, errorCallback);
    },
  },
};
</script>