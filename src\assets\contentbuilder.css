body {
  overflow-x: hidden !important;
}

svg {
  overflow: hidden;
  vertical-align: middle;
  pointer-events: none;
}

.is-container > div > div svg {
  pointer-events: initial;
}

[contenteditable] svg {
  pointer-events: initial;
}

.is-icon-flex {
  width: 16px;
  height: 16px;
}

a:focus,
button:focus {
  outline: none;
}

a:focus-visible,
button:focus-visible {
  outline: #3e93f7 2px solid;
  outline-offset: 2px;
}

.transition1 {
  transition: all ease 0.1s;
}

.focus-outline {
  outline: #3e93f7 2px solid !important;
  outline-offset: 2px !important;
}

.is-page {
  position: relative;
  transform-origin: top;
  max-width: none;
  display: flex;
  align-items: center;
  flex-direction: column;
  width: 100%;
  height: 100%;
}

.is-page > .is-box.box-select {
  outline: 1px solid #00da89;
  outline-offset: -1px;
  z-index: 1;
}

.is-builder {
  transition: all ease 0.3s;
  transform-origin: top;
}
.is-builder > div > div:focus {
  outline: none;
}
.is-builder .is-subblock:focus {
  outline: none;
}
.is-builder > div {
  position: relative;
  transition: none;
  margin-left: 0;
  margin-right: 0;
  width: auto;
}
.is-builder[gridoutline] > div > div {
  outline: 1px solid rgba(132, 132, 132, 0.27);
  outline-offset: 1px;
}
.is-builder > div > div.cell-active:not([data-protected]) {
  outline: 1px solid #00da89;
  transition: none !important;
}
.is-builder .row-active:not([data-protected]) {
  outline: 1px solid #00da89;
  z-index: 1;
}
.is-builder .row-active:not([data-protected]).row-outline {
  outline: 1px solid rgba(132, 132, 132, 0.2);
}
.is-builder .row-active:not([data-protected]):not(.row-outline) > div.cell-active {
  outline: none;
}
.is-builder table.default td {
  border: transparent 1px dashed;
}
.is-builder .cell-active .elm-active:not(button) {
  background: rgba(200, 200, 201, 0.11);
  transition: none !important;
}
.is-builder .cell-active table.elm-active {
  background-color: transparent;
}
.is-builder .cell-active table.default td {
  border: #cccccc 1px dashed;
}
.is-builder .cell-active hr {
  cursor: pointer;
}
.is-builder .cell-active[data-html] {
  background-color: rgba(200, 200, 201, 0.11);
}
.is-builder .cell-active .icon-active,
.is-builder .cell-active .svg-active {
  background-color: rgba(200, 200, 201, 0.4);
}
.is-builder .elm-inspected {
  animation-name: elm-inspected-anim;
  animation-duration: 0.6s;
  outline: 1px solid #ffb84a !important;
}
.is-builder .elm-inspected .elm-active {
  background: none;
}
@keyframes elm-inspected-anim {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.97);
  }
  100% {
    transform: scale(1);
  }
}

@media (min-width: 971px) {
  .is-builder > div {
    display: flex;
  }
}
.sortable-drag {
  background-image: none !important;
  background-color: transparent !important;
  outline: none !important;
}
.sortable-drag * {
  opacity: 0;
}
.sortable-drag .is-row-tool {
  opacity: 0;
}

.sortable-ghost {
  background: rgba(204, 204, 204, 0.15);
  width: 100%;
  outline: none !important;
}
.sortable-ghost * {
  outline: none !important;
}
.sortable-ghost .is-row-tool,
.sortable-ghost .is-col-tool,
.sortable-ghost .is-rowadd-tool {
  display: none !important;
}

.sortable-drag::before {
  content: " ";
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.03);
  transform-origin: top left;
  left: 10%;
  top: -35%;
}

#_cbhtml .snippet-item {
  cursor: move !important;
}
#_cbhtml .snippet-item.sortable-chosen {
  height: auto;
}
#_cbhtml .snippet-item.sortable-chosen * {
  visibility: visible;
}
#_cbhtml .snippet-item.sortable-chosen.sortable-drag * {
  visibility: hidden;
  transition: none !important;
}
#_cbhtml .snippet-item.sortable-drag {
  outline: none !important;
}

.is-builder .snippet-item.sortable-ghost {
  width: 100%;
  background: rgba(204, 204, 204, 0.15);
  height: 40px;
}
.is-builder .snippet-item.sortable-ghost * {
  visibility: hidden;
}

.moveable-control {
  border: none !important;
  width: 17px !important;
  height: 17px !important;
  margin-top: -8.5px !important;
  margin-left: -8.5px !important;
  background: #fff !important;
  border: #dddddd 1px solid !important;
  box-shadow: 1px 3px 6px 0px rgba(0, 0, 0, 0.22) !important;
}
.moveable-control.moveable-origin {
  display: none !important;
}

.moveable-direction.moveable-s, .moveable-direction.moveable-n {
  display: none !important;
}
.moveable-direction.moveable-e, .moveable-direction.moveable-w {
  display: none !important;
}

.moveable-line {
  display: none !important;
}

#_cbhtml {
  height: 0px;
  float: left;
  margin-top: -100px;
}

#_cbhtml,
.is-ui {
  /*
  * { 
      font-family:sans-serif;
      line-height: inherit;
      // Prevent overide
  } 
  */
  font-family: sans-serif;
  font-size: 13px;
  letter-spacing: 1px;
  font-weight: 300;
  /*  general use */
  /*
  .rte-align-options,
  .rte-formatting-options,
  .rte-list-options,
  .rte-more-options,
  .elementrte-more-options,
  .rte-textsetting-options,
  .rte-color-picker,
  .rte-icon-options,
  .rte-fontfamily-options,
  .rte-customtag-options,
  .rte-paragraph-options,
  .rte-zoom-options
  */
  /*
  .pickgradientcolor {
      flex-direction: column;
      width: 300px;
      padding: 15px;
      box-sizing: border-box;
      .is-settings {
          margin-bottom: 15px;
          & > div {
              display: flex;
              align-items: center;
              height: 50px;
              &.is-label {
                  height: auto;
                  font-family: sans-serif;
                  font-size: 13px;
                  font-weight: 300;
                  letter-spacing: 1px;
                  margin: 10px 0 3px;
              }
          }
          button { 
              width: auto; 
              height: 37px; 
              font-size: 10px;
              line-height: 1;
              text-transform: uppercase;
              padding: 1px 20px;
              box-sizing: border-box;
              border: none;
              outline-offset: -2px;
              &.is-btn-color {
                  width: 35px;
                  height: 35px;
                  padding: 0;
                  background: $ui-modal-pickcolor-button-background;
                  border: $ui-modal-pickgradient-button-border;
              }
              .is-elmgrad-remove {
                  position: absolute;
                  top: 0px;
                  right: 0px;
                  width: 20px;
                  height: 20px;
                  background: $ui-modal-pickgradient-remove-button-background;
                  color: $ui-modal-pickgradient-remove-button-color;
                  line-height: 20px;
                  text-align: center;
                  font-size: 12px;
                  cursor: pointer;
                  display: none;
              }
              &[data-elmgradient].active .is-elmgrad-remove {
                  display:block;
              }
          }
          label {
              font-size: 13px;
              color: inherit;
          }
      } //.is-settings

      button.input-gradient-clear,
      button.input-gradient-clear:hover {
          border: transparent 1px solid;
          background-color: transparent;
      }

      .div-gradients {
          button{
              width:35px;
              height:35px;
              margin: 0;
              padding:0;
              border:none;
              transition: none;
              outline: none;
              border-radius: 0px;

              &:focus {
                  border: white 2px solid;
              }
          }
      }
  }
  */
}
#_cbhtml p,
.is-ui p {
  font-size: 13px;
}
#_cbhtml .style-helper,
.is-ui .style-helper {
  display: none;
  background: #fff;
  color: #121212;
}
#_cbhtml .style-helper.on,
.is-ui .style-helper.on {
  background: #f1f1f1;
}
#_cbhtml .style-helper.hover,
.is-ui .style-helper.hover {
  background: #f1f1f1;
}
#_cbhtml .style-helper svg,
.is-ui .style-helper svg {
  fill: #000;
}
#_cbhtml .style-helper.modal-color,
.is-ui .style-helper.modal-color {
  background: #000;
}
#_cbhtml .style-helper.modal-background,
.is-ui .style-helper.modal-background {
  background: #fff;
}
#_cbhtml .style-helper.button-pickcolor-border,
.is-ui .style-helper.button-pickcolor-border {
  border: rgba(0, 0, 0, 0.09) 1px solid;
}
#_cbhtml .style-helper.button-pickcolor-background,
.is-ui .style-helper.button-pickcolor-background {
  background: rgba(255, 255, 255, 0.2);
}
#_cbhtml .style-helper.snippet-color,
.is-ui .style-helper.snippet-color {
  background: #000;
}
#_cbhtml .style-helper.snippet-background,
.is-ui .style-helper.snippet-background {
  background: #fff;
}
#_cbhtml .style-helper.snippet-tabs-background,
.is-ui .style-helper.snippet-tabs-background {
  background: whitesmoke;
}
#_cbhtml .style-helper.snippet-tab-item-background,
.is-ui .style-helper.snippet-tab-item-background {
  background: #fff;
}
#_cbhtml .style-helper.snippet-tab-item-background-active,
.is-ui .style-helper.snippet-tab-item-background-active {
  background: whitesmoke;
}
#_cbhtml .style-helper.snippet-tab-item-background-hover,
.is-ui .style-helper.snippet-tab-item-background-hover {
  background: #fff;
}
#_cbhtml .style-helper.snippet-tab-item-color,
.is-ui .style-helper.snippet-tab-item-color {
  background: #000;
}
#_cbhtml .style-helper.snippet-more-item-background,
.is-ui .style-helper.snippet-more-item-background {
  background: #fff;
}
#_cbhtml .style-helper.snippet-more-item-background-active,
.is-ui .style-helper.snippet-more-item-background-active {
  background: #f7f7f7;
}
#_cbhtml .style-helper.snippet-more-item-background-hover,
.is-ui .style-helper.snippet-more-item-background-hover {
  background: #f7f7f7;
}
#_cbhtml .style-helper.snippet-more-item-color,
.is-ui .style-helper.snippet-more-item-color {
  background: #000;
}
#_cbhtml .style-helper.tabs-background,
.is-ui .style-helper.tabs-background {
  background: #fafafa;
}
#_cbhtml .style-helper.tab-item-active-border-bottom,
.is-ui .style-helper.tab-item-active-border-bottom {
  border: #595959 1px solid;
}
#_cbhtml .style-helper.tab-item-color,
.is-ui .style-helper.tab-item-color {
  background: #4a4a4a;
}
#_cbhtml .style-helper.tabs-more-background,
.is-ui .style-helper.tabs-more-background {
  background: #fff;
}
#_cbhtml .style-helper.tabs-more-border,
.is-ui .style-helper.tabs-more-border {
  border: 1px solid #f2f2f2;
}
#_cbhtml .style-helper.tabs-more-item-color,
.is-ui .style-helper.tabs-more-item-color {
  background: #4a4a4a;
}
#_cbhtml .style-helper.tabs-more-item-background-hover,
.is-ui .style-helper.tabs-more-item-background-hover {
  background: whitesmoke;
}
#_cbhtml .style-helper.separator-color,
.is-ui .style-helper.separator-color {
  background: #f0f0f0;
}
#_cbhtml .style-helper.outline-color,
.is-ui .style-helper.outline-color {
  background: #3e93f7;
}
#_cbhtml .style-helper-button-classic,
.is-ui .style-helper-button-classic {
  background: transparent;
  color: #000;
}
#_cbhtml .style-helper-button-classic.hover,
.is-ui .style-helper-button-classic.hover {
  background: #f1f1f1;
}
#_cbhtml .style-helper-button-classic svg,
.is-ui .style-helper-button-classic svg {
  fill: #000;
}
#_cbhtml .is-area color,
.is-ui .is-area color {
  background: #000 !important;
}
#_cbhtml .is-area background,
.is-ui .is-area background {
  background: #fff !important;
}
#_cbhtml .is-area button,
.is-ui .is-area button {
  color: #000 !important;
}
#_cbhtml .is-area button svg,
.is-ui .is-area button svg {
  fill: #000 !important;
}
#_cbhtml .is-area-2nd,
.is-ui .is-area-2nd {
  color: #000 !important;
  background: #fff !important;
}
#_cbhtml .is-area-2nd button,
.is-ui .is-area-2nd button {
  color: #000 !important;
}
#_cbhtml .is-area-2nd button svg,
.is-ui .is-area-2nd button svg {
  fill: #000 !important;
}
#_cbhtml .is-pop,
.is-ui .is-pop {
  display: none;
  z-index: 10003;
  position: absolute;
  top: 0;
  left: 0;
  background: #fff;
  border: 1px solid #f2f2f2;
  box-shadow: 4px 17px 20px 0px rgba(0, 0, 0, 0.08);
  outline: none;
  border-radius: 7px;
  overflow: hidden;
}
#_cbhtml .is-pop:hover,
.is-ui .is-pop:hover {
  z-index: 10003;
}
#_cbhtml .is-pop.arrow-top:after, #_cbhtml .is-pop.arrow-top:before,
.is-ui .is-pop.arrow-top:after,
.is-ui .is-pop.arrow-top:before {
  bottom: 100%;
  left: 25px;
  border: solid transparent;
  content: " ";
  height: 0;
  width: 0;
  position: absolute;
  pointer-events: none;
  top: auto;
}
#_cbhtml .is-pop.arrow-top:after,
.is-ui .is-pop.arrow-top:after {
  border-color: transparent;
  border-bottom-color: #fff;
  border-width: 7px;
  margin-left: -7px;
}
#_cbhtml .is-pop.arrow-top:before,
.is-ui .is-pop.arrow-top:before {
  border-color: transparent;
  border-bottom-color: #e0e0e0;
  border-width: 8px;
  margin-left: -8px;
}
#_cbhtml .is-pop.arrow-top.right:after, #_cbhtml .is-pop.arrow-top.right:before,
.is-ui .is-pop.arrow-top.right:after,
.is-ui .is-pop.arrow-top.right:before {
  left: auto;
}
#_cbhtml .is-pop.arrow-top.right:after,
.is-ui .is-pop.arrow-top.right:after {
  right: 19px;
}
#_cbhtml .is-pop.arrow-top.right:before,
.is-ui .is-pop.arrow-top.right:before {
  right: 18px;
}
#_cbhtml .is-pop.arrow-top.left:after, #_cbhtml .is-pop.arrow-top.left:before,
.is-ui .is-pop.arrow-top.left:after,
.is-ui .is-pop.arrow-top.left:before {
  right: auto;
}
#_cbhtml .is-pop.arrow-top.left:after,
.is-ui .is-pop.arrow-top.left:after {
  left: 18px;
}
#_cbhtml .is-pop.arrow-top.left:before,
.is-ui .is-pop.arrow-top.left:before {
  left: 18px;
}
#_cbhtml .is-pop.arrow-top.center:after, #_cbhtml .is-pop.arrow-top.center:before,
.is-ui .is-pop.arrow-top.center:after,
.is-ui .is-pop.arrow-top.center:before {
  left: calc(50% + 3px);
}
#_cbhtml .is-pop.arrow-left:after, #_cbhtml .is-pop.arrow-left:before,
.is-ui .is-pop.arrow-left:after,
.is-ui .is-pop.arrow-left:before {
  right: 100%;
  top: 20px;
  border: solid transparent;
  content: " ";
  height: 0;
  width: 0;
  position: absolute;
  pointer-events: none;
}
#_cbhtml .is-pop.arrow-left:after,
.is-ui .is-pop.arrow-left:after {
  border-color: transparent;
  border-right-color: #fff;
  border-width: 7px;
  margin-top: -7px;
}
#_cbhtml .is-pop.arrow-left:before,
.is-ui .is-pop.arrow-left:before {
  border-color: transparent;
  border-right-color: #e0e0e0;
  border-width: 8px;
  margin-top: -8px;
}
#_cbhtml .is-pop.arrow-left.bottom:after, #_cbhtml .is-pop.arrow-left.bottom:before,
.is-ui .is-pop.arrow-left.bottom:after,
.is-ui .is-pop.arrow-left.bottom:before {
  top: calc(100% - 28px);
}
#_cbhtml .is-pop.arrow-right:after, #_cbhtml .is-pop.arrow-right:before,
.is-ui .is-pop.arrow-right:after,
.is-ui .is-pop.arrow-right:before {
  left: 100%;
  top: 20px;
  border: solid transparent;
  content: " ";
  height: 0;
  width: 0;
  position: absolute;
  pointer-events: none;
}
#_cbhtml .is-pop.arrow-right:after,
.is-ui .is-pop.arrow-right:after {
  border-color: transparent;
  border-left-color: #fff;
  border-width: 7px;
  margin-top: -7px;
}
#_cbhtml .is-pop.arrow-right:before,
.is-ui .is-pop.arrow-right:before {
  border-color: transparent;
  border-left-color: #e0e0e0;
  border-width: 8px;
  margin-top: -8px;
}
#_cbhtml .is-pop.arrow-bottom:after, #_cbhtml .is-pop.arrow-bottom:before,
.is-ui .is-pop.arrow-bottom:after,
.is-ui .is-pop.arrow-bottom:before {
  top: 100%;
  left: calc(100% - 28px);
  border: solid transparent;
  content: " ";
  height: 0;
  width: 0;
  position: absolute;
  pointer-events: none;
}
#_cbhtml .is-pop.arrow-bottom:after,
.is-ui .is-pop.arrow-bottom:after {
  border-color: transparent;
  border-top-color: #fff;
  border-width: 7px;
  margin-left: -7px;
}
#_cbhtml .is-pop.arrow-bottom:before,
.is-ui .is-pop.arrow-bottom:before {
  border-color: transparent;
  border-top-color: #e0e0e0;
  border-width: 8px;
  margin-left: -8px;
}
#_cbhtml .is-pop.arrow-bottom.center:after, #_cbhtml .is-pop.arrow-bottom.center:before,
.is-ui .is-pop.arrow-bottom.center:after,
.is-ui .is-pop.arrow-bottom.center:before {
  left: calc(50% + 3px);
}
#_cbhtml .is-pop-tabs,
.is-ui .is-pop-tabs {
  display: flex;
  width: 100%;
  margin-bottom: 5px;
}
#_cbhtml .is-pop-tabs > div,
.is-ui .is-pop-tabs > div {
  background: #f7f7f7;
  width: 50%;
  height: 30px;
  line-height: 30px;
  box-sizing: border-box;
  text-align: center;
  font-size: 9px;
  font-weight: 400;
  text-transform: uppercase;
  color: #121212;
}
#_cbhtml .is-pop-tabs > div.is-pop-tab-item,
.is-ui .is-pop-tabs > div.is-pop-tab-item {
  cursor: pointer;
}
#_cbhtml .is-pop-tabs > div.active,
.is-ui .is-pop-tabs > div.active {
  background: transparent;
  cursor: auto;
}
#_cbhtml .is-rte-tool,
#_cbhtml .is-elementrte-tool,
.is-ui .is-rte-tool,
.is-ui .is-elementrte-tool {
  top: 25px;
}
#_cbhtml .is-rte-tool > div:not(.is-draggable),
#_cbhtml .is-elementrte-tool > div:not(.is-draggable),
.is-ui .is-rte-tool > div:not(.is-draggable),
.is-ui .is-elementrte-tool > div:not(.is-draggable) {
  padding: 8px 10px 8px 10px;
}
#_cbhtml .is-general-options,
.is-ui .is-general-options {
  color: #000;
  background: #fff;
}
#_cbhtml .is-general-options button,
#_cbhtml .is-general-options div[role=button],
.is-ui .is-general-options button,
.is-ui .is-general-options div[role=button] {
  background-color: transparent;
  color: #121212;
  width: 45px;
  height: 40px;
  margin: 0;
  box-shadow: none;
}
#_cbhtml .is-general-options button.on,
#_cbhtml .is-general-options div[role=button].on,
.is-ui .is-general-options button.on,
.is-ui .is-general-options div[role=button].on {
  background: #f1f1f1;
}
#_cbhtml .is-general-options button:hover,
#_cbhtml .is-general-options div[role=button]:hover,
.is-ui .is-general-options button:hover,
.is-ui .is-general-options div[role=button]:hover {
  background: #f1f1f1 !important;
}
#_cbhtml .is-general-options button:focus,
#_cbhtml .is-general-options div[role=button]:focus,
.is-ui .is-general-options button:focus,
.is-ui .is-general-options div[role=button]:focus {
  outline: none;
}
#_cbhtml .is-general-options button svg,
#_cbhtml .is-general-options div[role=button] svg,
.is-ui .is-general-options button svg,
.is-ui .is-general-options div[role=button] svg {
  fill: #000;
}
#_cbhtml .is-rte-tool,
#_cbhtml .is-elementrte-tool,
#_cbhtml .is-rte-pop.rte-more-options,
#_cbhtml .is-rte-pop.elementrte-more-options,
.is-ui .is-rte-tool,
.is-ui .is-elementrte-tool,
.is-ui .is-rte-pop.rte-more-options,
.is-ui .is-rte-pop.elementrte-more-options {
  z-index: 10001;
  padding: 0;
  color: #000;
  background: #fff;
  box-shadow: rgba(0, 0, 0, 0.05) 0px 5px 9px 0px;
  border-radius: 7px;
  overflow: hidden;
}
#_cbhtml .is-rte-tool div[role=button],
#_cbhtml .is-elementrte-tool div[role=button],
#_cbhtml .is-rte-pop.rte-more-options div[role=button],
#_cbhtml .is-rte-pop.elementrte-more-options div[role=button],
.is-ui .is-rte-tool div[role=button],
.is-ui .is-elementrte-tool div[role=button],
.is-ui .is-rte-pop.rte-more-options div[role=button],
.is-ui .is-rte-pop.elementrte-more-options div[role=button] {
  background-color: transparent;
  color: #121212;
  width: 45px;
  height: 40px;
  margin: 0;
  box-shadow: none;
}
#_cbhtml .is-rte-tool div[role=button].on,
#_cbhtml .is-elementrte-tool div[role=button].on,
#_cbhtml .is-rte-pop.rte-more-options div[role=button].on,
#_cbhtml .is-rte-pop.elementrte-more-options div[role=button].on,
.is-ui .is-rte-tool div[role=button].on,
.is-ui .is-elementrte-tool div[role=button].on,
.is-ui .is-rte-pop.rte-more-options div[role=button].on,
.is-ui .is-rte-pop.elementrte-more-options div[role=button].on {
  background: #f1f1f1;
}
#_cbhtml .is-rte-tool div[role=button]:hover,
#_cbhtml .is-elementrte-tool div[role=button]:hover,
#_cbhtml .is-rte-pop.rte-more-options div[role=button]:hover,
#_cbhtml .is-rte-pop.elementrte-more-options div[role=button]:hover,
.is-ui .is-rte-tool div[role=button]:hover,
.is-ui .is-elementrte-tool div[role=button]:hover,
.is-ui .is-rte-pop.rte-more-options div[role=button]:hover,
.is-ui .is-rte-pop.elementrte-more-options div[role=button]:hover {
  background: #f1f1f1;
}
#_cbhtml .is-rte-tool div[role=button]:focus,
#_cbhtml .is-elementrte-tool div[role=button]:focus,
#_cbhtml .is-rte-pop.rte-more-options div[role=button]:focus,
#_cbhtml .is-rte-pop.elementrte-more-options div[role=button]:focus,
.is-ui .is-rte-tool div[role=button]:focus,
.is-ui .is-elementrte-tool div[role=button]:focus,
.is-ui .is-rte-pop.rte-more-options div[role=button]:focus,
.is-ui .is-rte-pop.elementrte-more-options div[role=button]:focus {
  outline: none;
}
#_cbhtml .is-rte-tool div[role=button] svg,
#_cbhtml .is-elementrte-tool div[role=button] svg,
#_cbhtml .is-rte-pop.rte-more-options div[role=button] svg,
#_cbhtml .is-rte-pop.elementrte-more-options div[role=button] svg,
.is-ui .is-rte-tool div[role=button] svg,
.is-ui .is-elementrte-tool div[role=button] svg,
.is-ui .is-rte-pop.rte-more-options div[role=button] svg,
.is-ui .is-rte-pop.elementrte-more-options div[role=button] svg {
  fill: #000;
}
#_cbhtml .is-rte-tool button,
#_cbhtml .is-elementrte-tool button,
#_cbhtml .is-rte-pop.rte-more-options button,
#_cbhtml .is-rte-pop.elementrte-more-options button,
.is-ui .is-rte-tool button,
.is-ui .is-elementrte-tool button,
.is-ui .is-rte-pop.rte-more-options button,
.is-ui .is-rte-pop.elementrte-more-options button {
  background-color: transparent;
  color: #121212;
  width: 44px;
  height: 40px;
  margin: 0 2px 0 0;
  border-radius: 3px;
  box-shadow: none;
}
#_cbhtml .is-rte-tool button.on,
#_cbhtml .is-elementrte-tool button.on,
#_cbhtml .is-rte-pop.rte-more-options button.on,
#_cbhtml .is-rte-pop.elementrte-more-options button.on,
.is-ui .is-rte-tool button.on,
.is-ui .is-elementrte-tool button.on,
.is-ui .is-rte-pop.rte-more-options button.on,
.is-ui .is-rte-pop.elementrte-more-options button.on {
  background: #f1f1f1;
}
#_cbhtml .is-rte-tool button:hover,
#_cbhtml .is-elementrte-tool button:hover,
#_cbhtml .is-rte-pop.rte-more-options button:hover,
#_cbhtml .is-rte-pop.elementrte-more-options button:hover,
.is-ui .is-rte-tool button:hover,
.is-ui .is-elementrte-tool button:hover,
.is-ui .is-rte-pop.rte-more-options button:hover,
.is-ui .is-rte-pop.elementrte-more-options button:hover {
  background: #f1f1f1;
}
#_cbhtml .is-rte-tool button:focus,
#_cbhtml .is-elementrte-tool button:focus,
#_cbhtml .is-rte-pop.rte-more-options button:focus,
#_cbhtml .is-rte-pop.elementrte-more-options button:focus,
.is-ui .is-rte-tool button:focus,
.is-ui .is-elementrte-tool button:focus,
.is-ui .is-rte-pop.rte-more-options button:focus,
.is-ui .is-rte-pop.elementrte-more-options button:focus {
  outline: none;
}
#_cbhtml .is-rte-tool button svg,
#_cbhtml .is-elementrte-tool button svg,
#_cbhtml .is-rte-pop.rte-more-options button svg,
#_cbhtml .is-rte-pop.elementrte-more-options button svg,
.is-ui .is-rte-tool button svg,
.is-ui .is-elementrte-tool button svg,
.is-ui .is-rte-pop.rte-more-options button svg,
.is-ui .is-rte-pop.elementrte-more-options button svg {
  fill: #000;
}
#_cbhtml .is-rte-tool .rte-separator,
#_cbhtml .is-elementrte-tool .rte-separator,
#_cbhtml .is-rte-pop.rte-more-options .rte-separator,
#_cbhtml .is-rte-pop.elementrte-more-options .rte-separator,
.is-ui .is-rte-tool .rte-separator,
.is-ui .is-elementrte-tool .rte-separator,
.is-ui .is-rte-pop.rte-more-options .rte-separator,
.is-ui .is-rte-pop.elementrte-more-options .rte-separator {
  height: 30px;
  width: 1px;
  background: #e3e3e3;
  margin: 7px 3px 0;
}
#_cbhtml[toolbarleft] .is-rte-tool,
#_cbhtml[toolbarleft] .is-elementrte-tool,
.is-ui[toolbarleft] .is-rte-tool,
.is-ui[toolbarleft] .is-elementrte-tool {
  left: 25px;
  box-shadow: rgba(0, 0, 0, 0.05) 6px 0px 9px 0px;
}
#_cbhtml[toolbarright] .is-rte-tool,
#_cbhtml[toolbarright] .is-elementrte-tool,
.is-ui[toolbarright] .is-rte-tool,
.is-ui[toolbarright] .is-elementrte-tool {
  right: 35px;
  left: auto;
  box-shadow: rgba(0, 0, 0, 0.05) -4px 0px 9px 0px;
}
#_cbhtml[toolbarleft] .is-rte-tool > div:not(.is-draggable),
#_cbhtml[toolbarleft] .is-elementrte-tool > div:not(.is-draggable), #_cbhtml[toolbarright] .is-rte-tool > div:not(.is-draggable),
#_cbhtml[toolbarright] .is-elementrte-tool > div:not(.is-draggable),
.is-ui[toolbarleft] .is-rte-tool > div:not(.is-draggable),
.is-ui[toolbarleft] .is-elementrte-tool > div:not(.is-draggable),
.is-ui[toolbarright] .is-rte-tool > div:not(.is-draggable),
.is-ui[toolbarright] .is-elementrte-tool > div:not(.is-draggable) {
  flex-direction: column;
  padding: 8px 9px 8px 9px;
}
#_cbhtml[toolbarleft] .is-rte-tool .rte-separator,
#_cbhtml[toolbarleft] .is-elementrte-tool .rte-separator,
#_cbhtml[toolbarleft] .rte-more-options .rte-separator,
#_cbhtml[toolbarleft] .elementrte-more-options .rte-separator, #_cbhtml[toolbarright] .is-rte-tool .rte-separator,
#_cbhtml[toolbarright] .is-elementrte-tool .rte-separator,
#_cbhtml[toolbarright] .rte-more-options .rte-separator,
#_cbhtml[toolbarright] .elementrte-more-options .rte-separator,
.is-ui[toolbarleft] .is-rte-tool .rte-separator,
.is-ui[toolbarleft] .is-elementrte-tool .rte-separator,
.is-ui[toolbarleft] .rte-more-options .rte-separator,
.is-ui[toolbarleft] .elementrte-more-options .rte-separator,
.is-ui[toolbarright] .is-rte-tool .rte-separator,
.is-ui[toolbarright] .is-elementrte-tool .rte-separator,
.is-ui[toolbarright] .rte-more-options .rte-separator,
.is-ui[toolbarright] .elementrte-more-options .rte-separator {
  height: 1px;
  width: 34px;
  margin: 3px 0 3px 7px;
}
#_cbhtml[toolbarfull] .is-rte-tool,
#_cbhtml[toolbarfull] .is-elementrte-tool,
.is-ui[toolbarfull] .is-rte-tool,
.is-ui[toolbarfull] .is-elementrte-tool {
  top: 0;
  left: 0;
  width: 100vw;
  align-items: center;
  box-shadow: rgba(0, 0, 0, 0.07) -1px 1px 0px 0px;
}
#_cbhtml[toolbarfull] .is-rte-pop,
.is-ui[toolbarfull] .is-rte-pop {
  box-shadow: rgba(0, 0, 0, 0.05) 0px 1px 0px 1px;
}
#_cbhtml .is-rte-pop,
.is-ui .is-rte-pop {
  z-index: 10002;
  display: none;
  position: fixed;
  height: 0;
  border: none;
  color: #000;
  background: #fff;
  box-shadow: rgba(0, 0, 0, 0.07) 0px 7px 12px 0px;
  box-sizing: border-box;
  overflow: hidden;
  outline: none;
  border-radius: 2px;
  overflow: hidden;
}
#_cbhtml .is-rte-pop > div,
.is-ui .is-rte-pop > div {
  display: flex;
  padding: 1px 9px 9px 9px;
}
#_cbhtml .is-rte-pop button,
.is-ui .is-rte-pop button {
  width: 46px;
  height: 40px;
  margin: 0;
  background-color: transparent;
  box-shadow: none;
}
#_cbhtml .is-rte-pop button.on,
.is-ui .is-rte-pop button.on {
  background: #f1f1f1;
}
#_cbhtml .is-rte-pop button:hover,
.is-ui .is-rte-pop button:hover {
  background: #f1f1f1;
}
#_cbhtml .is-rte-pop .is-label,
.is-ui .is-rte-pop .is-label {
  font-size: 9px;
  font-weight: bold;
  text-transform: uppercase;
  line-height: 2;
  padding: 6px 0 1px;
  text-align: center;
}
#_cbhtml .is-rte-pop .is-label.separator,
.is-ui .is-rte-pop .is-label.separator {
  margin-top: 5px;
  border-top: #f0f0f0 1px solid;
}
#_cbhtml .is-rte-pop.active,
.is-ui .is-rte-pop.active {
  animation-name: formatting-slide-out;
  animation-duration: 100ms;
  animation-fill-mode: forwards;
}
@keyframes formatting-slide-out {
  from {
    height: 0px;
  }
  to {
    height: 49px;
  }
}
#_cbhtml .is-rte-pop.deactive,
.is-ui .is-rte-pop.deactive {
  animation-name: formatting-slide-in;
  animation-duration: 100ms;
  animation-fill-mode: forwards;
}
@keyframes formatting-slide-in {
  from {
    height: 49px;
  }
  to {
    height: 0px;
  }
}
#_cbhtml .is-rte-pop.rte-paragraph-options,
.is-ui .is-rte-pop.rte-paragraph-options {
  overflow: hidden;
}
#_cbhtml .is-rte-pop.rte-paragraph-options.active,
.is-ui .is-rte-pop.rte-paragraph-options.active {
  animation-name: paragraph-slide-out;
}
@keyframes paragraph-slide-out {
  from {
    height: 0;
  }
  to {
    height: 277px;
  }
}
#_cbhtml .is-rte-pop.rte-paragraph-options.deactive,
.is-ui .is-rte-pop.rte-paragraph-options.deactive {
  animation-name: paragraph-slide-in;
}
@keyframes paragraph-slide-in {
  from {
    height: 277px;
  }
  to {
    height: 0;
  }
}
#_cbhtml .is-rte-pop.rte-paragraph-options li.on,
.is-ui .is-rte-pop.rte-paragraph-options li.on {
  background: #f1f1f1;
}
#_cbhtml .is-rte-pop.rte-paragraph-options > ul,
.is-ui .is-rte-pop.rte-paragraph-options > ul {
  width: 242px;
  padding: 1px 9px 9px;
  box-sizing: border-box;
  overflow: hidden;
  flex-direction: column;
  list-style: none;
  padding: 0;
  margin: 0;
}
#_cbhtml .is-rte-pop.rte-paragraph-options > ul > li,
.is-ui .is-rte-pop.rte-paragraph-options > ul > li {
  cursor: pointer;
  overflow: hidden;
  padding: 5px 0;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  outline: none;
}
#_cbhtml .is-rte-pop.rte-paragraph-options > ul > li:hover, #_cbhtml .is-rte-pop.rte-paragraph-options > ul > li:focus,
.is-ui .is-rte-pop.rte-paragraph-options > ul > li:hover,
.is-ui .is-rte-pop.rte-paragraph-options > ul > li:focus {
  background: #f1f1f1;
}
#_cbhtml .is-rte-pop.rte-paragraph-options > ul > li > *,
.is-ui .is-rte-pop.rte-paragraph-options > ul > li > * {
  text-transform: none !important;
  margin: 0 !important;
  line-height: 1.45 !important;
  text-align: center;
  white-space: nowrap;
  pointer-events: none;
}
#_cbhtml .is-rte-pop.rte-paragraph-options > ul > li > *:not(p),
.is-ui .is-rte-pop.rte-paragraph-options > ul > li > *:not(p) {
  transform: scale(0.8);
}
#_cbhtml .is-rte-pop.rte-textsetting-options > div,
.is-ui .is-rte-pop.rte-textsetting-options > div {
  width: 233px;
  flex-direction: column;
  padding: 1px 10px 12px 13px;
  box-sizing: border-box;
}
#_cbhtml .is-rte-pop.rte-textsetting-options button,
.is-ui .is-rte-pop.rte-textsetting-options button {
  width: 39px;
  height: 28.2px;
  margin: 1px;
  box-shadow: none;
  background: transparent;
  font-size: 12px;
}
#_cbhtml .is-rte-pop.rte-textsetting-options button.on,
.is-ui .is-rte-pop.rte-textsetting-options button.on {
  background: #f1f1f1;
}
#_cbhtml .is-rte-pop.rte-textsetting-options button:hover,
.is-ui .is-rte-pop.rte-textsetting-options button:hover {
  background: #f1f1f1;
}
#_cbhtml .is-rte-pop.rte-textsetting-options button:focus,
.is-ui .is-rte-pop.rte-textsetting-options button:focus {
  background: #f1f1f1;
}
#_cbhtml .is-rte-pop.rte-textsetting-options.active,
.is-ui .is-rte-pop.rte-textsetting-options.active {
  animation-name: textsetting-slide-out;
}
@keyframes textsetting-slide-out {
  from {
    height: 0;
  }
  to {
    height: 372px;
  }
}
#_cbhtml .is-rte-pop.rte-textsetting-options.deactive,
.is-ui .is-rte-pop.rte-textsetting-options.deactive {
  animation-name: textsetting-slide-in;
}
@keyframes textsetting-slide-in {
  from {
    height: 372px;
  }
  to {
    height: 0;
  }
}
#_cbhtml .is-rte-pop.rte-textsetting-options.simple > div,
.is-ui .is-rte-pop.rte-textsetting-options.simple > div {
  width: 224px;
  padding: 1px 12px 12px 12px;
}
#_cbhtml .is-rte-pop.rte-textsetting-options.simple button,
.is-ui .is-rte-pop.rte-textsetting-options.simple button {
  width: 38px;
}
#_cbhtml .is-rte-pop.rte-textsetting-options.simple .label-fontweight,
#_cbhtml .is-rte-pop.rte-textsetting-options.simple .rte-fontweight-options,
.is-ui .is-rte-pop.rte-textsetting-options.simple .label-fontweight,
.is-ui .is-rte-pop.rte-textsetting-options.simple .rte-fontweight-options {
  display: none !important;
}
#_cbhtml .is-rte-pop.rte-textsetting-options.simple .is-label,
.is-ui .is-rte-pop.rte-textsetting-options.simple .is-label {
  padding: 8px 0 2px;
}
#_cbhtml .is-rte-pop.rte-textsetting-options.simple .is-label.separator,
.is-ui .is-rte-pop.rte-textsetting-options.simple .is-label.separator {
  margin-top: 5px;
}
#_cbhtml .is-rte-pop.rte-textsetting-options.simple.active,
.is-ui .is-rte-pop.rte-textsetting-options.simple.active {
  animation-name: textsetting2-slide-out;
}
#_cbhtml .is-rte-pop.rte-textsetting-options.simple.deactive,
.is-ui .is-rte-pop.rte-textsetting-options.simple.deactive {
  animation-name: textsetting2-slide-in;
}
@keyframes textsetting2-slide-out {
  from {
    height: 0;
  }
  to {
    height: 288px;
  }
}
@keyframes textsetting2-slide-in {
  from {
    height: 288px;
  }
  to {
    height: 0;
  }
}
#_cbhtml .is-rte-pop.rte-formatting-options button,
.is-ui .is-rte-pop.rte-formatting-options button {
  box-shadow: none;
  background: transparent;
}
#_cbhtml .is-rte-pop.rte-formatting-options button.on,
.is-ui .is-rte-pop.rte-formatting-options button.on {
  background: #f1f1f1;
}
#_cbhtml .is-rte-pop.rte-formatting-options button:hover,
.is-ui .is-rte-pop.rte-formatting-options button:hover {
  background: #f1f1f1;
}
#_cbhtml .is-rte-pop.rte-list-options button,
.is-ui .is-rte-pop.rte-list-options button {
  box-shadow: none;
  background: transparent;
}
#_cbhtml .is-rte-pop.rte-list-options button.on,
.is-ui .is-rte-pop.rte-list-options button.on {
  background: #f1f1f1;
}
#_cbhtml .is-rte-pop.rte-list-options button:hover,
.is-ui .is-rte-pop.rte-list-options button:hover {
  background: #f1f1f1;
}
#_cbhtml .is-rte-pop.rte-align-options button,
.is-ui .is-rte-pop.rte-align-options button {
  box-shadow: none;
  background: transparent;
}
#_cbhtml .is-rte-pop.rte-align-options button.on,
.is-ui .is-rte-pop.rte-align-options button.on {
  background: #f1f1f1;
}
#_cbhtml .is-rte-pop.rte-align-options button:hover,
.is-ui .is-rte-pop.rte-align-options button:hover {
  background: #f1f1f1;
}
#_cbhtml .is-rte-pop.rte-color-picker,
.is-ui .is-rte-pop.rte-color-picker {
  width: 240px;
  /* new colorpicker */
  flex-direction: column;
}
#_cbhtml .is-rte-pop.rte-color-picker > div,
.is-ui .is-rte-pop.rte-color-picker > div {
  padding: 0;
}
#_cbhtml .is-rte-pop.rte-color-picker button,
.is-ui .is-rte-pop.rte-color-picker button {
  background-color: transparent;
  box-shadow: 0px 3px 6px -6px rgba(0, 0, 0, 0.32);
  border-radius: 0px;
}
#_cbhtml .is-rte-pop.rte-color-picker button:hover,
.is-ui .is-rte-pop.rte-color-picker button:hover {
  background: transparent;
}
#_cbhtml .is-rte-pop.rte-color-picker button:focus,
.is-ui .is-rte-pop.rte-color-picker button:focus {
  outline-offset: -2px;
}
#_cbhtml .is-rte-pop.rte-color-picker input[type=text]:focus,
.is-ui .is-rte-pop.rte-color-picker input[type=text]:focus {
  outline: #3e93f7 2px solid;
  outline-offset: -2px;
}
#_cbhtml .is-rte-pop.rte-color-picker .color-default button,
#_cbhtml .is-rte-pop.rte-color-picker .color-gradient button,
.is-ui .is-rte-pop.rte-color-picker .color-default button,
.is-ui .is-rte-pop.rte-color-picker .color-gradient button {
  outline: none;
}
#_cbhtml .is-rte-pop.rte-color-picker.active,
.is-ui .is-rte-pop.rte-color-picker.active {
  animation-name: colorpicker-slide-out;
  animation-duration: 200ms;
  /* new colorpicker */
}
@keyframes colorpicker-slide-out {
  from {
    height: 0;
  }
  to {
    height: 390px;
  }
  /* 445px */
  /* new colorpicker */
}
#_cbhtml .is-rte-pop.rte-color-picker.deactive,
.is-ui .is-rte-pop.rte-color-picker.deactive {
  animation-name: colorpicker-slide-in;
}
@keyframes colorpicker-slide-in {
  from {
    height: 390px;
  }
  /* new colorpicker */
  to {
    height: 0;
  }
}
#_cbhtml .is-rte-pop.rte-color-picker .is-pop-tabs,
.is-ui .is-rte-pop.rte-color-picker .is-pop-tabs {
  padding: 3px 12px 0;
  box-sizing: border-box;
}
#_cbhtml .is-rte-pop.rte-color-picker .rte-color-picker-area > div,
.is-ui .is-rte-pop.rte-color-picker .rte-color-picker-area > div {
  padding-top: 5px !important;
}
#_cbhtml .is-rte-pop.rte-color-picker._4lines.active,
.is-ui .is-rte-pop.rte-color-picker._4lines.active {
  animation-name: colorpicker-slide-out-4lines;
}
@keyframes colorpicker-slide-out-4lines {
  from {
    height: 0;
  }
  to {
    height: 420px;
  }
}
#_cbhtml .is-rte-pop.rte-color-picker._4lines.deactive,
.is-ui .is-rte-pop.rte-color-picker._4lines.deactive {
  animation-name: colorpicker-slide-in-4lines;
}
@keyframes colorpicker-slide-in-4lines {
  from {
    height: 420px;
  }
  to {
    height: 0;
  }
}
#_cbhtml .is-rte-pop.rte-color-picker._5lines.active,
.is-ui .is-rte-pop.rte-color-picker._5lines.active {
  animation-name: colorpicker-slide-out-5lines;
}
@keyframes colorpicker-slide-out-5lines {
  from {
    height: 0;
  }
  to {
    height: 451px;
  }
}
#_cbhtml .is-rte-pop.rte-color-picker._5lines.deactive,
.is-ui .is-rte-pop.rte-color-picker._5lines.deactive {
  animation-name: colorpicker-slide-in-5lines;
}
@keyframes colorpicker-slide-in-5lines {
  from {
    height: 451px;
  }
  to {
    height: 0;
  }
}
#_cbhtml .is-rte-pop.rte-icon-options,
.is-ui .is-rte-pop.rte-icon-options {
  width: 270px;
}
#_cbhtml .is-rte-pop.rte-icon-options #inpRteIconSearch,
.is-ui .is-rte-pop.rte-icon-options #inpRteIconSearch {
  height: 40px;
  flex: none;
}
#_cbhtml .is-rte-pop.rte-icon-options > div,
.is-ui .is-rte-pop.rte-icon-options > div {
  flex-direction: column;
  width: 100%;
  padding: 0;
}
#_cbhtml .is-rte-pop.rte-icon-options > div .div-icon-list,
.is-ui .is-rte-pop.rte-icon-options > div .div-icon-list {
  width: 100%;
  height: 100%;
  display: flex;
  align-content: flex-start;
  flex-wrap: wrap;
  overflow-y: auto;
  overflow-x: hidden;
}
#_cbhtml .is-rte-pop.rte-icon-options > div .div-icon-list button,
.is-ui .is-rte-pop.rte-icon-options > div .div-icon-list button {
  font-size: 16px;
  width: 50px;
  height: 40px;
  outline-offset: -2px;
}
#_cbhtml .is-rte-pop.rte-icon-options.active,
.is-ui .is-rte-pop.rte-icon-options.active {
  animation-name: icon-slide-out;
}
@keyframes icon-slide-out {
  from {
    height: 0;
  }
  to {
    height: 280px;
  }
}
#_cbhtml .is-rte-pop.rte-icon-options.deactive,
.is-ui .is-rte-pop.rte-icon-options.deactive {
  animation-name: icon-slide-in;
}
@keyframes icon-slide-in {
  from {
    height: 280px;
  }
  to {
    height: 0;
  }
}
#_cbhtml .is-rte-pop.rte-fontfamily-options,
.is-ui .is-rte-pop.rte-fontfamily-options {
  width: 260px;
}
#_cbhtml .is-rte-pop.rte-fontfamily-options #inpRteFontSearch,
.is-ui .is-rte-pop.rte-fontfamily-options #inpRteFontSearch {
  height: 40px;
  flex: none;
  text-align: center;
}
#_cbhtml .is-rte-pop.rte-fontfamily-options > div,
.is-ui .is-rte-pop.rte-fontfamily-options > div {
  flex-direction: column;
  width: 100%;
  padding: 0;
}
#_cbhtml .is-rte-pop.rte-fontfamily-options > div .div-font-list,
.is-ui .is-rte-pop.rte-fontfamily-options > div .div-font-list {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  overflow-x: hidden;
}
#_cbhtml .is-rte-pop.rte-fontfamily-options > div .div-font-list button,
.is-ui .is-rte-pop.rte-fontfamily-options > div .div-font-list button {
  font-size: 16px;
  width: 100%;
  height: 40px;
  outline-offset: -2px;
  flex: none;
}
#_cbhtml .is-rte-pop.rte-fontfamily-options > div .div-font-list button img,
.is-ui .is-rte-pop.rte-fontfamily-options > div .div-font-list button img {
  height: 24px;
  pointer-events: none;
}
#_cbhtml .is-rte-pop.rte-fontfamily-options > div .div-font-list button.hidden,
.is-ui .is-rte-pop.rte-fontfamily-options > div .div-font-list button.hidden {
  display: none;
}
#_cbhtml .is-rte-pop.rte-fontfamily-options iframe,
.is-ui .is-rte-pop.rte-fontfamily-options iframe {
  margin: 1px 0 0;
  width: 100%;
  max-width: 260px;
  height: 100%;
  border: none;
}
#_cbhtml .is-rte-pop.rte-fontfamily-options.active,
.is-ui .is-rte-pop.rte-fontfamily-options.active {
  animation-name: fontfamily-slide-out;
}
@keyframes fontfamily-slide-out {
  from {
    height: 0;
  }
  to {
    height: 263px;
  }
}
#_cbhtml .is-rte-pop.rte-fontfamily-options.deactive,
.is-ui .is-rte-pop.rte-fontfamily-options.deactive {
  animation-name: fontfamily-slide-in;
}
@keyframes fontfamily-slide-in {
  from {
    height: 263px;
  }
  to {
    height: 0;
  }
}
#_cbhtml .is-rte-pop.rte-customtag-options.active,
.is-ui .is-rte-pop.rte-customtag-options.active {
  animation-name: customtag-slide-out;
}
@keyframes customtag-slide-out {
  from {
    height: 0;
  }
  to {
    height: 125px;
  }
}
#_cbhtml .is-rte-pop.rte-customtag-options.deactive,
.is-ui .is-rte-pop.rte-customtag-options.deactive {
  animation-name: customtag-slide-in;
}
@keyframes customtag-slide-in {
  from {
    height: 125px;
  }
  to {
    height: 0;
  }
}
#_cbhtml .is-rte-pop.rte-customtag-options > div,
.is-ui .is-rte-pop.rte-customtag-options > div {
  width: 180px;
  padding: 1px 9px 9px;
  box-sizing: border-box;
  overflow-x: hidden;
  overflow-y: auto;
  flex-direction: column;
}
#_cbhtml .is-rte-pop.rte-customtag-options > div button,
.is-ui .is-rte-pop.rte-customtag-options > div button {
  font-size: 11px;
  width: 100%;
  box-shadow: none;
  background: transparent;
  flex: none;
}
#_cbhtml .is-rte-pop.rte-customtag-options > div button:hover,
.is-ui .is-rte-pop.rte-customtag-options > div button:hover {
  background: #f1f1f1;
}
#_cbhtml .is-rte-pop.rte-customtag-options > div button:focus,
.is-ui .is-rte-pop.rte-customtag-options > div button:focus {
  background: #f1f1f1;
}
#_cbhtml .is-rte-pop.rte-zoom-options > div,
.is-ui .is-rte-pop.rte-zoom-options > div {
  width: 224px;
  flex-direction: column;
  padding: 1px 12px 12px 12px;
  box-sizing: border-box;
}
#_cbhtml .is-rte-pop.rte-zoom-options.active,
.is-ui .is-rte-pop.rte-zoom-options.active {
  animation-name: zoomsetting-slide-out;
}
@keyframes zoomsetting-slide-out {
  from {
    height: 0;
  }
  to {
    height: 95px;
  }
}
#_cbhtml .is-rte-pop.rte-zoom-options.deactive,
.is-ui .is-rte-pop.rte-zoom-options.deactive {
  animation-name: zoomsetting-slide-in;
}
@keyframes zoomsetting-slide-in {
  from {
    height: 78px;
  }
  to {
    height: 0;
  }
}
#_cbhtml[toolbarleft] .is-rte-pop,
.is-ui[toolbarleft] .is-rte-pop {
  height: auto;
  width: 0;
  flex-direction: column;
  box-shadow: rgba(0, 0, 0, 0.05) 5px 0px 9px 0px;
}
#_cbhtml[toolbarleft] .is-rte-pop > div,
.is-ui[toolbarleft] .is-rte-pop > div {
  flex-direction: column;
  padding: 9px 9px 9px 1px;
}
#_cbhtml[toolbarleft] .is-rte-pop.active,
.is-ui[toolbarleft] .is-rte-pop.active {
  animation-name: formatting-leftslide-out;
  animation-duration: 0.1s;
  animation-fill-mode: forwards;
}
@keyframes formatting-leftslide-out {
  from {
    width: 0;
  }
  to {
    width: 55px;
  }
}
#_cbhtml[toolbarleft] .is-rte-pop.deactive,
.is-ui[toolbarleft] .is-rte-pop.deactive {
  animation-name: formatting-leftslide-in;
  animation-duration: 0.1s;
  animation-fill-mode: forwards;
}
@keyframes formatting-leftslide-in {
  from {
    width: 55px;
  }
  to {
    width: 0;
  }
}
#_cbhtml[toolbarleft] .is-rte-pop.rte-paragraph-options.active,
.is-ui[toolbarleft] .is-rte-pop.rte-paragraph-options.active {
  animation-name: paragraph-leftslide-out;
}
@keyframes paragraph-leftslide-out {
  from {
    width: 0;
  }
  to {
    width: 250px;
  }
}
#_cbhtml[toolbarleft] .is-rte-pop.rte-paragraph-options.deactive,
.is-ui[toolbarleft] .is-rte-pop.rte-paragraph-options.deactive {
  animation-name: paragraph-leftslide-in;
}
@keyframes paragraph-leftslide-in {
  from {
    width: 250px;
  }
  to {
    width: 0;
  }
}
#_cbhtml[toolbarleft] .is-rte-pop.rte-paragraph-options > div,
.is-ui[toolbarleft] .is-rte-pop.rte-paragraph-options > div {
  width: 245px;
  padding: 9px;
}
#_cbhtml[toolbarleft] .is-rte-pop.rte-textsetting-options.active,
.is-ui[toolbarleft] .is-rte-pop.rte-textsetting-options.active {
  animation-name: textsetting-leftslide-out;
}
@keyframes textsetting-leftslide-out {
  from {
    width: 0;
  }
  to {
    width: 213px;
  }
}
#_cbhtml[toolbarleft] .is-rte-pop.rte-textsetting-options.deactive,
.is-ui[toolbarleft] .is-rte-pop.rte-textsetting-options.deactive {
  animation-name: textsetting-leftslide-in;
}
@keyframes textsetting-leftslide-in {
  from {
    width: 213px;
  }
  to {
    width: 0;
  }
}
#_cbhtml[toolbarleft] .is-rte-pop.rte-fontfamily-options,
.is-ui[toolbarleft] .is-rte-pop.rte-fontfamily-options {
  height: 260px;
}
#_cbhtml[toolbarleft] .is-rte-pop.rte-fontfamily-options #inpRteFontSearch,
.is-ui[toolbarleft] .is-rte-pop.rte-fontfamily-options #inpRteFontSearch {
  height: 40px;
  flex: none;
  text-align: center;
}
#_cbhtml[toolbarleft] .is-rte-pop.rte-fontfamily-options > div,
.is-ui[toolbarleft] .is-rte-pop.rte-fontfamily-options > div {
  flex-direction: column;
  width: 100%;
  height: 320px;
  padding: 0;
}
#_cbhtml[toolbarleft] .is-rte-pop.rte-fontfamily-options > div .div-font-list,
.is-ui[toolbarleft] .is-rte-pop.rte-fontfamily-options > div .div-font-list {
  width: 100%;
  height: 220px;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  overflow-x: hidden;
}
#_cbhtml[toolbarleft] .is-rte-pop.rte-fontfamily-options > div .div-font-list button,
.is-ui[toolbarleft] .is-rte-pop.rte-fontfamily-options > div .div-font-list button {
  font-size: 16px;
  width: 100%;
  height: 40px;
  outline-offset: -2px;
  flex: none;
}
#_cbhtml[toolbarleft] .is-rte-pop.rte-fontfamily-options > div .div-font-list button img,
.is-ui[toolbarleft] .is-rte-pop.rte-fontfamily-options > div .div-font-list button img {
  height: 24px;
  pointer-events: none;
}
#_cbhtml[toolbarleft] .is-rte-pop.rte-fontfamily-options > div .div-font-list button.hidden,
.is-ui[toolbarleft] .is-rte-pop.rte-fontfamily-options > div .div-font-list button.hidden {
  display: none;
}
#_cbhtml[toolbarleft] .is-rte-pop.rte-fontfamily-options iframe,
.is-ui[toolbarleft] .is-rte-pop.rte-fontfamily-options iframe {
  margin: 9px 0 9px 0;
}
#_cbhtml[toolbarleft] .is-rte-pop.rte-fontfamily-options.active,
.is-ui[toolbarleft] .is-rte-pop.rte-fontfamily-options.active {
  animation-name: fontfamily-leftslide-out;
}
@keyframes fontfamily-leftslide-out {
  from {
    width: 0;
  }
  to {
    width: 260px;
  }
}
#_cbhtml[toolbarleft] .is-rte-pop.rte-fontfamily-options.deactive,
.is-ui[toolbarleft] .is-rte-pop.rte-fontfamily-options.deactive {
  animation-name: fontfamily-leftslide-in;
}
@keyframes fontfamily-leftslide-in {
  from {
    width: 260px;
  }
  to {
    width: 0;
  }
}
#_cbhtml[toolbarleft] .is-rte-pop.rte-customtag-options > div,
.is-ui[toolbarleft] .is-rte-pop.rte-customtag-options > div {
  width: 180px;
  height: 125px;
  padding: 9px 9px 9px;
}
#_cbhtml[toolbarleft] .is-rte-pop.rte-customtag-options.active,
.is-ui[toolbarleft] .is-rte-pop.rte-customtag-options.active {
  animation-name: customtag-leftslide-out;
}
@keyframes customtag-leftslide-out {
  from {
    width: 0;
  }
  to {
    width: 180px;
  }
}
#_cbhtml[toolbarleft] .is-rte-pop.rte-customtag-options.deactive,
.is-ui[toolbarleft] .is-rte-pop.rte-customtag-options.deactive {
  animation-name: customtag-leftslide-in;
}
@keyframes customtag-leftslide-in {
  from {
    width: 180px;
  }
  to {
    width: 0;
  }
}
#_cbhtml[toolbarleft] .is-rte-pop.rte-color-picker,
.is-ui[toolbarleft] .is-rte-pop.rte-color-picker {
  height: 452px;
}
#_cbhtml[toolbarleft] .is-rte-pop.rte-color-picker.active,
.is-ui[toolbarleft] .is-rte-pop.rte-color-picker.active {
  animation-name: colorpicker-leftslide-out;
}
@keyframes colorpicker-leftslide-out {
  from {
    width: 0;
  }
  to {
    width: 270px;
  }
}
#_cbhtml[toolbarleft] .is-rte-pop.rte-color-picker.deactive,
.is-ui[toolbarleft] .is-rte-pop.rte-color-picker.deactive {
  animation-name: colorpicker-leftslide-in;
}
@keyframes colorpicker-leftslide-in {
  from {
    width: 270px;
  }
  to {
    width: 0;
  }
}
#_cbhtml[toolbarleft] .is-rte-pop.rte-color-picker .is-pop-tabs,
.is-ui[toolbarleft] .is-rte-pop.rte-color-picker .is-pop-tabs {
  flex-direction: row;
  padding: 11px 12px 0;
}
#_cbhtml[toolbarleft] .is-rte-pop.rte-color-picker .rte-color-picker-area,
.is-ui[toolbarleft] .is-rte-pop.rte-color-picker .rte-color-picker-area {
  padding: 0;
}
#_cbhtml[toolbarleft] .is-rte-pop.rte-icon-options,
.is-ui[toolbarleft] .is-rte-pop.rte-icon-options {
  height: 320px;
}
#_cbhtml[toolbarleft] .is-rte-pop.rte-icon-options #inpRteIconSearch,
.is-ui[toolbarleft] .is-rte-pop.rte-icon-options #inpRteIconSearch {
  height: 40px;
  flex: none;
}
#_cbhtml[toolbarleft] .is-rte-pop.rte-icon-options > div,
.is-ui[toolbarleft] .is-rte-pop.rte-icon-options > div {
  flex-direction: column;
  width: 100%;
  height: 320px;
  padding: 0;
}
#_cbhtml[toolbarleft] .is-rte-pop.rte-icon-options > div .div-icon-list,
.is-ui[toolbarleft] .is-rte-pop.rte-icon-options > div .div-icon-list {
  width: 100%;
  height: 100%;
  display: flex;
  align-content: flex-start;
  flex-wrap: wrap;
  overflow-y: auto;
  overflow-x: hidden;
}
#_cbhtml[toolbarleft] .is-rte-pop.rte-icon-options > div .div-icon-list button,
.is-ui[toolbarleft] .is-rte-pop.rte-icon-options > div .div-icon-list button {
  font-size: 16px;
  width: 50px;
  height: 40px;
  outline-offset: -2px;
}
#_cbhtml[toolbarleft] .is-rte-pop.rte-icon-options iframe,
.is-ui[toolbarleft] .is-rte-pop.rte-icon-options iframe {
  margin: 9px 0 9px 0;
}
#_cbhtml[toolbarleft] .is-rte-pop.rte-icon-options.active,
.is-ui[toolbarleft] .is-rte-pop.rte-icon-options.active {
  animation-name: icon-leftslide-out;
}
@keyframes icon-leftslide-out {
  from {
    width: 0;
  }
  to {
    width: 280px;
  }
}
#_cbhtml[toolbarleft] .is-rte-pop.rte-icon-options.deactive,
.is-ui[toolbarleft] .is-rte-pop.rte-icon-options.deactive {
  animation-name: icon-leftslide-in;
}
@keyframes icon-leftslide-in {
  from {
    width: 280px;
  }
  to {
    width: 0;
  }
}
#_cbhtml[toolbarleft] .is-rte-pop.rte-zoom-options > div,
.is-ui[toolbarleft] .is-rte-pop.rte-zoom-options > div {
  width: 224px;
  flex-direction: column;
  padding: 1px 12px 12px 12px;
  box-sizing: border-box;
}
#_cbhtml[toolbarleft] .is-rte-pop.rte-zoom-options.active,
.is-ui[toolbarleft] .is-rte-pop.rte-zoom-options.active {
  animation-name: zoomsetting-leftslide-out;
}
@keyframes zoomsetting-leftslide-out {
  from {
    width: 0;
  }
  to {
    width: 225px;
  }
}
#_cbhtml[toolbarleft] .is-rte-pop.rte-zoom-options.deactive,
.is-ui[toolbarleft] .is-rte-pop.rte-zoom-options.deactive {
  animation-name: zoomsetting-leftslide-in;
}
@keyframes zoomsetting-leftslide-in {
  from {
    width: 225px;
  }
  to {
    width: 0;
  }
}
#_cbhtml[toolbarright] .is-rte-pop,
.is-ui[toolbarright] .is-rte-pop {
  height: auto;
  width: 0;
  flex-direction: column;
  box-shadow: rgba(0, 0, 0, 0.05) -6px 1px 9px 0px;
}
#_cbhtml[toolbarright] .is-rte-pop > div,
.is-ui[toolbarright] .is-rte-pop > div {
  flex-direction: column;
  padding: 9px 2px 9px 9px;
}
#_cbhtml[toolbarright] .is-rte-pop.active,
.is-ui[toolbarright] .is-rte-pop.active {
  animation-name: formatting-rightslide-out;
  animation-duration: 0.1s;
  animation-fill-mode: forwards;
}
@keyframes formatting-rightslide-out {
  from {
    width: 0;
  }
  to {
    width: 55px;
  }
}
#_cbhtml[toolbarright] .is-rte-pop.deactive,
.is-ui[toolbarright] .is-rte-pop.deactive {
  animation-name: formatting-rightslide-in;
  animation-duration: 0.1s;
  animation-fill-mode: forwards;
}
@keyframes formatting-rightslide-in {
  from {
    width: 55px;
  }
  to {
    width: 0;
  }
}
#_cbhtml[toolbarright] .is-rte-pop.rte-paragraph-options.active,
.is-ui[toolbarright] .is-rte-pop.rte-paragraph-options.active {
  animation-name: paragraph-rightslide-out;
}
@keyframes paragraph-rightslide-out {
  from {
    width: 0;
  }
  to {
    width: 250px;
  }
}
#_cbhtml[toolbarright] .is-rte-pop.rte-paragraph-options.deactive,
.is-ui[toolbarright] .is-rte-pop.rte-paragraph-options.deactive {
  animation-name: paragraph-rightslide-in;
}
@keyframes paragraph-rightslide-in {
  from {
    width: 250px;
  }
  to {
    width: 0;
  }
}
#_cbhtml[toolbarright] .is-rte-pop.rte-paragraph-options > div,
.is-ui[toolbarright] .is-rte-pop.rte-paragraph-options > div {
  width: 245px;
  padding: 9px;
}
#_cbhtml[toolbarright] .is-rte-pop.rte-textsetting-options.active,
.is-ui[toolbarright] .is-rte-pop.rte-textsetting-options.active {
  animation-name: textsetting-rightslide-out;
}
@keyframes textsetting-rightslide-out {
  from {
    width: 0;
  }
  to {
    width: 225px;
  }
}
#_cbhtml[toolbarright] .is-rte-pop.rte-textsetting-options.deactive,
.is-ui[toolbarright] .is-rte-pop.rte-textsetting-options.deactive {
  animation-name: textsetting-rightslide-in;
}
@keyframes textsetting-rightslide-in {
  from {
    width: 225px;
  }
  to {
    width: 0;
  }
}
#_cbhtml[toolbarright] .is-rte-pop.rte-fontfamily-options,
.is-ui[toolbarright] .is-rte-pop.rte-fontfamily-options {
  height: 260px;
}
#_cbhtml[toolbarright] .is-rte-pop.rte-fontfamily-options #inpRteFontSearch,
.is-ui[toolbarright] .is-rte-pop.rte-fontfamily-options #inpRteFontSearch {
  height: 40px;
  flex: none;
  text-align: center;
}
#_cbhtml[toolbarright] .is-rte-pop.rte-fontfamily-options > div,
.is-ui[toolbarright] .is-rte-pop.rte-fontfamily-options > div {
  flex-direction: column;
  width: 100%;
  height: 320px;
  padding: 0;
}
#_cbhtml[toolbarright] .is-rte-pop.rte-fontfamily-options > div .div-font-list,
.is-ui[toolbarright] .is-rte-pop.rte-fontfamily-options > div .div-font-list {
  width: 100%;
  height: 220px;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  overflow-x: hidden;
}
#_cbhtml[toolbarright] .is-rte-pop.rte-fontfamily-options > div .div-font-list button,
.is-ui[toolbarright] .is-rte-pop.rte-fontfamily-options > div .div-font-list button {
  font-size: 16px;
  width: 100%;
  height: 40px;
  outline-offset: -2px;
  flex: none;
}
#_cbhtml[toolbarright] .is-rte-pop.rte-fontfamily-options > div .div-font-list button img,
.is-ui[toolbarright] .is-rte-pop.rte-fontfamily-options > div .div-font-list button img {
  height: 24px;
  pointer-events: none;
}
#_cbhtml[toolbarright] .is-rte-pop.rte-fontfamily-options > div .div-font-list button.hidden,
.is-ui[toolbarright] .is-rte-pop.rte-fontfamily-options > div .div-font-list button.hidden {
  display: none;
}
#_cbhtml[toolbarright] .is-rte-pop.rte-fontfamily-options iframe,
.is-ui[toolbarright] .is-rte-pop.rte-fontfamily-options iframe {
  margin: 9px 0 9px 0;
}
#_cbhtml[toolbarright] .is-rte-pop.rte-fontfamily-options.active,
.is-ui[toolbarright] .is-rte-pop.rte-fontfamily-options.active {
  animation-name: fontfamily-leftslide-out;
}
@keyframes fontfamily-leftslide-out {
  from {
    width: 0;
  }
  to {
    width: 260px;
  }
}
#_cbhtml[toolbarright] .is-rte-pop.rte-fontfamily-options.deactive,
.is-ui[toolbarright] .is-rte-pop.rte-fontfamily-options.deactive {
  animation-name: fontfamily-leftslide-in;
}
@keyframes fontfamily-leftslide-in {
  from {
    width: 260px;
  }
  to {
    width: 0;
  }
}
#_cbhtml[toolbarright] .is-rte-pop.rte-customtag-options > div,
.is-ui[toolbarright] .is-rte-pop.rte-customtag-options > div {
  width: 180px;
  height: 125px;
  padding: 9px 9px 9px;
}
#_cbhtml[toolbarright] .is-rte-pop.rte-customtag-options.active,
.is-ui[toolbarright] .is-rte-pop.rte-customtag-options.active {
  animation-name: customtag-rightslide-out;
}
@keyframes customtag-rightslide-out {
  from {
    width: 0;
  }
  to {
    width: 180px;
  }
}
#_cbhtml[toolbarright] .is-rte-pop.rte-customtag-options.deactive,
.is-ui[toolbarright] .is-rte-pop.rte-customtag-options.deactive {
  animation-name: customtag-rightslide-in;
}
@keyframes customtag-rightslide-in {
  from {
    width: 180px;
  }
  to {
    width: 0;
  }
}
#_cbhtml[toolbarright] .is-rte-pop.rte-icon-options,
.is-ui[toolbarright] .is-rte-pop.rte-icon-options {
  height: 320px;
}
#_cbhtml[toolbarright] .is-rte-pop.rte-icon-options > div,
.is-ui[toolbarright] .is-rte-pop.rte-icon-options > div {
  flex-direction: column;
  width: 100%;
  height: 320px;
  padding: 0;
}
#_cbhtml[toolbarright] .is-rte-pop.rte-icon-options > div .div-icon-list,
.is-ui[toolbarright] .is-rte-pop.rte-icon-options > div .div-icon-list {
  width: 100%;
  height: 100%;
  display: flex;
  align-content: flex-start;
  flex-wrap: wrap;
  overflow-y: auto;
  overflow-x: hidden;
}
#_cbhtml[toolbarright] .is-rte-pop.rte-icon-options > div .div-icon-list button,
.is-ui[toolbarright] .is-rte-pop.rte-icon-options > div .div-icon-list button {
  font-size: 16px;
  width: 50px;
  height: 40px;
  outline-offset: -2px;
}
#_cbhtml[toolbarright] .is-rte-pop.rte-icon-options iframe,
.is-ui[toolbarright] .is-rte-pop.rte-icon-options iframe {
  margin: 9px 0 9px 0;
}
#_cbhtml[toolbarright] .is-rte-pop.rte-icon-options.active,
.is-ui[toolbarright] .is-rte-pop.rte-icon-options.active {
  animation-name: icon-rightslide-out;
}
@keyframes icon-rightslide-out {
  from {
    width: 0;
  }
  to {
    width: 280px;
  }
}
#_cbhtml[toolbarright] .is-rte-pop.rte-icon-options.deactive,
.is-ui[toolbarright] .is-rte-pop.rte-icon-options.deactive {
  animation-name: icon-rightslide-in;
}
@keyframes icon-rightslide-in {
  from {
    width: 280px;
  }
  to {
    width: 0;
  }
}
#_cbhtml[toolbarright] .is-rte-pop.rte-color-picker,
.is-ui[toolbarright] .is-rte-pop.rte-color-picker {
  height: 452px;
}
#_cbhtml[toolbarright] .is-rte-pop.rte-color-picker.active,
.is-ui[toolbarright] .is-rte-pop.rte-color-picker.active {
  animation-name: colorpicker-rightslide-out;
}
@keyframes colorpicker-rightslide-out {
  from {
    width: 0;
  }
  to {
    width: 270px;
  }
}
#_cbhtml[toolbarright] .is-rte-pop.rte-color-picker.deactive,
.is-ui[toolbarright] .is-rte-pop.rte-color-picker.deactive {
  animation-name: colorpicker-rightslide-in;
}
@keyframes colorpicker-rightslide-in {
  from {
    width: 270px;
  }
  to {
    width: 0;
  }
}
#_cbhtml[toolbarright] .is-rte-pop.rte-color-picker .is-pop-tabs,
.is-ui[toolbarright] .is-rte-pop.rte-color-picker .is-pop-tabs {
  flex-direction: row;
  padding: 11px 12px 0;
}
#_cbhtml[toolbarright] .is-rte-pop.rte-color-picker .rte-color-picker-area,
.is-ui[toolbarright] .is-rte-pop.rte-color-picker .rte-color-picker-area {
  padding: 0;
}
#_cbhtml[toolbarright] .is-rte-pop.rte-zoom-options > div,
.is-ui[toolbarright] .is-rte-pop.rte-zoom-options > div {
  width: 224px;
  flex-direction: column;
  padding: 1px 12px 12px 12px;
  box-sizing: border-box;
}
#_cbhtml[toolbarright] .is-rte-pop.rte-zoom-options.active,
.is-ui[toolbarright] .is-rte-pop.rte-zoom-options.active {
  animation-name: zoomsetting-rightslide-out;
}
@keyframes zoomsetting-rightslide-out {
  from {
    width: 0;
  }
  to {
    width: 225px;
  }
}
#_cbhtml[toolbarright] .is-rte-pop.rte-zoom-options.deactive,
.is-ui[toolbarright] .is-rte-pop.rte-zoom-options.deactive {
  animation-name: zoomsetting-rightslide-in;
}
@keyframes zoomsetting-rightslide-in {
  from {
    width: 225px;
  }
  to {
    width: 0;
  }
}
#_cbhtml .is-modal,
.is-ui .is-modal {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  display: none;
  z-index: 10004;
  background: rgba(0, 0, 0, 0.04);
  justify-content: center;
  align-items: center;
  flex-direction: column;
  font-family: sans-serif;
  font-weight: 300;
  outline: none;
}
#_cbhtml .is-modal a:focus,
.is-ui .is-modal a:focus {
  outline: #3e93f7 2px solid;
  outline-offset: 0;
}
#_cbhtml .is-modal.active,
.is-ui .is-modal.active {
  display: flex;
}
#_cbhtml .is-modal button,
.is-ui .is-modal button {
  color: #000;
  background: #fff;
  box-shadow: 0px 3px 6px -6px rgba(0, 0, 0, 0.32);
}
#_cbhtml .is-modal button:hover,
.is-ui .is-modal button:hover {
  background: #f1f1f1;
}
#_cbhtml .is-modal button.on,
.is-ui .is-modal button.on {
  background: #f1f1f1;
}
#_cbhtml .is-modal button.is-btn-color,
.is-ui .is-modal button.is-btn-color {
  width: 35px !important;
  height: 35px !important;
  padding: 0 !important;
  background: rgba(255, 255, 255, 0.2);
  border: rgba(0, 0, 0, 0.09) 1px solid !important;
}
#_cbhtml .is-modal .is-separator,
.is-ui .is-modal .is-separator {
  width: 100%;
  border-top: #f0f0f0 1px solid;
  margin-bottom: 10px;
  margin-top: 10px;
}
#_cbhtml .is-modal .form-upload-larger.please-wait svg,
.is-ui .is-modal .form-upload-larger.please-wait svg {
  transform: scale(1, 1);
  opacity: 1;
  animation-name: please-wait-anim;
  animation-duration: 3s;
  animation-fill-mode: forwards;
  animation-iteration-count: infinite;
}
@keyframes please-wait-anim {
  0% {
    transform: scale(1, 1);
    opacity: 0;
  }
  25% {
    transform: scale(1.2, 1.2);
    opacity: 1;
  }
  50% {
    transform: scale(1, 1);
    opacity: 0;
  }
  75% {
    transform: scale(1.2, 1.2);
    opacity: 1;
  }
  100% {
    transform: scale(1, 1);
    opacity: 0;
  }
}
#_cbhtml .is-modal .form-upload-larger:focus-within,
.is-ui .is-modal .form-upload-larger:focus-within {
  outline: #3e93f7 2px solid;
  outline-offset: 0;
  border-radius: 1px;
}
#_cbhtml .is-modal .is-modal-overlay,
.is-ui .is-modal .is-modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.04);
  z-index: -1;
  max-width: none !important;
  margin: 0 !important;
}
#_cbhtml .is-modal .is-modal-overlay.overlay-stay,
.is-ui .is-modal .is-modal-overlay.overlay-stay {
  background: rgba(0, 0, 0, 0.04);
}
#_cbhtml .is-modal.is-modal-content,
#_cbhtml .is-modal .is-modal-content,
.is-ui .is-modal.is-modal-content,
.is-ui .is-modal .is-modal-content {
  background: #fff;
  border: 1px solid #e3e3e3;
  box-shadow: 6px 14px 20px 0px rgba(95, 95, 95, 0.08);
  border-radius: 7px;
  overflow: hidden;
}
#_cbhtml .is-modal .is-modal-content,
.is-ui .is-modal .is-modal-content {
  position: relative;
  width: 100%;
  padding: 25px 25px;
  box-sizing: border-box;
  border-radius: 8px;
}
#_cbhtml .is-modal:not(.is-modal-content) > div:not(.is-modal-overlay),
.is-ui .is-modal:not(.is-modal-content) > div:not(.is-modal-overlay) {
  background: #fff;
  border: 1px solid #e3e3e3;
  box-shadow: 6px 14px 20px 0px rgba(95, 95, 95, 0.08);
  position: relative;
  width: 100%;
  padding: 25px 25px;
  box-sizing: border-box;
  border-radius: 7px;
  overflow: hidden;
}
#_cbhtml .is-modal.is-modal-full > div:not(.is-modal-overlay),
.is-ui .is-modal.is-modal-full > div:not(.is-modal-overlay) {
  width: 100% !important;
  height: 100% !important;
  max-width: 100% !important;
  max-height: 100% !important;
}
#_cbhtml .is-modal div.is-draggable,
.is-ui .is-modal div.is-draggable {
  cursor: move;
  box-shadow: none;
  background: transparent;
  padding: 0;
  border: none;
}
#_cbhtml .is-modal div.is-draggable > span,
.is-ui .is-modal div.is-draggable > span {
  pointer-events: none;
  user-select: none;
}
#_cbhtml .is-modal div.is-modal-bar,
.is-ui .is-modal div.is-modal-bar {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  flex: none;
  background: transparent;
  border: none;
  box-sizing: border-box;
  text-align: center;
  font-family: sans-serif;
  font-size: 13px;
  letter-spacing: 1px;
  color: #545454;
  touch-action: none;
  user-select: none;
  z-index: 1;
  line-height: 35px;
  height: 35px;
}
#_cbhtml .is-modal div.is-modal-bar .is-modal-close,
.is-ui .is-modal div.is-modal-bar .is-modal-close {
  z-index: 1;
  width: 32px;
  height: 32px;
  position: absolute;
  top: 2px;
  right: 2px;
  box-sizing: border-box;
  padding: 0;
  line-height: 32px;
  font-size: 12px;
  color: #545454;
  text-align: center;
  cursor: pointer;
  box-shadow: none;
  background: transparent;
  border: none;
}
#_cbhtml .is-modal div.is-modal-footer button,
.is-ui .is-modal div.is-modal-footer button {
  margin-left: 2px;
}
#_cbhtml .is-modal.fileselect, #_cbhtml .is-modal.mediaselect, #_cbhtml .is-modal.imageselect, #_cbhtml .is-modal.videoselect, #_cbhtml .is-modal.otherselect, #_cbhtml .is-modal.pickcolor, #_cbhtml .is-modal.pickcolormore, #_cbhtml .is-modal.pickcolorclass, #_cbhtml .is-modal.imagesource, #_cbhtml .is-modal.imageadjust, #_cbhtml .is-modal.imageadjust2, #_cbhtml .is-modal.audioselect, #_cbhtml .is-modal.mediasource,
.is-ui .is-modal.fileselect,
.is-ui .is-modal.mediaselect,
.is-ui .is-modal.imageselect,
.is-ui .is-modal.videoselect,
.is-ui .is-modal.otherselect,
.is-ui .is-modal.pickcolor,
.is-ui .is-modal.pickcolormore,
.is-ui .is-modal.pickcolorclass,
.is-ui .is-modal.imagesource,
.is-ui .is-modal.imageadjust,
.is-ui .is-modal.imageadjust2,
.is-ui .is-modal.audioselect,
.is-ui .is-modal.mediasource {
  z-index: 10006 !important;
}
#_cbhtml .is-modal.previewcontent,
.is-ui .is-modal.previewcontent {
  background: #d1d1d1;
}
#_cbhtml .is-modal.previewcontent .size-control,
.is-ui .is-modal.previewcontent .size-control {
  cursor: pointer;
  background: #f7f7f7;
  border-left: #dedede 2px solid;
  border-right: #dedede 2px solid;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
#_cbhtml .is-modal.previewcontent .size-control-info,
.is-ui .is-modal.previewcontent .size-control-info {
  text-align: center;
  color: #000;
}
#_cbhtml .is-modal.previewcontent .size-control.hover,
.is-ui .is-modal.previewcontent .size-control.hover {
  background: #f0f0f0;
}
#_cbhtml .is-modal.is-confirm div.is-modal-content,
.is-ui .is-modal.is-confirm div.is-modal-content {
  max-width: 526px;
  text-align: center;
}
#_cbhtml .is-modal.viewconfig label,
.is-ui .is-modal.viewconfig label {
  font-size: 14px;
}
#_cbhtml .is-modal.viewconfig div.is-modal-content,
.is-ui .is-modal.viewconfig div.is-modal-content {
  max-width: 750px;
  padding: 5px 25px 25px 25px;
}
#_cbhtml .is-modal.viewhtml, #_cbhtml .is-modal.viewhtmlformatted, #_cbhtml .is-modal.viewhtmlnormal,
.is-ui .is-modal.viewhtml,
.is-ui .is-modal.viewhtmlformatted,
.is-ui .is-modal.viewhtmlnormal {
  z-index: 10005;
}
#_cbhtml .is-modal.viewhtml div.is-modal-content, #_cbhtml .is-modal.viewhtmlformatted div.is-modal-content, #_cbhtml .is-modal.viewhtmlnormal div.is-modal-content,
.is-ui .is-modal.viewhtml div.is-modal-content,
.is-ui .is-modal.viewhtmlformatted div.is-modal-content,
.is-ui .is-modal.viewhtmlnormal div.is-modal-content {
  width: 80%;
  max-width: 1200px;
  height: 80%;
  padding: 0;
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
}
#_cbhtml .is-modal.viewhtmllarger,
.is-ui .is-modal.viewhtmllarger {
  z-index: 10005;
  align-items: flex-end;
}
#_cbhtml .is-modal.viewhtmllarger div.is-modal-content,
.is-ui .is-modal.viewhtmllarger div.is-modal-content {
  width: 100%;
  height: 100%;
  border: none;
  padding: 0;
}
#_cbhtml .is-modal.grideditor,
.is-ui .is-modal.grideditor {
  background: #fff;
  width: 96px;
  height: 488px;
  top: 33%;
  left: auto;
  right: 15%;
  margin-top: -220px;
  box-sizing: content-box;
  overflow: hidden;
}
#_cbhtml .is-modal.grideditor .is-modal-bar,
.is-ui .is-modal.grideditor .is-modal-bar {
  z-index: 1;
  height: 20px;
}
#_cbhtml .is-modal.grideditor .is-modal-bar .is-modal-close,
.is-ui .is-modal.grideditor .is-modal-bar .is-modal-close {
  width: 20px;
  height: 20px;
  line-height: 20px;
  font-size: 10px;
  margin: 2px;
}
#_cbhtml .is-modal.grideditor.active,
.is-ui .is-modal.grideditor.active {
  display: flex;
}
#_cbhtml .is-modal.grideditor > div,
.is-ui .is-modal.grideditor > div {
  width: 100%;
  box-sizing: border-box;
  padding: 1px;
  border: none;
}
#_cbhtml .is-modal.grideditor button,
.is-ui .is-modal.grideditor button {
  width: 45px;
  height: 40px;
  margin: 1px;
  background-color: transparent !important;
  box-shadow: none !important;
  outline-offset: -2px;
}
#_cbhtml .is-modal.grideditor button.on,
.is-ui .is-modal.grideditor button.on {
  background-color: rgba(0, 0, 0, 0.05) !important;
}
#_cbhtml .is-modal.grideditor button:hover,
.is-ui .is-modal.grideditor button:hover {
  background-color: rgba(0, 0, 0, 0.03) !important;
}
#_cbhtml .is-modal.grideditor .is-separator,
.is-ui .is-modal.grideditor .is-separator {
  width: 100%;
  border-top: #f2f2f2 1px solid;
  display: flex;
  padding: 1px;
}
#_cbhtml .is-modal.pagesize .is-modal-content,
.is-ui .is-modal.pagesize .is-modal-content {
  max-width: 980px;
}
#_cbhtml .is-modal.pagesize .div-page-sizes,
.is-ui .is-modal.pagesize .div-page-sizes {
  display: flex;
  overflow-y: auto;
  overflow-x: hidden;
  background: #f3f3f3;
  flex-flow: row wrap;
  padding: 20px;
  box-sizing: border-box;
  height: 590px;
}
#_cbhtml .is-modal.pagesize .div-page-sizes .paper-item,
.is-ui .is-modal.pagesize .div-page-sizes .paper-item {
  flex: none;
}
#_cbhtml .is-modal.pagesize [data-pagesize],
.is-ui .is-modal.pagesize [data-pagesize] {
  background: #fff;
  margin: 20px;
  box-shadow: 5px 9px 10px rgba(0, 0, 0, 0.1);
  min-width: 70px;
  min-height: 70px;
}
#_cbhtml .is-modal.pagesize .input-width,
#_cbhtml .is-modal.pagesize .input-height,
.is-ui .is-modal.pagesize .input-width,
.is-ui .is-modal.pagesize .input-height {
  width: 65px;
}
#_cbhtml .is-modal.edittable,
.is-ui .is-modal.edittable {
  z-index: 10002;
  position: fixed;
  overflow: hidden;
  width: 250px;
  height: 410px;
  top: 50%;
  left: auto;
  right: 30%;
  margin-top: -205px;
  box-sizing: content-box;
  flex-direction: row;
  align-items: flex-start;
}
#_cbhtml .is-modal.edittable .is-modal-bar,
.is-ui .is-modal.edittable .is-modal-bar {
  line-height: 30px;
  height: 30px;
  background-color: #fafafa;
}
#_cbhtml .is-modal.edittable .is-modal-bar .is-modal-close,
.is-ui .is-modal.edittable .is-modal-bar .is-modal-close {
  width: 20px;
  height: 20px;
  line-height: 20px;
  font-size: 10px;
}
#_cbhtml .is-modal.edittable.active,
.is-ui .is-modal.edittable.active {
  display: flex;
}
#_cbhtml .is-modal.edittable > div:not(.is-draggable),
.is-ui .is-modal.edittable > div:not(.is-draggable) {
  width: 100%;
  margin-top: 30px;
}
#_cbhtml .is-modal.edittable .is-modal-overlay,
.is-ui .is-modal.edittable .is-modal-overlay {
  display: none !important;
}
#_cbhtml .is-modal.edittable button,
.is-ui .is-modal.edittable button {
  height: 35px;
  outline-offset: -2px;
}
#_cbhtml .is-modal.edittable button:focus,
.is-ui .is-modal.edittable button:focus {
  border-radius: 2px;
}
#_cbhtml .is-modal.edittable button.is-btn-color,
.is-ui .is-modal.edittable button.is-btn-color {
  width: 35px;
  height: 35px;
  padding: 0;
  background: rgba(255, 255, 255, 0.2);
  border: rgba(0, 0, 0, 0.09) 1px solid;
}
#_cbhtml .is-modal.editblock,
.is-ui .is-modal.editblock {
  z-index: 10002;
  position: fixed;
  overflow: hidden;
  width: 300px;
  height: auto;
  top: calc(50vh - 275px);
  left: auto;
  right: 40px;
  box-sizing: content-box;
  flex-direction: row;
  align-items: flex-start;
}
#_cbhtml .is-modal.editblock .is-modal-bar,
.is-ui .is-modal.editblock .is-modal-bar {
  line-height: 30px;
  height: 30px;
}
#_cbhtml .is-modal.editblock .is-modal-bar .is-modal-close,
.is-ui .is-modal.editblock .is-modal-bar .is-modal-close {
  width: 20px;
  height: 20px;
  line-height: 20px;
  font-size: 10px;
}
#_cbhtml .is-modal.editblock.active,
.is-ui .is-modal.editblock.active {
  display: flex;
}
#_cbhtml .is-modal.editblock > div:not(.is-draggable),
.is-ui .is-modal.editblock > div:not(.is-draggable) {
  width: 100%;
  margin-top: 30px;
  padding: 25px;
  box-sizing: border-box;
}
#_cbhtml .is-modal.editblock .is-modal-overlay,
.is-ui .is-modal.editblock .is-modal-overlay {
  display: none !important;
}
#_cbhtml .is-modal.editblock button,
.is-ui .is-modal.editblock button {
  width: auto;
  height: 35px;
  font-size: 10px;
  line-height: 1;
  text-transform: uppercase;
  padding: 1px 20px;
  box-sizing: border-box;
  border: none;
  outline-offset: -2px;
}
#_cbhtml .is-modal.editblock button:focus,
.is-ui .is-modal.editblock button:focus {
  border-radius: 2px;
}
#_cbhtml .is-modal.editblock button.is-btn-color,
.is-ui .is-modal.editblock button.is-btn-color {
  width: 35px;
  height: 35px;
  padding: 0;
  background: rgba(255, 255, 255, 0.2);
  border: rgba(0, 0, 0, 0.09) 1px solid;
}
#_cbhtml .is-modal.editblock button span,
.is-ui .is-modal.editblock button span {
  margin-left: 5px;
}
#_cbhtml .is-modal.editblock button svg,
.is-ui .is-modal.editblock button svg {
  width: 12px;
  height: 12px;
  flex: none;
}
#_cbhtml .is-modal.editblock button.input-cell-bgimage,
.is-ui .is-modal.editblock button.input-cell-bgimage {
  margin-right: 1px;
}
#_cbhtml .is-modal.editblock button.input-cell-bgimage svg,
.is-ui .is-modal.editblock button.input-cell-bgimage svg {
  width: 14px;
  height: 14px;
}
#_cbhtml .is-modal.editblock .asset-page-preview,
#_cbhtml .is-modal.editblock .asset-block-preview,
.is-ui .is-modal.editblock .asset-page-preview,
.is-ui .is-modal.editblock .asset-block-preview {
  max-width: 120px;
  height: 100px;
}
#_cbhtml .is-modal.editblock .asset-page-preview img,
#_cbhtml .is-modal.editblock .asset-block-preview img,
.is-ui .is-modal.editblock .asset-page-preview img,
.is-ui .is-modal.editblock .asset-block-preview img {
  max-width: 100%;
  max-height: 100%;
}
#_cbhtml .is-modal.editblock .div-content-textcolor,
.is-ui .is-modal.editblock .div-content-textcolor {
  display: flex;
}
#_cbhtml .is-modal.editblock .div-content-textcolor button,
.is-ui .is-modal.editblock .div-content-textcolor button {
  width: 40px;
  border: transparent 1px solid;
}
#_cbhtml .is-modal.editblock .div-content-textcolor button[data-textcolor=dark],
.is-ui .is-modal.editblock .div-content-textcolor button[data-textcolor=dark] {
  width: auto;
  background-color: #f7f7f7;
  color: #111;
}
#_cbhtml .is-modal.editblock .div-content-textcolor button[data-textcolor=light],
.is-ui .is-modal.editblock .div-content-textcolor button[data-textcolor=light] {
  width: auto;
  background-color: #333;
  color: #f7f7f7;
}
#_cbhtml .is-modal.shortcuts div.is-modal-content,
.is-ui .is-modal.shortcuts div.is-modal-content {
  max-width: 600px;
  padding: 5px 25px 25px 25px;
}
#_cbhtml .is-modal.shortcuts table,
.is-ui .is-modal.shortcuts table {
  width: 100%;
  margin: 30px 7px 0;
}
#_cbhtml .is-modal.shortcuts td,
.is-ui .is-modal.shortcuts td {
  font-family: sans-serif;
  font-size: 15px;
  font-weight: 300;
  padding: 7px 2px;
  line-height: 1.2;
}
#_cbhtml .is-modal.shortcuts td.shortcut-title,
.is-ui .is-modal.shortcuts td.shortcut-title {
  font-weight: 500;
  padding-top: 20px;
}
#_cbhtml .is-modal.pickcolor,
.is-ui .is-modal.pickcolor {
  background: rgba(255, 255, 255, 0);
}
#_cbhtml .is-modal.pickcolor .is-modal-overlay,
.is-ui .is-modal.pickcolor .is-modal-overlay {
  background: rgba(255, 255, 255, 0);
}
#_cbhtml .is-modal.snippetwindow,
.is-ui .is-modal.snippetwindow {
  z-index: 10002;
  position: fixed;
  overflow: hidden;
  width: 228px;
  height: 85vh;
  min-height: 560px;
  max-height: 968px;
  top: calc(50% - 42.5vh);
  left: auto;
  right: 40px;
  box-sizing: content-box;
  flex-direction: row;
  align-items: flex-start;
  border-radius: 6px;
  box-shadow: 9px 14px 30px 1px rgba(0, 0, 0, 0.12);
  background-color: #fff;
}
#_cbhtml .is-modal.snippetwindow.left,
.is-ui .is-modal.snippetwindow.left {
  right: auto;
  left: 40px;
}
#_cbhtml .is-modal.snippetwindow #divSnippetWindow,
.is-ui .is-modal.snippetwindow #divSnippetWindow {
  height: 100%;
  position: absolute;
  top: 0;
  border-top: transparent 20px solid;
  box-sizing: border-box;
  margin-top: 0;
}
#_cbhtml .is-modal.snippetwindow .is-modal-overlay,
.is-ui .is-modal.snippetwindow .is-modal-overlay {
  display: none !important;
}
#_cbhtml .is-modal.snippetwindow .is-modal-bar,
.is-ui .is-modal.snippetwindow .is-modal-bar {
  line-height: 30px;
  height: 20px;
  background-color: transparent;
  transition: none !important;
}
#_cbhtml .is-modal.snippetwindow .is-modal-bar .is-modal-close,
.is-ui .is-modal.snippetwindow .is-modal-bar .is-modal-close {
  width: 20px;
  height: 20px;
  line-height: 20px;
  font-size: 10px;
  top: 0px;
}
#_cbhtml .is-modal.snippetwindow.active,
.is-ui .is-modal.snippetwindow.active {
  display: flex;
}
#_cbhtml .is-modal.snippetwindow > div:not(.is-draggable),
.is-ui .is-modal.snippetwindow > div:not(.is-draggable) {
  width: 100%;
  margin-top: 20px;
}
#_cbhtml .is-modal.snippetwindow #divSnippetHandle,
.is-ui .is-modal.snippetwindow #divSnippetHandle {
  display: none !important;
}
#_cbhtml .is-modal.buttoneditor,
.is-ui .is-modal.buttoneditor {
  z-index: 10002;
  position: fixed;
  overflow: hidden;
  width: 575px;
  height: 648px;
  top: 50%;
  left: auto;
  right: 30%;
  margin-top: -324px;
  box-sizing: content-box;
  flex-direction: row;
  align-items: flex-start;
}
#_cbhtml .is-modal.buttoneditor .is-modal-overlay,
.is-ui .is-modal.buttoneditor .is-modal-overlay {
  display: none !important;
}
#_cbhtml .is-modal.buttoneditor .is-modal-bar,
.is-ui .is-modal.buttoneditor .is-modal-bar {
  line-height: 30px;
  height: 30px;
  background-color: #fafafa;
}
#_cbhtml .is-modal.buttoneditor .is-modal-bar .is-modal-close,
.is-ui .is-modal.buttoneditor .is-modal-bar .is-modal-close {
  width: 20px;
  height: 20px;
  line-height: 20px;
  font-size: 10px;
}
#_cbhtml .is-modal.buttoneditor.active,
.is-ui .is-modal.buttoneditor.active {
  display: flex;
}
#_cbhtml .is-modal.buttoneditor > div:not(.is-draggable),
.is-ui .is-modal.buttoneditor > div:not(.is-draggable) {
  width: 100%;
  margin-top: 30px;
}
#_cbhtml .is-modal.buttoneditor button,
.is-ui .is-modal.buttoneditor button {
  padding: 0 10px;
  height: 35px;
  outline-offset: -2px;
}
#_cbhtml .is-modal.buttoneditor button:focus,
.is-ui .is-modal.buttoneditor button:focus {
  border-radius: 2px;
}
#_cbhtml .is-modal.buttoneditor .is-button-remove,
.is-ui .is-modal.buttoneditor .is-button-remove {
  position: absolute;
  top: -11px;
  right: -11px;
  width: 20px;
  height: 20px;
  justify-content: center;
  align-items: center;
  background: #f96700;
  color: #fff;
  font-size: 12px;
  cursor: pointer;
  display: none;
  border-radius: 500px;
}
#_cbhtml .is-modal.buttoneditor .is-button-remove svg,
.is-ui .is-modal.buttoneditor .is-button-remove svg {
  fill: white;
  width: 20px;
  height: 20px;
}
#_cbhtml .is-modal.buttoneditor #divMyButtons, #_cbhtml .is-modal.buttoneditor #divButtonTemplates,
.is-ui .is-modal.buttoneditor #divMyButtons,
.is-ui .is-modal.buttoneditor #divButtonTemplates {
  background-color: #ebebeb;
  position: absolute;
  width: 100%;
  height: calc(100% - 80px);
  top: 80px;
  left: 0px;
  box-sizing: border-box;
}
#_cbhtml .is-modal.buttoneditor #divMyButtonList,
.is-ui .is-modal.buttoneditor #divMyButtonList {
  padding: 10px 20px 10px;
  height: 100%;
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  border-top: transparent 70px solid;
  box-sizing: border-box;
  overflow: hidden;
  overflow-y: auto;
  overflow-y: auto;
}
#_cbhtml .is-modal.buttoneditor #divMyButtonList a,
.is-ui .is-modal.buttoneditor #divMyButtonList a {
  position: relative;
  margin: 10px 20px 20px 0;
  outline-offset: 2px;
}
#_cbhtml .is-modal.buttoneditor #divMyButtonList a:hover .is-button-remove,
.is-ui .is-modal.buttoneditor #divMyButtonList a:hover .is-button-remove {
  display: flex;
}
#_cbhtml .is-modal.buttoneditor #divButtonTemplates,
.is-ui .is-modal.buttoneditor #divButtonTemplates {
  padding: 0;
  overflow: hidden;
  overflow-y: auto;
}
#_cbhtml .is-modal.buttoneditor #divButtonTemplateList,
.is-ui .is-modal.buttoneditor #divButtonTemplateList {
  padding: 10px 20px 0px;
  width: 100%;
  top: 0;
  left: 0;
  box-sizing: border-box;
}
#_cbhtml .is-modal.buttoneditor #divButtonTemplateList a,
.is-ui .is-modal.buttoneditor #divButtonTemplateList a {
  position: relative;
  margin: 15px 28px 13px 0;
  outline-offset: 2px;
}
#_cbhtml .is-modal.columnsettings .is-modal-bar, #_cbhtml .is-modal.rowsettings .is-modal-bar,
.is-ui .is-modal.columnsettings .is-modal-bar,
.is-ui .is-modal.rowsettings .is-modal-bar {
  background-color: #fafafa;
}
#_cbhtml .is-modal.columnsettings .is-modal-content, #_cbhtml .is-modal.rowsettings .is-modal-content,
.is-ui .is-modal.columnsettings .is-modal-content,
.is-ui .is-modal.rowsettings .is-modal-content {
  width: 380px !important;
  min-height: 435px;
  padding: 0px !important;
}
#_cbhtml .is-modal.columnsettings.active, #_cbhtml .is-modal.rowsettings.active,
.is-ui .is-modal.columnsettings.active,
.is-ui .is-modal.rowsettings.active {
  display: flex;
}
#_cbhtml .is-modal.columnsettings > div:not(.is-draggable), #_cbhtml .is-modal.rowsettings > div:not(.is-draggable),
.is-ui .is-modal.columnsettings > div:not(.is-draggable),
.is-ui .is-modal.rowsettings > div:not(.is-draggable) {
  width: 100%;
  margin-top: 30px;
}
#_cbhtml .is-modal.columnsettings button, #_cbhtml .is-modal.rowsettings button,
.is-ui .is-modal.columnsettings button,
.is-ui .is-modal.rowsettings button {
  width: auto;
  height: 35px;
  font-size: 10px;
  line-height: 1;
  text-transform: uppercase;
  padding: 1px 20px;
  box-sizing: border-box;
  border: none;
  outline-offset: -2px;
}
#_cbhtml .is-modal.columnsettings button:focus, #_cbhtml .is-modal.rowsettings button:focus,
.is-ui .is-modal.columnsettings button:focus,
.is-ui .is-modal.rowsettings button:focus {
  border-radius: 2px;
}
#_cbhtml .is-modal.columnsettings button.is-btn-color, #_cbhtml .is-modal.rowsettings button.is-btn-color,
.is-ui .is-modal.columnsettings button.is-btn-color,
.is-ui .is-modal.rowsettings button.is-btn-color {
  width: 35px;
  height: 35px;
  padding: 0;
  background: rgba(255, 255, 255, 0.2);
  border: rgba(0, 0, 0, 0.09) 1px solid;
}
#_cbhtml .is-modal.columnsettings button span, #_cbhtml .is-modal.rowsettings button span,
.is-ui .is-modal.columnsettings button span,
.is-ui .is-modal.rowsettings button span {
  margin-left: 5px;
}
#_cbhtml .is-modal.columnsettings button svg, #_cbhtml .is-modal.rowsettings button svg,
.is-ui .is-modal.columnsettings button svg,
.is-ui .is-modal.rowsettings button svg {
  width: 12px;
  height: 12px;
  flex: none;
}
#_cbhtml .is-modal.columnsettings button.input-cell-bgimage, #_cbhtml .is-modal.rowsettings button.input-cell-bgimage,
.is-ui .is-modal.columnsettings button.input-cell-bgimage,
.is-ui .is-modal.rowsettings button.input-cell-bgimage {
  margin-right: 1px;
}
#_cbhtml .is-modal.columnsettings button.input-cell-bgimage svg, #_cbhtml .is-modal.rowsettings button.input-cell-bgimage svg,
.is-ui .is-modal.columnsettings button.input-cell-bgimage svg,
.is-ui .is-modal.rowsettings button.input-cell-bgimage svg {
  width: 14px;
  height: 14px;
}
#_cbhtml .is-modal.columnsettings .cell-bgimage-preview, #_cbhtml .is-modal.rowsettings .cell-bgimage-preview,
.is-ui .is-modal.columnsettings .cell-bgimage-preview,
.is-ui .is-modal.rowsettings .cell-bgimage-preview {
  max-width: 120px;
}
#_cbhtml .is-modal.columnsettings .cell-bgimage-preview img, #_cbhtml .is-modal.rowsettings .cell-bgimage-preview img,
.is-ui .is-modal.columnsettings .cell-bgimage-preview img,
.is-ui .is-modal.rowsettings .cell-bgimage-preview img {
  max-width: 100%;
  max-height: 100%;
}
#_cbhtml .is-modal.columnsettings .div-content-padding,
#_cbhtml .is-modal.columnsettings .div-content-height, #_cbhtml .is-modal.rowsettings .div-content-padding,
#_cbhtml .is-modal.rowsettings .div-content-height,
.is-ui .is-modal.columnsettings .div-content-padding,
.is-ui .is-modal.columnsettings .div-content-height,
.is-ui .is-modal.rowsettings .div-content-padding,
.is-ui .is-modal.rowsettings .div-content-height {
  display: flex;
}
#_cbhtml .is-modal.columnsettings .div-content-padding button,
#_cbhtml .is-modal.columnsettings .div-content-height button, #_cbhtml .is-modal.rowsettings .div-content-padding button,
#_cbhtml .is-modal.rowsettings .div-content-height button,
.is-ui .is-modal.columnsettings .div-content-padding button,
.is-ui .is-modal.columnsettings .div-content-height button,
.is-ui .is-modal.rowsettings .div-content-padding button,
.is-ui .is-modal.rowsettings .div-content-height button {
  width: 40px;
  border: transparent 1px solid;
}
#_cbhtml .is-modal.columnsettings .div-content-padding button svg,
#_cbhtml .is-modal.columnsettings .div-content-height button svg, #_cbhtml .is-modal.rowsettings .div-content-padding button svg,
#_cbhtml .is-modal.rowsettings .div-content-height button svg,
.is-ui .is-modal.columnsettings .div-content-padding button svg,
.is-ui .is-modal.columnsettings .div-content-height button svg,
.is-ui .is-modal.rowsettings .div-content-padding button svg,
.is-ui .is-modal.rowsettings .div-content-height button svg {
  flex: none;
}
#_cbhtml .is-modal.columnsettings .div-content-padding-pos, #_cbhtml .is-modal.rowsettings .div-content-padding-pos,
.is-ui .is-modal.columnsettings .div-content-padding-pos,
.is-ui .is-modal.rowsettings .div-content-padding-pos {
  display: flex;
}
#_cbhtml .is-modal.columnsettings .div-content-padding-pos button, #_cbhtml .is-modal.rowsettings .div-content-padding-pos button,
.is-ui .is-modal.columnsettings .div-content-padding-pos button,
.is-ui .is-modal.rowsettings .div-content-padding-pos button {
  width: 75px;
  padding-left: 15px;
  padding-right: 15px;
  border: transparent 1px solid;
}
#_cbhtml .is-modal.columnsettings .div-content-padding-pos button svg, #_cbhtml .is-modal.rowsettings .div-content-padding-pos button svg,
.is-ui .is-modal.columnsettings .div-content-padding-pos button svg,
.is-ui .is-modal.rowsettings .div-content-padding-pos button svg {
  flex: none;
}
#_cbhtml .is-modal.columnsettings .div-content-textcolor, #_cbhtml .is-modal.rowsettings .div-content-textcolor,
.is-ui .is-modal.columnsettings .div-content-textcolor,
.is-ui .is-modal.rowsettings .div-content-textcolor {
  display: flex;
}
#_cbhtml .is-modal.columnsettings .div-content-textcolor button, #_cbhtml .is-modal.rowsettings .div-content-textcolor button,
.is-ui .is-modal.columnsettings .div-content-textcolor button,
.is-ui .is-modal.rowsettings .div-content-textcolor button {
  width: 40px;
  border: transparent 1px solid;
}
#_cbhtml .is-modal.columnsettings .div-content-textcolor button[data-command=dark], #_cbhtml .is-modal.rowsettings .div-content-textcolor button[data-command=dark],
.is-ui .is-modal.columnsettings .div-content-textcolor button[data-command=dark],
.is-ui .is-modal.rowsettings .div-content-textcolor button[data-command=dark] {
  width: auto;
  background-color: #f7f7f7;
  color: #111;
}
#_cbhtml .is-modal.columnsettings .div-content-textcolor button[data-command=light], #_cbhtml .is-modal.rowsettings .div-content-textcolor button[data-command=light],
.is-ui .is-modal.columnsettings .div-content-textcolor button[data-command=light],
.is-ui .is-modal.rowsettings .div-content-textcolor button[data-command=light] {
  width: auto;
  background-color: #333;
  color: #f7f7f7;
}
#_cbhtml .is-modal.columnsettings .div-content-position, #_cbhtml .is-modal.rowsettings .div-content-position,
.is-ui .is-modal.columnsettings .div-content-position,
.is-ui .is-modal.rowsettings .div-content-position {
  display: flex;
  flex-flow: row wrap;
  align-items: center;
  width: 184px;
  padding: 0;
}
#_cbhtml .is-modal.columnsettings .div-content-position button, #_cbhtml .is-modal.rowsettings .div-content-position button,
.is-ui .is-modal.columnsettings .div-content-position button,
.is-ui .is-modal.rowsettings .div-content-position button {
  width: 30px;
  height: 36px;
  border: transparent 1px solid;
}
#_cbhtml .is-modal.columnsettings .div-content-position button svg, #_cbhtml .is-modal.rowsettings .div-content-position button svg,
.is-ui .is-modal.columnsettings .div-content-position button svg,
.is-ui .is-modal.rowsettings .div-content-position button svg {
  flex: none;
}
#_cbhtml .is-modal.columnsettings .image-src, #_cbhtml .is-modal.rowsettings .image-src,
.is-ui .is-modal.columnsettings .image-src,
.is-ui .is-modal.rowsettings .image-src {
  display: flex;
}
#_cbhtml .is-modal.columnsettings .image-src > button, #_cbhtml .is-modal.rowsettings .image-src > button,
.is-ui .is-modal.columnsettings .image-src > button,
.is-ui .is-modal.rowsettings .image-src > button {
  width: 50px;
  height: 43px;
}
#_cbhtml .is-modal.columnsettings #divCellClick p, #_cbhtml .is-modal.rowsettings #divCellClick p,
.is-ui .is-modal.columnsettings #divCellClick p,
.is-ui .is-modal.rowsettings #divCellClick p {
  font-size: 13px;
  line-height: 1.7;
}
#_cbhtml .is-modal.imagesource .is-modal-content, #_cbhtml .is-modal.mediasource .is-modal-content,
.is-ui .is-modal.imagesource .is-modal-content,
.is-ui .is-modal.mediasource .is-modal-content {
  padding: 20px;
}
#_cbhtml .is-modal.imagesource .image-src, #_cbhtml .is-modal.mediasource .image-src,
.is-ui .is-modal.imagesource .image-src,
.is-ui .is-modal.mediasource .image-src {
  display: flex;
}
#_cbhtml .is-modal.imagesource .image-src > button, #_cbhtml .is-modal.mediasource .image-src > button,
.is-ui .is-modal.imagesource .image-src > button,
.is-ui .is-modal.mediasource .image-src > button {
  width: 50px;
  height: 43px;
}
#_cbhtml .is-modal.imagesource .input-select, #_cbhtml .is-modal.mediasource .input-select,
.is-ui .is-modal.imagesource .input-select,
.is-ui .is-modal.mediasource .input-select {
  width: 50px;
  height: 43px;
  flex: none;
}
#_cbhtml .is-modal.imagesource .input-upload, #_cbhtml .is-modal.mediasource .input-upload,
.is-ui .is-modal.imagesource .input-upload,
.is-ui .is-modal.mediasource .input-upload {
  width: 50px;
  height: 43px;
}
#_cbhtml .is-modal.pickfontfamily .is-modal-bar,
.is-ui .is-modal.pickfontfamily .is-modal-bar {
  background: #fafafa;
}
#_cbhtml .is-modal.pickfontfamily div.is-modal-content,
.is-ui .is-modal.pickfontfamily div.is-modal-content {
  max-width: 303px;
  padding: 0;
}
#_cbhtml .is-modal.editstyles .is-modal-bar .is-modal-close,
.is-ui .is-modal.editstyles .is-modal-bar .is-modal-close {
  width: 20px;
  height: 20px;
  line-height: 20px;
  font-size: 10px;
}
#_cbhtml .is-modal.pickcolor div.is-modal-content,
.is-ui .is-modal.pickcolor div.is-modal-content {
  max-width: 271px;
  padding: 12px 12px;
}
#_cbhtml .is-modal.pickcolor .is-modal-bar,
.is-ui .is-modal.pickcolor .is-modal-bar {
  height: 11px;
}
#_cbhtml .is-modal.pickcolormore div.is-modal-content,
.is-ui .is-modal.pickcolormore div.is-modal-content {
  max-width: 340px;
}
#_cbhtml .is-modal.pickcolormore .is-modal-bar,
.is-ui .is-modal.pickcolormore .is-modal-bar {
  height: 11px;
}
#_cbhtml .is-modal.insertimage label,
.is-ui .is-modal.insertimage label {
  font-size: 14px;
}
#_cbhtml .is-modal.insertimage .is-drop-area,
.is-ui .is-modal.insertimage .is-drop-area {
  border: 2px dashed #b3b3b3;
  position: relative;
}
#_cbhtml .is-modal.insertimage .is-drop-area:hover,
.is-ui .is-modal.insertimage .is-drop-area:hover {
  background-color: #f7f7f7;
}
#_cbhtml .is-modal.insertimage .is-drop-area:focus-within,
.is-ui .is-modal.insertimage .is-drop-area:focus-within {
  border-color: #3e93f7;
}
#_cbhtml .is-modal.insertimage .is-drop-area.image-dropping,
.is-ui .is-modal.insertimage .is-drop-area.image-dropping {
  background-color: #f7f7f7;
}
#_cbhtml .is-modal.insertimage .is-drop-area #fileInsertImage,
.is-ui .is-modal.insertimage .is-drop-area #fileInsertImage {
  position: absolute;
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  outline: none;
  opacity: 0;
  cursor: pointer;
}
#_cbhtml .is-modal.insertimage .is-drop-area .drag-text p,
.is-ui .is-modal.insertimage .is-drop-area .drag-text p {
  font-size: 12px;
  text-align: center;
  text-transform: uppercase;
  letter-spacing: 1px;
  padding: 60px 0;
}
#_cbhtml .is-modal.insertimage .is-preview-area,
.is-ui .is-modal.insertimage .is-preview-area {
  display: none;
  text-align: center;
}
#_cbhtml .is-modal.insertimage .is-preview-area div,
.is-ui .is-modal.insertimage .is-preview-area div {
  position: relative;
  display: inline-block;
  margin: 10px;
}
#_cbhtml .is-modal.insertimage .is-preview-area div button,
.is-ui .is-modal.insertimage .is-preview-area div button {
  position: absolute;
  top: 0;
  right: 0;
  cursor: pointer;
  background: rgba(255, 255, 255, 0.8);
  color: #000;
  width: 28px;
  height: 28px;
  text-align: center;
  line-height: 28px;
  font-size: 27px;
  box-sizing: border-box;
  padding-left: 1px;
  cursor: pointer;
}
#_cbhtml .is-modal.insertimage .is-preview-area #imgInsertImagePreview,
.is-ui .is-modal.insertimage .is-preview-area #imgInsertImagePreview {
  max-height: 200px;
  max-width: 200px;
}
#_cbhtml .is-modal.insertimage .image-src,
.is-ui .is-modal.insertimage .image-src {
  display: flex;
}
#_cbhtml .is-modal.insertimage .image-src > button,
.is-ui .is-modal.insertimage .image-src > button {
  width: 50px;
  height: 43px;
}
#_cbhtml .is-modal.imageedit div.is-modal-content,
.is-ui .is-modal.imageedit div.is-modal-content {
  width: auto;
  padding-top: 7px;
}
#_cbhtml .is-modal.imageedit .imageedit-crop button,
.is-ui .is-modal.imageedit .imageedit-crop button {
  margin: 0 20px 0 0;
  border: #d1d1d1 1px solid;
  background-color: transparent !important;
}
#_cbhtml .is-modal.imagelink label,
.is-ui .is-modal.imagelink label {
  font-size: 14px;
}
#_cbhtml .is-modal.imagelink div.is-modal-content,
.is-ui .is-modal.imagelink div.is-modal-content {
  max-width: 526px;
}
#_cbhtml .is-modal.imagelink .image-src,
.is-ui .is-modal.imagelink .image-src {
  display: flex;
}
#_cbhtml .is-modal.imagelink .image-src button,
.is-ui .is-modal.imagelink .image-src button {
  width: 50px;
  height: 43px;
}
#_cbhtml .is-modal.imagelink .image-src .image-larger1,
.is-ui .is-modal.imagelink .image-src .image-larger1 {
  width: 50px;
  height: 43px;
}
#_cbhtml .is-modal.imagelink .image-link,
.is-ui .is-modal.imagelink .image-link {
  display: flex;
}
#_cbhtml .is-modal.imagelink .image-link button,
.is-ui .is-modal.imagelink .image-link button {
  width: 50px;
  height: 43px;
}
#_cbhtml .is-modal.imagelink .image-link .image-larger2,
.is-ui .is-modal.imagelink .image-link .image-larger2 {
  width: 50px;
  height: 43px;
}
#_cbhtml .is-modal.iframelink div.is-modal-content,
.is-ui .is-modal.iframelink div.is-modal-content {
  max-width: 550px;
}
#_cbhtml .is-modal.videolink div.is-modal-content,
.is-ui .is-modal.videolink div.is-modal-content {
  max-width: 550px;
}
#_cbhtml .is-modal.videolink .video-url,
.is-ui .is-modal.videolink .video-url {
  position: relative;
  height: 43px;
  display: flex;
  flex-direction: row;
}
#_cbhtml .is-modal.videolink .video-url .inpVideoLinkSource,
.is-ui .is-modal.videolink .video-url .inpVideoLinkSource {
  width: 100%;
}
#_cbhtml .is-modal.videolink .input-select,
.is-ui .is-modal.videolink .input-select {
  width: 50px;
  height: 43px;
  font-size: 20px;
  height: 43px;
  width: 50px;
  border-left: none;
  flex: none;
}
#_cbhtml .is-modal.videolink .input-upload,
.is-ui .is-modal.videolink .input-upload {
  width: 50px;
  height: 43px;
}
#_cbhtml .is-modal.svgsettings div.is-modal-content,
.is-ui .is-modal.svgsettings div.is-modal-content {
  max-width: 550px;
}
#_cbhtml .is-modal.svgsettings div.is-modal-content #inpSvgCode,
.is-ui .is-modal.svgsettings div.is-modal-content #inpSvgCode {
  width: 100%;
  height: 300px;
  line-height: 1.4;
  font-size: 16px;
  font-family: "Courier New", monospace;
  resize: none;
}
#_cbhtml .is-modal.audiolink div.is-modal-content,
.is-ui .is-modal.audiolink div.is-modal-content {
  max-width: 550px;
}
#_cbhtml .is-modal.audiolink .audio-url,
.is-ui .is-modal.audiolink .audio-url {
  position: relative;
  height: 43px;
  display: flex;
  flex-direction: row;
}
#_cbhtml .is-modal.audiolink .audio-url .inpAudioLinkSource,
.is-ui .is-modal.audiolink .audio-url .inpAudioLinkSource {
  width: 100%;
}
#_cbhtml .is-modal.audiolink .input-select,
.is-ui .is-modal.audiolink .input-select {
  width: 50px;
  height: 43px;
  font-size: 20px;
  height: 43px;
  width: 50px;
  border-left: none;
  flex: none;
}
#_cbhtml .is-modal.audiolink .input-upload,
.is-ui .is-modal.audiolink .input-upload {
  width: 50px;
  height: 43px;
}
#_cbhtml .is-modal.createlink label,
.is-ui .is-modal.createlink label {
  font-size: 14px;
  display: block;
}
#_cbhtml .is-modal.createlink div.is-modal-content,
.is-ui .is-modal.createlink div.is-modal-content {
  max-width: 526px;
}
#_cbhtml .is-modal.createlink .link-src,
.is-ui .is-modal.createlink .link-src {
  position: relative;
  height: 43px;
  display: flex;
  flex-direction: row;
}
#_cbhtml .is-modal.createlink .link-src .input-url,
.is-ui .is-modal.createlink .link-src .input-url {
  width: 100%;
}
#_cbhtml .is-modal.createlink .input-select,
.is-ui .is-modal.createlink .input-select {
  width: 60px;
  font-size: 20px;
  height: 43px;
  width: 50px;
  border-left: none;
  flex: none;
}
#_cbhtml .is-modal.viewconfig .div-themes,
.is-ui .is-modal.viewconfig .div-themes {
  display: flex;
  flex-flow: wrap;
  width: 198px;
  box-sizing: content-box;
}
#_cbhtml .is-modal.viewconfig button.input-setcolor,
.is-ui .is-modal.viewconfig button.input-setcolor {
  width: 27px;
  height: 21px;
  border: transparent 2px solid;
  border-radius: 0px;
}
#_cbhtml .is-modal.viewconfig button.input-setcolor:focus,
.is-ui .is-modal.viewconfig button.input-setcolor:focus {
  outline-offset: -2px;
}
#_cbhtml .is-modal .div-anyfile-upload,
#_cbhtml .is-modal [class^=image-larger],
.is-ui .is-modal .div-anyfile-upload,
.is-ui .is-modal [class^=image-larger] {
  flex: none;
}
#_cbhtml .is-tool#divImageTool, #_cbhtml .is-tool.is-video-tool, #_cbhtml .is-tool.is-audio-tool, #_cbhtml .is-tool.is-iframe-tool,
.is-ui .is-tool#divImageTool,
.is-ui .is-tool.is-video-tool,
.is-ui .is-tool.is-audio-tool,
.is-ui .is-tool.is-iframe-tool {
  background: rgba(255, 255, 255, 0.97) !important;
  border: transparent 1px solid;
  box-shadow: -1px 5px 8px 0px rgba(0, 0, 0, 0.08);
  border-radius: 20px;
}
#_cbhtml .is-tool#divImageTool > div, #_cbhtml .is-tool#divImageTool > button, #_cbhtml .is-tool.is-video-tool > div, #_cbhtml .is-tool.is-video-tool > button, #_cbhtml .is-tool.is-audio-tool > div, #_cbhtml .is-tool.is-audio-tool > button, #_cbhtml .is-tool.is-iframe-tool > div, #_cbhtml .is-tool.is-iframe-tool > button,
.is-ui .is-tool#divImageTool > div,
.is-ui .is-tool#divImageTool > button,
.is-ui .is-tool.is-video-tool > div,
.is-ui .is-tool.is-video-tool > button,
.is-ui .is-tool.is-audio-tool > div,
.is-ui .is-tool.is-audio-tool > button,
.is-ui .is-tool.is-iframe-tool > div,
.is-ui .is-tool.is-iframe-tool > button {
  width: 35px;
  height: 35px;
  background: transparent;
}
#_cbhtml .is-tool#divImageTool svg, #_cbhtml .is-tool.is-video-tool svg, #_cbhtml .is-tool.is-audio-tool svg, #_cbhtml .is-tool.is-iframe-tool svg,
.is-ui .is-tool#divImageTool svg,
.is-ui .is-tool.is-video-tool svg,
.is-ui .is-tool.is-audio-tool svg,
.is-ui .is-tool.is-iframe-tool svg {
  width: 17px;
  height: 17px;
  fill: #111 !important;
  color: #111 !important;
}
#_cbhtml .is-tool.is-video-tool > button, #_cbhtml .is-tool.is-audio-tool > button, #_cbhtml .is-tool.is-iframe-tool > button,
.is-ui .is-tool.is-video-tool > button,
.is-ui .is-tool.is-audio-tool > button,
.is-ui .is-tool.is-iframe-tool > button {
  width: 35px !important;
  height: 35px !important;
}
#_cbhtml .is-tool#divImageResizer,
.is-ui .is-tool#divImageResizer {
  background: rgba(0, 0, 0, 0.01);
  width: 1px;
  height: 1px;
  z-index: 10;
  touch-action: none;
}
#_cbhtml .is-tool#divImageResizerOverlay,
.is-ui .is-tool#divImageResizerOverlay {
  background: rgba(0, 0, 0, 0.01);
  position: fixed;
  width: 100vw;
  height: 100vh;
  top: 0;
  left: 0;
  z-index: 9;
  display: none;
}
#_cbhtml .is-tool.is-spacer-tool,
.is-ui .is-tool.is-spacer-tool {
  border: none;
  box-shadow: -1px 5px 8px 0px rgba(0, 0, 0, 0.08);
  border-radius: 20px;
  overflow: hidden;
}
#_cbhtml .is-tool.is-spacer-tool > button,
.is-ui .is-tool.is-spacer-tool > button {
  width: 40px;
  height: 35px;
  background: rgba(255, 255, 255, 0.97) !important;
}
#_cbhtml .is-tool.is-spacer-tool > button svg,
.is-ui .is-tool.is-spacer-tool > button svg {
  fill: #111 !important;
  color: #111 !important;
}
#_cbhtml .is-tool.is-table-tool,
.is-ui .is-tool.is-table-tool {
  border: none;
  background: none;
  border-radius: 20px;
  box-shadow: -1px 5px 8px 0px rgba(0, 0, 0, 0.08);
}
#_cbhtml .is-tool.is-table-tool:hover,
.is-ui .is-tool.is-table-tool:hover {
  z-index: 10001 !important;
}
#_cbhtml .is-tool.is-table-tool > button,
.is-ui .is-tool.is-table-tool > button {
  width: 35px;
  height: 35px;
  background: rgba(255, 255, 255, 0.97) !important;
}
#_cbhtml .is-tool.is-table-tool > button svg,
.is-ui .is-tool.is-table-tool > button svg {
  width: 17px !important;
  height: 17px !important;
  fill: #111 !important;
  color: #111 !important;
}
#_cbhtml .is-tool.is-code-tool, #_cbhtml .is-tool.is-module-tool, #_cbhtml .is-tool.is-svg-tool,
.is-ui .is-tool.is-code-tool,
.is-ui .is-tool.is-module-tool,
.is-ui .is-tool.is-svg-tool {
  border: none;
  background: none;
  border-radius: 20px;
  box-shadow: -1px 5px 8px 0px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}
#_cbhtml .is-tool.is-code-tool > button, #_cbhtml .is-tool.is-module-tool > button, #_cbhtml .is-tool.is-svg-tool > button,
.is-ui .is-tool.is-code-tool > button,
.is-ui .is-tool.is-module-tool > button,
.is-ui .is-tool.is-svg-tool > button {
  width: 35px;
  height: 35px;
  background: rgba(255, 255, 255, 0.97) !important;
}
#_cbhtml .is-tool.is-code-tool > button svg, #_cbhtml .is-tool.is-module-tool > button svg, #_cbhtml .is-tool.is-svg-tool > button svg,
.is-ui .is-tool.is-code-tool > button svg,
.is-ui .is-tool.is-module-tool > button svg,
.is-ui .is-tool.is-svg-tool > button svg {
  width: 17px !important;
  height: 17px !important;
  fill: #111 !important;
  color: #111 !important;
}
#_cbhtml .is-tool#divLinkTool, #_cbhtml .is-tool#divButtonTool,
.is-ui .is-tool#divLinkTool,
.is-ui .is-tool#divButtonTool {
  background: rgba(255, 255, 255, 0.97) !important;
  box-shadow: none;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: -1px 5px 8px 0px rgba(0, 0, 0, 0.08);
  margin-top: -10px;
  margin-left: 7px;
}
#_cbhtml .is-tool#divLinkTool button, #_cbhtml .is-tool#divButtonTool button,
.is-ui .is-tool#divLinkTool button,
.is-ui .is-tool#divButtonTool button {
  width: 37px;
  height: 37px;
  background: transparent !important;
}
#_cbhtml .is-tool#divLinkTool button svg, #_cbhtml .is-tool#divButtonTool button svg,
.is-ui .is-tool#divLinkTool button svg,
.is-ui .is-tool#divButtonTool button svg {
  fill: #111 !important;
  color: #111 !important;
}
#_cbhtml .dot,
.is-ui .dot {
  height: 7px;
  width: 7px;
  border-radius: 50%;
  display: inline-block;
  margin: 25px 2px 0;
  -webkit-animation: jump 1.5s linear infinite;
}
@-webkit-keyframes jump {
  0%, 100% {
    transform: translateY(0px);
  }
  20% {
    transform: translateY(-10px);
  }
  40% {
    transform: translateY(0px);
  }
}
#_cbhtml .dot:nth-of-type(2),
.is-ui .dot:nth-of-type(2) {
  -webkit-animation-delay: 0.2s;
}
#_cbhtml .dot:nth-of-type(3),
.is-ui .dot:nth-of-type(3) {
  -webkit-animation-delay: 0.4s;
}
#_cbhtml #divImageProgress,
.is-ui #divImageProgress {
  display: none;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  box-sizing: border-box;
  background: rgba(0, 0, 0, 0.15);
  transition: none;
}
#_cbhtml #divImageProgress > div,
.is-ui #divImageProgress > div {
  display: table-cell;
  vertical-align: middle;
  text-align: center;
}
#_cbhtml #divImageProgress .dot,
.is-ui #divImageProgress .dot {
  background-color: #fff;
  margin: 10px 2px 0;
}
#_cbhtml .is-side,
.is-ui .is-side {
  display: block;
  position: fixed;
  top: 0;
  right: -367px;
  left: auto;
  width: 365px;
  height: 100%;
  border: none;
  box-shadow: rgba(0, 0, 0, 0.05) 0 0 16px 0px;
  box-sizing: border-box;
  background: #fff;
  transition: all ease 0.3s;
  font-family: sans-serif;
  font-size: 13px;
  letter-spacing: 1px;
  z-index: 10003;
  outline: none;
}
#_cbhtml .is-side button,
.is-ui .is-side button {
  color: #000;
  background: #fff;
  box-shadow: 0px 3px 6px -6px rgba(0, 0, 0, 0.32);
}
#_cbhtml .is-side button:hover,
.is-ui .is-side button:hover {
  background: #f1f1f1;
}
#_cbhtml .is-side button.on,
.is-ui .is-side button.on {
  background: #f1f1f1;
}
#_cbhtml .is-side.active,
.is-ui .is-side.active {
  right: 0;
}
#_cbhtml .is-side.fromleft,
.is-ui .is-side.fromleft {
  left: -367px;
  right: auto;
  border: none;
  border-right: 1px solid #e0e0e0;
}
#_cbhtml .is-side.fromleft.active,
.is-ui .is-side.fromleft.active {
  left: 0;
}
#_cbhtml .is-side > div,
.is-ui .is-side > div {
  width: 100%;
  background: none;
  border: none;
  box-shadow: none;
  padding: 0;
}
#_cbhtml button,
#_cbhtml .is-btn,
.is-ui button,
.is-ui .is-btn {
  color: #121212;
  background: #f7f7f7;
  box-shadow: 0px 3px 6px -6px rgba(0, 0, 0, 0.32);
  width: auto;
  height: 43px;
  border: none;
  font-family: sans-serif;
  font-size: 12px;
  letter-spacing: 1px;
  font-weight: 300;
  opacity: 1;
  line-height: 1;
  display: inline-block;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  cursor: pointer;
  text-transform: none;
  text-align: center;
  position: relative;
  border-radius: 1px;
  user-select: none;
  -moz-user-select: none;
  -khtml-user-select: none;
  -webkit-user-select: none;
  -o-user-select: none;
  white-space: nowrap;
  display: flex;
  align-items: center;
  justify-content: center;
}
#_cbhtml button:hover,
#_cbhtml .is-btn:hover,
.is-ui button:hover,
.is-ui .is-btn:hover {
  background: #f9f9f9;
}
#_cbhtml button svg,
#_cbhtml .is-btn svg,
.is-ui button svg,
.is-ui .is-btn svg {
  fill: #000;
}
#_cbhtml button:focus,
#_cbhtml .is-btn:focus,
.is-ui button:focus,
.is-ui .is-btn:focus {
  outline-offset: 0;
}
#_cbhtml button.fullwidth,
#_cbhtml .is-btn.fullwidth,
.is-ui button.fullwidth,
.is-ui .is-btn.fullwidth {
  width: 100%;
}
#_cbhtml button.classic,
#_cbhtml .is-btn.classic,
.is-ui button.classic,
.is-ui .is-btn.classic {
  width: 100%;
  height: 43px;
  display: block;
  background: transparent;
  box-shadow: 0px 3px 6px -6px rgba(0, 0, 0, 0.32);
}
#_cbhtml button.classic:hover,
#_cbhtml .is-btn.classic:hover,
.is-ui button.classic:hover,
.is-ui .is-btn.classic:hover {
  background: #f1f1f1;
}
#_cbhtml button.classic-primary,
#_cbhtml .is-btn.classic-primary,
.is-ui button.classic-primary,
.is-ui .is-btn.classic-primary {
  display: inline-block;
  width: auto;
  height: 43px;
  padding-left: 25px;
  padding-right: 25px;
  min-width: 110px;
  background: #f5f5f5;
  border: transparent 1px solid;
  box-shadow: 0px 3px 6px -6px rgba(0, 0, 0, 0.32);
}
#_cbhtml button.classic-primary:hover,
#_cbhtml .is-btn.classic-primary:hover,
.is-ui button.classic-primary:hover,
.is-ui .is-btn.classic-primary:hover {
  background: #f1f1f1;
}
#_cbhtml button.classic-secondary,
#_cbhtml .is-btn.classic-secondary,
.is-ui button.classic-secondary,
.is-ui .is-btn.classic-secondary {
  display: inline-block;
  width: auto;
  height: 43px;
  padding-left: 25px;
  padding-right: 25px;
  background: transparent;
  border: transparent 1px solid;
  box-shadow: 0px 3px 6px -6px rgba(0, 0, 0, 0.32);
}
#_cbhtml button.classic-secondary:hover,
#_cbhtml .is-btn.classic-secondary:hover,
.is-ui button.classic-secondary:hover,
.is-ui .is-btn.classic-secondary:hover {
  background: #f1f1f1;
}
#_cbhtml textarea:not(.monaco-mouse-cursor-text),
.is-ui textarea:not(.monaco-mouse-cursor-text) {
  font-family: courier;
  font-size: 15px;
  line-height: 2;
  letter-spacing: 1px;
  margin: 0;
  padding: 8px 16px;
  box-sizing: border-box;
  border: 1px solid rgba(0, 0, 0, 0.06);
  background-color: #fafafa;
  color: #121212;
  border-radius: 1px;
}
#_cbhtml textarea:not(.monaco-mouse-cursor-text):focus,
.is-ui textarea:not(.monaco-mouse-cursor-text):focus {
  outline: #3e93f7 2px solid;
  outline-offset: 0;
  box-shadow: none;
}
#_cbhtml .find-part .monaco-inputbox textarea,
#_cbhtml .replace-part .monaco-inputbox textarea,
.is-ui .find-part .monaco-inputbox textarea,
.is-ui .replace-part .monaco-inputbox textarea {
  padding: 0 !important;
  line-height: 1.4 !important;
}
#_cbhtml select,
.is-ui select {
  font-size: 13px;
  letter-spacing: 1px;
  height: 35px;
  line-height: 1.7;
  color: #4a4a4a;
  border-radius: 5px;
  border: none;
  background-color: #f6f6f6;
  width: auto;
  display: inline-block;
  background-image: none;
  -webkit-appearance: menulist;
  -moz-appearance: menulist;
  appearance: menulist;
  padding: 0 5px;
  border-radius: 1px;
}
#_cbhtml select option,
.is-ui select option {
  background: rgba(255, 255, 255, 0.93);
}
#_cbhtml select:focus,
.is-ui select:focus {
  outline: #3e93f7 2px solid;
  outline-offset: -2px;
  box-shadow: none;
}
#_cbhtml input[type=text],
.is-ui input[type=text] {
  width: 100%;
  height: 43px;
  box-sizing: border-box;
  margin: 0;
  font-family: sans-serif;
  font-size: 15px;
  letter-spacing: 1px;
  padding: 0;
  padding-left: 8px;
  color: #121212;
  display: inline-block;
  border: none;
  border-bottom: none;
  border-radius: 1px;
  background-color: #f6f6f6;
}
#_cbhtml input[type=text]:focus,
.is-ui input[type=text]:focus {
  outline: #3e93f7 2px solid;
  outline-offset: -2px;
  box-shadow: none;
}
#_cbhtml input[type=text] [type=checkbox], #_cbhtml input[type=text] [type=radio],
.is-ui input[type=text] [type=checkbox],
.is-ui input[type=text] [type=radio] {
  position: relative;
  opacity: 1;
  margin-top: 0;
  margin-bottom: 0;
}
#_cbhtml input[type=radio]:focus,
.is-ui input[type=radio]:focus {
  outline: #3e93f7 1px solid;
}
#_cbhtml label,
.is-ui label {
  font-size: 13px;
  letter-spacing: 1px;
  padding: 0;
}
#_cbhtml label.label-checkbox,
.is-ui label.label-checkbox {
  display: flex;
  align-items: center;
  line-height: 1;
  cursor: pointer;
}
#_cbhtml label.label-checkbox input,
.is-ui label.label-checkbox input {
  margin: 0;
  margin-right: 5px;
}
#_cbhtml .is-rangeslider,
.is-ui .is-rangeslider {
  -webkit-appearance: none;
  width: 100%;
  height: 24px;
  background: #e3e3e3;
  outline: none;
  border: none !important;
  opacity: 1;
  -webkit-transition: 0.2s;
  transition: opacity 0.2s;
  margin: 2px !important;
  border-radius: 2px !important;
}
#_cbhtml .is-rangeslider:hover,
.is-ui .is-rangeslider:hover {
  opacity: 1;
}
#_cbhtml .is-rangeslider:focus,
.is-ui .is-rangeslider:focus {
  outline: #3e93f7 2px solid;
  outline-offset: 2px;
}
#_cbhtml .is-rangeslider::-webkit-slider-thumb,
.is-ui .is-rangeslider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 25px !important;
  height: 24px !important;
  border-radius: 3px !important;
  background: #0e75de;
  cursor: pointer;
  margin: 0 !important;
}
#_cbhtml .is-rangeslider::-moz-range-thumb,
.is-ui .is-rangeslider::-moz-range-thumb {
  width: 25px !important;
  height: 24px !important;
  border-radius: 3px !important;
  background: #0e75de;
  cursor: pointer;
  margin: 0 !important;
}
#_cbhtml .is-rangeslider::-webkit-slider-runnable-track,
.is-ui .is-rangeslider::-webkit-slider-runnable-track {
  height: auto !important;
  background: none !important;
  border: none !important;
}
#_cbhtml .rangeSlider, #_cbhtml .rangeSlider__fill,
.is-ui .rangeSlider,
.is-ui .rangeSlider__fill {
  display: block;
}
#_cbhtml .rangeSlider,
.is-ui .rangeSlider {
  position: relative;
  background-color: transparent;
  outline: 1px solid rgba(0, 0, 0, 0.06);
}
#_cbhtml .rangeSlider__horizontal,
.is-ui .rangeSlider__horizontal {
  height: 24px;
  width: 100%;
}
#_cbhtml .rangeSlider__vertical,
.is-ui .rangeSlider__vertical {
  height: 100%;
  width: 20px;
}
#_cbhtml .rangeSlider--disabled,
.is-ui .rangeSlider--disabled {
  filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=40);
  opacity: 0.4;
}
#_cbhtml .rangeSlider__fill,
.is-ui .rangeSlider__fill {
  background-color: transparent;
  position: absolute;
}
#_cbhtml .rangeSlider__fill__horizontal,
.is-ui .rangeSlider__fill__horizontal {
  height: 100%;
  top: 0;
  left: 0;
}
#_cbhtml .rangeSlider__fill__vertical,
.is-ui .rangeSlider__fill__vertical {
  width: 100%;
  bottom: 0;
  left: 0;
}
#_cbhtml .rangeSlider__handle,
.is-ui .rangeSlider__handle {
  cursor: pointer;
  display: inline-block;
  width: 25px;
  height: 24px;
  position: absolute;
  border: 1px solid transparent;
  background: rgba(15, 86, 222, 0.8) linear-gradient(rgba(255, 255, 255, 0), rgba(0, 0, 0, 0.04));
  z-index: 1;
}
#_cbhtml .rangeSlider__handle__horizontal,
.is-ui .rangeSlider__handle__horizontal {
  top: -1px;
}
#_cbhtml .rangeSlider__handle__vertical,
.is-ui .rangeSlider__handle__vertical {
  left: -10px;
  bottom: 0;
}
#_cbhtml .rangeSlider__handle:after,
.is-ui .rangeSlider__handle:after {
  content: "";
  display: block;
  width: 10px;
  height: 10px;
  margin: auto;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  border-radius: 50%;
}
#_cbhtml input[type=range]:focus + .rangeSlider .rangeSlider__handle,
.is-ui input[type=range]:focus + .rangeSlider .rangeSlider__handle {
  box-shadow: 0 0 8px rgba(142, 68, 173, 0.9);
}
#_cbhtml .rangeSlider__buffer,
.is-ui .rangeSlider__buffer {
  position: absolute;
  top: 3px;
  height: 14px;
  border-radius: 10px;
}
#_cbhtml .dot-1,
.is-ui .dot-1 {
  background: #f0f0f0;
  width: 7px;
  height: 8px;
}
#_cbhtml .dot-2,
.is-ui .dot-2 {
  background: #fff;
  width: 7px;
  height: 8px;
}
#_cbhtml .dot-3,
.is-ui .dot-3 {
  background: #f0f0f0;
  width: 7px;
  height: 7px;
}
#_cbhtml .dot-4,
.is-ui .dot-4 {
  background: #fff;
  width: 7px;
  height: 7px;
}
#_cbhtml .is-tabs,
.is-ui .is-tabs {
  white-space: nowrap;
  padding: 20px;
  padding-bottom: 5px;
  padding-top: 10px;
  box-sizing: border-box;
  font-family: sans-serif;
  font-size: 11px;
  line-height: 1.8 !important;
  text-transform: uppercase;
  letter-spacing: 1px;
  background: #fafafa;
  display: flex;
  flex-flow: wrap;
}
#_cbhtml .is-tabs a,
.is-ui .is-tabs a {
  display: inline-block;
  padding: 3px 1px 0;
  color: #4a4a4a;
  border-bottom: transparent 1px solid;
  margin: 0 15px 13px 0;
  text-decoration: none;
  transition: box-shadow ease 0.3s;
  outline: none;
}
#_cbhtml .is-tabs a.active,
.is-ui .is-tabs a.active {
  background: transparent;
  box-shadow: none;
  cursor: default;
  border-bottom: #595959 1px solid;
}
#_cbhtml .is-tabs a:focus,
.is-ui .is-tabs a:focus {
  outline: none;
  background: rgba(0, 0, 0, 0.05);
}
#_cbhtml .is-tab-content,
.is-ui .is-tab-content {
  display: none;
  padding: 20px;
  flex-direction: column;
}
#_cbhtml .is-tab-content[tabindex="-1"]:focus,
.is-ui .is-tab-content[tabindex="-1"]:focus {
  outline: none;
}
#_cbhtml .is-tabs-more,
.is-ui .is-tabs-more {
  box-sizing: border-box;
  width: 150px;
  position: absolute;
  list-style: none;
  padding: 0;
  margin: 0;
  top: 0;
  left: 0;
  background: #fff;
  display: none;
  z-index: 1;
  border: 1px solid #f2f2f2;
  box-shadow: 3px 4px 9px 0px rgba(0, 0, 0, 0.06);
  outline: none;
}
#_cbhtml .is-tabs-more li,
.is-ui .is-tabs-more li {
  display: flex;
  align-items: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-family: sans-serif;
  font-size: 11px;
  text-transform: uppercase;
  letter-spacing: 1px;
  color: #4a4a4a;
  padding: 10px;
  text-decoration: none;
  text-align: center;
  cursor: pointer;
  outline: none;
}
#_cbhtml .is-tabs-more li:hover, #_cbhtml .is-tabs-more li:focus, #_cbhtml .is-tabs-more li.active,
.is-ui .is-tabs-more li:hover,
.is-ui .is-tabs-more li:focus,
.is-ui .is-tabs-more li.active {
  background: whitesmoke;
}
#_cbhtml .inp-snippets.custom-select,
.is-ui .inp-snippets.custom-select {
  width: 100%;
}
#_cbhtml .inp-snippets.custom-select .select-styled,
.is-ui .inp-snippets.custom-select .select-styled {
  border: none;
  border-bottom: 1px solid #bababa38;
  padding: 0 6px 0 14px;
  outline-offset: -2px;
  box-shadow: none;
}
#_cbhtml .inp-snippets.custom-select .select-options,
.is-ui .inp-snippets.custom-select .select-options {
  max-height: 300px;
  top: 46px;
}
#_cbhtml .inp-snippets.custom-select .select-options li,
.is-ui .inp-snippets.custom-select .select-options li {
  font-size: 14px !important;
  padding: 0 8px 0 14px;
}
#_cbhtml #divSnippetList,
.is-ui #divSnippetList {
  right: -230px;
  width: 230px;
  background: #fff;
  border-left: 1px solid #e6e6e6;
  box-shadow: rgba(0, 0, 0, 0.03) 0 0 10px 0px;
}
#_cbhtml #divSnippetList.active,
.is-ui #divSnippetList.active {
  right: 0;
}
#_cbhtml #divSnippetList.active #divSnippetScrollUp,
.is-ui #divSnippetList.active #divSnippetScrollUp {
  display: block;
}
#_cbhtml #divSnippetList.active #divSnippetScrollDown,
.is-ui #divSnippetList.active #divSnippetScrollDown {
  display: block;
}
#_cbhtml #divSnippetList #divSnippetHandle,
.is-ui #divSnippetList #divSnippetHandle {
  position: absolute;
  width: 40px;
  height: 40px;
  top: 170px;
  left: -40px;
  background: #fff;
  border: 1px solid #dfdfdf;
  border-right: none;
  line-height: 39px;
  text-align: center;
  box-shadow: rgba(0, 0, 0, 0.025) -4px 2px 5px 0px;
  cursor: pointer;
}
#_cbhtml #divSnippetList.fromleft,
.is-ui #divSnippetList.fromleft {
  left: -230px;
}
#_cbhtml #divSnippetList.fromleft.active,
.is-ui #divSnippetList.fromleft.active {
  left: 0;
}
#_cbhtml #divSnippetList.fromleft #divSnippetHandle,
.is-ui #divSnippetList.fromleft #divSnippetHandle {
  border-left: none;
  border-right: 1px solid #e8e8e8;
  left: auto;
  right: -41px;
  box-shadow: none !important;
}
#_cbhtml #divSnippetScrollUp,
#_cbhtml #divSnippetScrollDown,
.is-ui #divSnippetScrollUp,
.is-ui #divSnippetScrollDown {
  display: none;
  background: rgba(0, 0, 0, 0.12);
  width: 45px;
  height: 45px;
  line-height: 45px;
  color: #a9a9a9;
  position: fixed;
  z-index: 100000;
  text-align: center;
  font-size: 12px;
  cursor: pointer;
  font-family: sans-serif;
}
#_cbhtml .is-design-list,
.is-ui .is-design-list {
  height: 100%;
  margin: 0;
  padding: 0 0 20px !important;
  box-sizing: border-box;
  overflow-y: auto;
  overflow-x: hidden;
  text-align: center;
  border-top: transparent 46px solid !important;
  outline: none;
}
#_cbhtml .is-design-list > div,
.is-ui .is-design-list > div {
  width: 170px;
  overflow: hidden;
  margin: 22px 22px 0;
  cursor: move;
  display: block;
  opacity: 1;
  outline: #ebebeb 1px solid !important;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.03);
  position: relative;
}
#_cbhtml .is-design-list > div.hide,
.is-ui .is-design-list > div.hide {
  display: none;
  height: 0;
  opacity: 0;
  overflow: hidden;
  transition: height 350ms ease-in-out, opacity 750ms ease-in-out;
}
#_cbhtml .is-design-list > div img,
.is-ui .is-design-list > div img {
  box-shadow: none;
  display: block;
  transition: all 0.2s ease-in-out;
  box-sizing: border-box;
  width: 100%;
}
#_cbhtml .is-design-list > div .is-overlay,
.is-ui .is-design-list > div .is-overlay {
  position: absolute;
  left: 0px;
  top: 0px;
  width: 100%;
  height: 100%;
}
#_cbhtml .is-design-list > div .is-overlay:after,
.is-ui .is-design-list > div .is-overlay:after {
  background: rgba(0, 0, 0, 0.02);
  position: absolute;
  content: "";
  display: block;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  transition: all 0.3s ease-in-out;
  opacity: 0;
}
#_cbhtml .is-design-list > div:hover .is-overlay:after,
.is-ui .is-design-list > div:hover .is-overlay:after {
  opacity: 0.9;
}
#_cbhtml .is-design-list .snippet-item.sortable-drag:hover,
.is-ui .is-design-list .snippet-item.sortable-drag:hover {
  background-color: rgba(0, 0, 0, 0.06);
}
#_cbhtml .is-selectbox,
.is-ui .is-selectbox {
  height: 40px;
  box-sizing: border-box;
  padding: 0 0 0 20px;
  color: #000;
  background: #f6f6f6;
  box-shadow: none;
  line-height: 40px !important;
  font-size: 14px;
  font-weight: 400;
  cursor: pointer;
}
#_cbhtml .is-selectbox:hover,
.is-ui .is-selectbox:hover {
  background: #f2f2f2;
}
#_cbhtml .is-selectbox svg,
.is-ui .is-selectbox svg {
  fill: #000;
}
#_cbhtml .is-selectbox-options,
.is-ui .is-selectbox-options {
  width: 100%;
  height: 350px;
  border: #e8e8e8 1px solid;
  overflow-y: auto;
  overflow-x: hidden;
  background-color: rgba(255, 255, 255, 0.93);
  display: none;
}
#_cbhtml .is-selectbox-options > div,
.is-ui .is-selectbox-options > div {
  color: #000;
  height: 35px;
  padding: 0 0 0 20px;
  line-height: 35px !important;
  font-size: 13px;
  font-weight: 300;
  cursor: pointer;
}
#_cbhtml .is-selectbox-options > div:hover,
.is-ui .is-selectbox-options > div:hover {
  background: whitesmoke;
}
#_cbhtml .is-selectbox-options > div.selected,
.is-ui .is-selectbox-options > div.selected {
  background: whitesmoke;
}
#_cbhtml .is-dropdown,
.is-ui .is-dropdown {
  width: 100%;
  position: relative;
  cursor: pointer;
}
#_cbhtml .is-dropdown .dropdown-toggle,
.is-ui .is-dropdown .dropdown-toggle {
  width: 100%;
  height: 40px;
  box-sizing: border-box;
  padding: 0 0 0 15px;
  color: #000;
  background: #f6f6f6;
  box-shadow: none;
  font-size: 14px;
  cursor: pointer;
  appearance: none;
  display: flex;
  justify-content: flex-start;
  border-radius: 2px;
  position: relative;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: pointer;
}
#_cbhtml .is-dropdown .dropdown-toggle:hover,
.is-ui .is-dropdown .dropdown-toggle:hover {
  background: #f2f2f2;
}
#_cbhtml .is-dropdown .dropdown-toggle svg,
.is-ui .is-dropdown .dropdown-toggle svg {
  fill: #000;
}
#_cbhtml .is-dropdown .dropdown-toggle.no-outline,
.is-ui .is-dropdown .dropdown-toggle.no-outline {
  outline: none !important;
}
#_cbhtml .is-dropdown .dropdown-toggle::after,
.is-ui .is-dropdown .dropdown-toggle::after {
  content: "";
  border-width: 5px;
  border-radius: 3px;
  border-style: solid;
  border-color: transparent;
  border-top-color: #000;
  position: absolute;
  right: 12px;
  top: calc(50% + 3px);
  transform: translateY(-50%);
}
#_cbhtml .is-dropdown .dropdown-toggle.active::after,
.is-ui .is-dropdown .dropdown-toggle.active::after {
  border-color: transparent;
  border-bottom-color: #000;
  top: calc(50% - 3px);
}
#_cbhtml .is-dropdown .dropdown-menu,
.is-ui .is-dropdown .dropdown-menu {
  width: 100%;
  height: 250px;
  border: #e8e8e8 1px solid;
  background-color: rgba(255, 255, 255, 0.93);
  list-style: none;
  padding: 0;
  margin: 0;
  border-radius: 2px;
  display: none;
  overflow-y: auto;
  overflow-x: hidden;
}
#_cbhtml .is-dropdown .dropdown-menu li,
.is-ui .is-dropdown .dropdown-menu li {
  background-color: rgba(255, 255, 255, 0.93);
  height: 38px;
  display: flex;
  align-items: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #000;
  padding: 0 0 0 15px;
  font-size: 13px;
}
#_cbhtml .is-dropdown .dropdown-menu li:hover, #_cbhtml .is-dropdown .dropdown-menu li:focus,
.is-ui .is-dropdown .dropdown-menu li:hover,
.is-ui .is-dropdown .dropdown-menu li:focus {
  outline: none;
  background: whitesmoke;
}
#_cbhtml .is-dropdown .dropdown-menu li.selected,
.is-ui .is-dropdown .dropdown-menu li.selected {
  background: whitesmoke;
}
#_cbhtml .is-dropdown .dropdown-menu[aria-expanded=true],
.is-ui .is-dropdown .dropdown-menu[aria-expanded=true] {
  display: block;
}
#_cbhtml .elementstyles,
.is-ui .elementstyles {
  width: 300px;
  font-size: 13px;
  z-index: 10004;
  /*10005*/
}
#_cbhtml .elementstyles .elm-list,
.is-ui .elementstyles .elm-list {
  font-size: 12px;
  line-height: 1.3;
  padding-bottom: 15px;
  color: #0096f1;
  border-bottom: rgba(0, 0, 0, 0.06) 1px solid;
}
#_cbhtml .elementstyles .elm-list a,
.is-ui .elementstyles .elm-list a {
  font-size: 13px;
  color: #0096f1;
  text-decoration: none;
  padding: 0 3px;
}
#_cbhtml .elementstyles .elm-list a.active,
.is-ui .elementstyles .elm-list a.active {
  color: #0096f1;
  background: #f0f0f0;
}
#_cbhtml .elementstyles div.is-separator,
.is-ui .elementstyles div.is-separator {
  width: 100%;
  border-top: #f0f0f0 1px solid;
  margin-bottom: 10px;
  margin-top: 10px;
}
#_cbhtml .elementstyles .is-settings,
.is-ui .elementstyles .is-settings {
  margin-top: 7px;
}
#_cbhtml .elementstyles .is-settings > div,
.is-ui .elementstyles .is-settings > div {
  display: flex;
  align-items: center;
  min-height: 35px;
}
#_cbhtml .elementstyles .is-settings > div.is-label,
.is-ui .elementstyles .is-settings > div.is-label {
  height: auto;
  font-family: sans-serif;
  font-size: 13px;
  font-weight: 300;
  letter-spacing: 1px;
  margin: 10px 0 3px;
}
#_cbhtml .elementstyles .is-settings button,
.is-ui .elementstyles .is-settings button {
  width: auto;
  height: 35px;
  font-size: 10px;
  line-height: 1;
  text-transform: uppercase;
  padding: 1px 20px;
  box-sizing: border-box;
  border: none;
}
#_cbhtml .elementstyles .is-settings button.is-btn-color,
.is-ui .elementstyles .is-settings button.is-btn-color {
  width: 35px;
  height: 35px;
  padding: 0;
  background: rgba(255, 255, 255, 0.2);
  border: rgba(0, 0, 0, 0.09) 1px solid;
}
#_cbhtml .elementstyles .is-settings button span,
.is-ui .elementstyles .is-settings button span {
  margin-left: 5px;
}
#_cbhtml .elementstyles .is-settings label,
.is-ui .elementstyles .is-settings label {
  font-size: 13px;
  color: inherit;
}
#_cbhtml .elementstyles .is-settings input[type=text],
.is-ui .elementstyles .is-settings input[type=text] {
  border-radius: 0;
  height: 35px;
  font-size: 13px;
  margin-right: 2px;
  border-radius: 1px;
}
#_cbhtml .elementstyles .is-settings select,
.is-ui .elementstyles .is-settings select {
  border-radius: 0;
  height: 35px;
  margin: 0;
  border-radius: 1px;
}
#_cbhtml .elementstyles .is-settings .elm-bgimage-preview,
.is-ui .elementstyles .is-settings .elm-bgimage-preview {
  max-width: 120px;
  min-height: unset;
}
#_cbhtml .elementstyles .is-settings .elm-bgimage-preview img,
.is-ui .elementstyles .is-settings .elm-bgimage-preview img {
  max-width: 100%;
  max-height: 100%;
}
#_cbhtml .editstyles,
.is-ui .editstyles {
  display: none;
  position: fixed;
  overflow: hidden;
  width: 280px;
  height: 390px;
  margin: 0px;
  top: 100px;
  left: auto;
  right: 320px;
  z-index: 10005;
  box-sizing: content-box;
  flex-direction: column;
}
#_cbhtml .editstyles.active,
.is-ui .editstyles.active {
  display: flex;
}
#_cbhtml .editstyles > div:not(.is-draggable),
.is-ui .editstyles > div:not(.is-draggable) {
  width: 100%;
  background: transparent;
  border: none;
  box-shadow: none;
  padding: initial;
  box-sizing: border-box;
}
#_cbhtml .is-modal.editstyles div.is-draggable,
.is-ui .is-modal.editstyles div.is-draggable {
  position: absolute;
  top: 0;
  left: 0;
  background: none;
  cursor: move;
  height: 20px;
  width: 100%;
  z-index: 1;
}
#_cbhtml .is-pop-overlay,
.is-ui .is-pop-overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0);
}
#_cbhtml .is-pop-overlay.pop1,
.is-ui .is-pop-overlay.pop1 {
  z-index: 10005 !important;
}
#_cbhtml .is-pop-overlay.pop2,
.is-ui .is-pop-overlay.pop2 {
  z-index: 10006 !important;
}
#_cbhtml .is-pop-overlay.pop3,
.is-ui .is-pop-overlay.pop3 {
  z-index: 10007 !important;
}
#_cbhtml .is-pop-overlay.pop4,
.is-ui .is-pop-overlay.pop4 {
  z-index: 10008 !important;
}
#_cbhtml .is-pop.pop1,
.is-ui .is-pop.pop1 {
  z-index: 10006 !important;
}
#_cbhtml .is-pop.pop2,
.is-ui .is-pop.pop2 {
  z-index: 10007 !important;
}
#_cbhtml .is-pop.pop2,
.is-ui .is-pop.pop2 {
  z-index: 10008 !important;
}
#_cbhtml .is-pop.pop2,
.is-ui .is-pop.pop2 {
  z-index: 10009 !important;
}
#_cbhtml .pickgradientcolor,
.is-ui .pickgradientcolor {
  width: 380px;
  flex-direction: column;
  padding: 10px 10px 11px;
  box-sizing: border-box;
}
#_cbhtml .pickgradientcolor .div-presets,
.is-ui .pickgradientcolor .div-presets {
  display: flex;
  flex-flow: wrap;
  gap: 10px;
}
#_cbhtml .pickgradientcolor .div-presets .btn-graditem,
.is-ui .pickgradientcolor .div-presets .btn-graditem {
  width: 82px;
  height: 82px;
  border-radius: 5px;
}
#_cbhtml .pickgradientcolor .label-saved,
.is-ui .pickgradientcolor .label-saved {
  margin-top: 20px;
}
#_cbhtml .pickgradientcolor .label-gradient-colors,
.is-ui .pickgradientcolor .label-gradient-colors {
  margin-top: 20px;
  margin-bottom: 10px;
}
#_cbhtml .pickgradientcolor .div-saved,
.is-ui .pickgradientcolor .div-saved {
  display: flex;
  flex-flow: wrap;
  gap: 10px;
  margin-top: 20px;
}
#_cbhtml .pickgradientcolor .div-saved div,
.is-ui .pickgradientcolor .div-saved div {
  width: 82px;
  height: 82px;
  position: relative;
}
#_cbhtml .pickgradientcolor .div-saved div .btn-graditem,
.is-ui .pickgradientcolor .div-saved div .btn-graditem {
  width: 82px;
  height: 82px;
  border-radius: 5px;
}
#_cbhtml .pickgradientcolor .div-saved div .btn-remove,
.is-ui .pickgradientcolor .div-saved div .btn-remove {
  width: 19px;
  height: 19px;
  position: absolute;
  top: -8px;
  right: -8px;
  background: #fff;
  border-radius: 50%;
  border: rgba(0, 0, 0, 0.6705882353) 1px solid;
}
#_cbhtml .pickgradientcolor .div-saved div .btn-remove svg,
.is-ui .pickgradientcolor .div-saved div .btn-remove svg {
  color: #000;
}
#_cbhtml .pickgradientcolor .div-sort,
.is-ui .pickgradientcolor .div-sort {
  display: flex;
  gap: 28px;
  flex-flow: wrap;
  margin-top: 20px;
}
#_cbhtml .pickgradientcolor .div-sort div,
.is-ui .pickgradientcolor .div-sort div {
  width: 44px;
  height: 44px;
  position: relative;
}
#_cbhtml .pickgradientcolor .div-sort div .btn-colorstop,
.is-ui .pickgradientcolor .div-sort div .btn-colorstop {
  width: 44px;
  height: 44px;
  border-radius: 4px;
  border: 1px solid rgba(197, 197, 197, 0.27);
}
#_cbhtml .pickgradientcolor .div-sort div .btn-remove,
.is-ui .pickgradientcolor .div-sort div .btn-remove {
  width: 19px;
  height: 19px;
  position: absolute;
  top: -8px;
  right: -8px;
  background: #fff;
  border-radius: 50%;
  border: rgba(0, 0, 0, 0.6705882353) 1px solid;
}
#_cbhtml .pickgradientcolor .div-sort div .btn-remove svg,
.is-ui .pickgradientcolor .div-sort div .btn-remove svg {
  color: #000;
}
#_cbhtml .pickgradientcolor .div-add,
.is-ui .pickgradientcolor .div-add {
  width: 100%;
  margin-top: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
#_cbhtml .pickgradientcolor .div-add .btn-clear,
.is-ui .pickgradientcolor .div-add .btn-clear {
  width: 44px;
  height: 44px;
  border-radius: 4px;
}
#_cbhtml .pickgradientcolor .div-add .btn-clear svg,
.is-ui .pickgradientcolor .div-add .btn-clear svg {
  width: 17px;
  height: 17px;
}
#_cbhtml .pickgradientcolor .div-add .btn-addstop,
.is-ui .pickgradientcolor .div-add .btn-addstop {
  width: 44px;
  height: 44px;
  border-radius: 4px;
}
#_cbhtml .pickgradientcolor .div-add .btn-addstop svg,
.is-ui .pickgradientcolor .div-add .btn-addstop svg {
  width: 17px;
  height: 17px;
}
#_cbhtml .pickgradientcolor .div-save,
.is-ui .pickgradientcolor .div-save {
  width: 100%;
  margin-top: 20px;
}
#_cbhtml .pickgradientcolor .div-save .btn-save,
.is-ui .pickgradientcolor .div-save .btn-save {
  width: 100%;
  height: 44px;
  border-radius: 4px;
}
#_cbhtml .roundslider-container,
.is-ui .roundslider-container {
  width: 60px;
  height: 60px;
  position: relative;
  flex: none;
}
#_cbhtml .roundslider-container .roundslider,
.is-ui .roundslider-container .roundslider {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  position: absolute;
  background-color: #f0f0f0;
  display: flex;
  justify-content: center;
  align-items: center;
}
#_cbhtml .roundslider-container .knob,
.is-ui .roundslider-container .knob {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: #007bff;
  position: absolute;
  cursor: pointer;
}
#_cbhtml .visually-hidden,
.is-ui .visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  clip-path: inset(50%);
  border: 0;
  padding: 0;
  white-space: nowrap;
  /* added for ensuring the text doesn't wrap */
}
#_cbhtml .gradient-slider-container,
.is-ui .gradient-slider-container {
  position: relative;
  width: 100%;
  max-width: 600px;
  height: 15px;
  margin: 20px 0 15px -2px;
  width: calc(100% - 28px);
}
#_cbhtml .gradient-slider-container .gradient-slider-container-shadow,
.is-ui .gradient-slider-container .gradient-slider-container-shadow {
  position: absolute;
  background-color: #ddd;
  width: 100%;
  height: 100%;
  left: 12px;
  border-radius: 4px;
}
#_cbhtml .gradient-slider-container .slider-point,
.is-ui .gradient-slider-container .slider-point {
  position: absolute;
  top: -8px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  cursor: pointer;
  user-select: none;
  border: #ffffff 3px solid;
}
#_cbhtml .color-swatch,
.is-ui .color-swatch {
  width: 100%;
  height: 315px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
}
#_cbhtml .color-swatch > *,
.is-ui .color-swatch > * {
  display: flex;
  height: 100%;
}
#_cbhtml .color-swatch > * > *,
.is-ui .color-swatch > * > * {
  width: 100%;
  height: 100%;
  border: transparent 1px solid;
  cursor: pointer;
  transition: all ease 0.3s;
}
#_cbhtml .color-swatch [data-color],
.is-ui .color-swatch [data-color] {
  width: 35px;
  height: 31.5px;
  margin: 0;
  transition: none;
  outline: none;
  border-radius: 0px;
}
#_cbhtml .color-swatch [data-color]:focus,
.is-ui .color-swatch [data-color]:focus {
  border: rgba(255, 255, 255, 0.7) 2px solid;
}
#_cbhtml .color-gradient,
.is-ui .color-gradient {
  width: 100%;
  height: 157px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
}
#_cbhtml .color-gradient > *,
.is-ui .color-gradient > * {
  display: flex;
  height: 100%;
}
#_cbhtml .color-gradient > * > *,
.is-ui .color-gradient > * > * {
  width: 100%;
  height: 100%;
  border: transparent 1px solid;
  cursor: pointer;
  transition: all ease 0.3s;
}
#_cbhtml .color-gradient [data-color],
.is-ui .color-gradient [data-color] {
  width: 35px;
  height: 31.5px;
  margin: 0;
  transition: none;
  outline: none;
}
#_cbhtml .color-gradient [data-color]:focus,
.is-ui .color-gradient [data-color]:focus {
  border: rgba(255, 255, 255, 0.7) 2px solid;
}
#_cbhtml .pickcolor button,
.is-ui .pickcolor button {
  float: left;
  width: 45px;
  height: 45px;
  cursor: pointer;
  border-radius: 0px;
}
#_cbhtml .pickcolor button:focus,
.is-ui .pickcolor button:focus {
  outline-offset: -2px;
}
#_cbhtml .pickcolor input[type=text]:focus,
.is-ui .pickcolor input[type=text]:focus {
  outline: #3e93f7 2px solid;
  outline-offset: -2px;
}
#_cbhtml .pickcolor .color-default button,
.is-ui .pickcolor .color-default button {
  width: 35px;
  height: 35px;
  border: transparent 1px solid;
  margin: 0px;
  transition: none;
  outline: none;
}
#_cbhtml .pickcolor .color-default button:focus,
.is-ui .pickcolor .color-default button:focus {
  border: rgba(255, 255, 255, 0.7) 2px solid;
}
#_cbhtml .pickcolor button.clear,
.is-ui .pickcolor button.clear {
  font-size: 10px;
}
#_cbhtml .is-color-preview,
.is-ui .is-color-preview {
  border: rgba(0, 0, 0, 0.06) 1px solid;
}
#_cbhtml .is-locked-indicator,
.is-ui .is-locked-indicator {
  display: none;
  width: 28px;
  height: 28px;
  position: absolute;
  align-items: center;
  justify-content: center;
  background: rgba(243, 243, 243, 0.9);
  border-radius: 500px;
  pointer-events: none;
}

.is-tool {
  position: absolute;
  top: 0;
  left: 0;
  display: none;
  z-index: 10001;
  background: rgba(243, 243, 243, 0.9);
  box-sizing: border-box;
  padding: 0;
  outline: none;
  border-radius: 3px;
  overflow: hidden;
}
.is-tool:hover {
  z-index: 10003;
}
.is-tool.active {
  display: flex;
}
.is-tool button {
  width: 100%;
  height: 25px;
  background-color: transparent;
  border: none;
  cursor: pointer;
  user-select: none;
  -moz-user-select: none;
  -khtml-user-select: none;
  -webkit-user-select: none;
  -o-user-select: none;
  color: #000;
  display: inline-block;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  font-family: sans-serif;
  letter-spacing: 1px;
  font-size: 12px;
  font-weight: 300;
  text-transform: uppercase;
  text-align: center;
  line-height: unset;
  position: relative;
  border-radius: 1px;
  transition: all ease 0.3s;
}
.is-tool button:focus {
  outline: none !important;
  outline-offset: 0;
}
.is-tool button svg {
  fill: #000;
}

#_cbhtml[gray] .is-tool {
  background: rgba(243, 243, 243, 0.9);
  box-shadow: none;
}
#_cbhtml[gray] .is-tool.is-column-tool {
  background: rgba(243, 243, 243, 0.9);
  flex-direction: row;
  margin-top: -2px;
}
#_cbhtml[gray] .is-tool.is-column-tool button {
  width: 27px;
  height: 27px;
}
#_cbhtml[gray] .is-tool.is-column-tool .cell-add {
  background: transparent;
}
#_cbhtml[gray] .is-tool.is-column-tool .cell-more {
  background: transparent;
}
#_cbhtml[gray] .is-tool.is-column-tool .cell-remove {
  background: transparent;
}
#_cbhtml[gray] .is-tool.is-column-tool svg {
  width: 18px;
  height: 18px;
  fill: #000;
}
#_cbhtml[gray] .is-tool.is-column-tool .cell-more svg {
  width: 12px;
  height: 12px;
}
#_cbhtml .is-tool.is-column-tool {
  flex-direction: row;
  margin-top: 0px;
}
#_cbhtml .is-tool.is-column-tool button {
  width: 25px;
  height: 25px;
  display: flex;
  justify-content: center;
  align-items: center;
}
#_cbhtml .is-tool.is-column-tool .cell-add {
  background: #0fcc52;
}
#_cbhtml .is-tool.is-column-tool .cell-more {
  background: rgba(216, 200, 6, 0.9);
}
#_cbhtml .is-tool.is-column-tool .cell-remove {
  background: rgba(255, 85, 4, 0.9);
}
#_cbhtml .is-tool.is-column-tool svg {
  width: 23px;
  height: 23px;
  fill: #fff;
}
#_cbhtml .is-tool.is-column-tool .cell-more svg {
  width: 14px;
  height: 14px;
}
#_cbhtml .is-pop.columnmore {
  flex-direction: column;
  /*
  button.cell-settings {
      width: 100px;
      height: 45px;
      margin-top: 10px;
      flex-direction: initial;
      justify-items: center;
      align-items: center;
      margin-bottom: 0px;
      margin-left: 7px;
      svg {
          width: 14px; 
          height: 14px;
      }
      span {
          width: auto;
          height: auto;
          margin-left: 5px;
          margin-top: 1px;
          line-height: 12px;
      }
  }
  button.cell-locking {
      width: 70px;
      height: 45px;
      margin-top: 10px;
      flex-direction: initial;
      justify-items: center;
      align-items: center;
      margin-bottom: 0px;
      margin-left: 10px;
      svg {
          width: 14px; 
          height: 14px;
          pointer-events: none;
          user-select: none;
      }
      span {
          width: auto;
          height: auto;
          margin-left: 5px;
          margin-top: 1px;
          line-height: 12px;
      }
  }
  button.cell-locking.on {
      background: rgb(0 0 0 / 5%) !important;
  }
  */
}
#_cbhtml .is-pop.columnmore > div {
  max-width: 190px;
  margin: 12px;
}
#_cbhtml .is-pop.columnmore button {
  width: 95px;
  height: 60px;
  text-align: center;
  font-size: 9px;
  font-weight: 400;
  text-transform: uppercase;
  flex-direction: column;
  background-color: transparent;
  box-shadow: none;
  border-radius: 3px;
}
#_cbhtml .is-pop.columnmore button span {
  display: inline-block;
  width: 95px;
  height: 24px;
}
#_cbhtml .is-pop.columnmore button.on {
  background-color: #f1f1f1;
}
#_cbhtml .is-pop.columnmore button:hover {
  background-color: #f1f1f1;
}
#_cbhtml .is-pop.columnmore div.is-separator {
  width: 100%;
  border-top: #f0f0f0 1px solid;
  margin-bottom: 10px;
  margin-top: 10px;
}
#_cbhtml .is-pop.rowmore {
  flex-direction: column;
}
#_cbhtml .is-pop.rowmore > div {
  width: 190px;
  margin: 12px;
}
#_cbhtml .is-pop.rowmore button {
  width: 95px;
  height: 60px;
  text-align: center;
  font-size: 9px;
  font-weight: 400;
  text-transform: uppercase;
  flex-direction: column;
  background-color: transparent;
  box-shadow: none;
  border-radius: 3px;
}
#_cbhtml .is-pop.rowmore button span {
  display: inline-block;
  width: 95px;
  height: 24px;
}
#_cbhtml .is-pop.rowmore button.on {
  background-color: #f1f1f1;
}
#_cbhtml .is-pop.rowmore button:hover {
  background-color: #f1f1f1;
}
#_cbhtml .is-pop.rowmore div.is-separator {
  width: 100%;
  border-top: #f0f0f0 1px solid;
  margin-bottom: 10px;
  margin-top: 10px;
}
#_cbhtml .is-pop.elmmore {
  flex-direction: column;
}
#_cbhtml .is-pop.elmmore > div {
  width: 190px;
  margin: 12px;
}
#_cbhtml .is-pop.elmmore button {
  width: 95px;
  height: 60px;
  text-align: center;
  font-size: 9px;
  font-weight: 400;
  text-transform: uppercase;
  flex-direction: column;
  background-color: transparent;
  box-shadow: none;
  border-radius: 3px;
}
#_cbhtml .is-pop.elmmore button span {
  display: inline-block;
  width: 95px;
  height: 24px;
}
#_cbhtml .is-pop.elmmore button.on {
  background-color: #f1f1f1;
}
#_cbhtml .is-pop.elmmore button:hover {
  background-color: #f1f1f1;
}
#_cbhtml .is-pop.quickadd {
  width: 430px;
  box-sizing: border-box;
  transition: none;
  flex-direction: row;
  flex-flow: wrap;
  justify-content: center;
  align-items: center;
}
#_cbhtml .is-pop.quickadd button {
  float: left;
  width: 100px;
  height: 60px;
  font-size: 9px;
  font-weight: 400;
  text-transform: uppercase;
  flex-direction: column;
  background-color: transparent;
  box-shadow: none;
  border-radius: 3px;
}
#_cbhtml .is-pop.quickadd button.on {
  background-color: #f1f1f1;
}
#_cbhtml .is-pop.quickadd button:hover {
  background-color: #f1f1f1;
}
#_cbhtml .is-pop.quickadd .pop-separator {
  width: 100%;
  margin-top: 10px;
  margin-bottom: 10px;
  border-top: #f0f0f0 1px solid;
}
#_cbhtml .is-pop.quickadd button.add-more {
  width: 100%;
  height: 50px;
  flex-direction: initial;
  padding: 5px;
}
#_cbhtml .is-tool.is-element-tool {
  border-radius: 3px;
  overflow: hidden;
  margin-top: 10px;
  margin-left: 0px;
  border-radius: 100px;
  box-shadow: -1px 5px 8px 0px rgba(0, 0, 0, 0.08);
  background-color: rgba(255, 255, 255, 0.9) !important;
}
#_cbhtml .is-tool.is-element-tool button {
  width: 32px;
  height: 31px;
  background: transparent;
  color: #111;
}
#_cbhtml .is-tool.is-element-tool svg {
  width: 14px;
  height: 14px;
  fill: #111 !important;
  color: #111 !important;
}
#_cbhtml .is-tool.is-element-tool .elm-more svg {
  width: 11px;
  height: 11px;
}

.row-outline .is-row-tool,
.row-active .is-row-tool {
  display: flex;
}

.row-active .is-col-tool {
  display: flex;
  width: auto;
  padding: 0 !important;
}

.is-builder[rowoutline] .row-active .is-col-tool {
  display: none;
}

.is-builder[hidecolumntool] .row-active .is-col-tool {
  display: none;
}

.is-tool.is-row-tool {
  flex-direction: column;
  width: auto;
  left: auto;
  right: -40px;
  box-shadow: none;
  border-radius: 2px;
  overflow: hidden;
}
.is-tool.is-row-tool button {
  width: 28px;
  height: 28px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.is-tool.is-row-tool svg {
  fill: #fff !important;
  color: #fff !important;
}
.is-tool.is-row-tool .row-handle {
  display: flex;
  height: 25px;
  justify-content: center;
  align-items: center;
  background: #169af7;
  margin: 0;
  padding: 0;
  border: none;
  border-radius: 0;
}
.is-tool.is-row-tool .row-handle svg {
  width: 13px;
  height: 13px;
}
.is-tool.is-row-tool .row-grideditor {
  background: rgba(216, 200, 6, 0.9);
}
.is-tool.is-row-tool .row-grideditor svg {
  width: 14px;
  height: 14px;
  margin-top: -1px;
}
.is-tool.is-row-tool .row-more {
  background: rgba(216, 200, 6, 0.9);
}
.is-tool.is-row-tool .row-more svg {
  width: 14px;
  height: 14px;
}
.is-tool.is-row-tool .row-remove {
  background: rgba(255, 85, 4, 0.9);
}
.is-tool.is-row-tool .row-remove svg {
  width: 23px;
  height: 23px;
}
.is-tool.is-col-tool {
  flex-direction: row;
  margin-top: 0px;
  margin-left: -1px;
  box-shadow: none;
  border-radius: 2px;
  overflow: hidden;
  top: -3px !important;
}
.is-tool.is-col-tool button {
  width: 28px;
  height: 28px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.is-tool.is-col-tool .cell-add {
  background: #0fcc52;
}
.is-tool.is-col-tool .cell-more {
  background: rgba(216, 200, 6, 0.9);
}
.is-tool.is-col-tool .cell-remove {
  background: rgba(255, 85, 4, 0.9);
}
.is-tool.is-col-tool svg {
  fill: #fff !important;
  /* $ui-columntool-colored-button-svg-fill */
  color: #fff !important;
}
.is-tool.is-col-tool .cell-more svg {
  width: 14px;
  height: 14px;
}
.is-tool.is-canvas-tool {
  display: flex;
  flex-direction: column;
  width: auto;
  left: auto;
  right: -40px;
  box-shadow: none;
  border-radius: 2px;
  overflow: hidden;
}
.is-tool.is-canvas-tool button {
  width: 30px;
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.is-tool.is-canvas-tool button.box-up svg, .is-tool.is-canvas-tool button.box-down svg {
  width: 12px;
  height: 12px;
}
.is-tool.is-canvas-tool button.box-settings svg {
  width: 12px;
  height: 12px;
}
.is-tool.is-canvas-tool button.box-remove svg {
  width: 14px;
  height: 14px;
}
.is-tool.is-canvasadd-tool {
  display: flex;
  flex-direction: column;
  width: auto;
  top: auto;
  bottom: -37px;
  left: calc(50% - 15px);
  box-shadow: none;
  border-radius: 2px;
  overflow: hidden;
}
.is-tool.is-canvasadd-tool button {
  width: 30px;
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.is-tool.is-canvasadd-tool button.box-add svg {
  width: 14px;
  height: 14px;
}

.page-web .is-tool.is-canvas-tool {
  top: 10vh;
  right: 5px;
}
.page-web .is-tool.is-canvasadd-tool {
  bottom: 5px;
}

.page-web-container .is-tool.is-canvasadd-tool {
  bottom: 5px;
}

.is-page:not(.page-web):not(.page-web-container) .is-canvasadd-tool {
  background-color: #ffffffa3;
  box-shadow: 2px 3px 9px #62626212;
}

.is-page:not(.page-web) .is-canvas-tool {
  background-color: #ffffffa3;
  box-shadow: 2px 3px 9px #62626212;
}

.is-builder[gray] .is-tool.is-row-tool {
  background: rgba(243, 243, 243, 0.9);
}
.is-builder[gray] .is-tool.is-row-tool button {
  width: 27px;
  height: 27px;
}
.is-builder[gray] .is-tool.is-row-tool svg {
  fill: #000;
}
.is-builder[gray] .is-tool.is-row-tool .row-handle {
  display: flex;
  height: 27px;
  justify-content: center;
  align-items: center;
  background: transparent;
  margin: 0;
  padding: 0;
  border: none;
  border-radius: 0;
}
.is-builder[gray] .is-tool.is-row-tool .row-handle svg {
  width: 11px;
  height: 11px;
}
.is-builder[gray] .is-tool.is-row-tool .row-grideditor {
  background: transparent;
}
.is-builder[gray] .is-tool.is-row-tool .row-grideditor svg {
  width: 13px;
  height: 13px;
}
.is-builder[gray] .is-tool.is-row-tool .row-more {
  background: transparent;
}
.is-builder[gray] .is-tool.is-row-tool .row-more svg {
  width: 12px;
  height: 12px;
}
.is-builder[gray] .is-tool.is-row-tool .row-remove {
  background: transparent;
}
.is-builder[gray] .is-tool.is-row-tool .row-remove svg {
  width: 19px;
  height: 19px;
}
.is-builder[gray] .is-tool.is-col-tool {
  background: rgba(243, 243, 243, 0.9);
  flex-direction: row;
  top: -2px;
  margin-top: 0px;
  margin-left: -1px;
}
.is-builder[gray] .is-tool.is-col-tool button {
  width: 27px;
  height: 27px;
}
.is-builder[gray] .is-tool.is-col-tool .cell-add {
  background: transparent;
}
.is-builder[gray] .is-tool.is-col-tool .cell-more {
  background: transparent;
}
.is-builder[gray] .is-tool.is-col-tool .cell-remove {
  background: transparent;
}
.is-builder[gray] .is-tool.is-col-tool svg {
  width: 18px;
  height: 18px;
  fill: #000 !important;
  /* $ui-columntool-grayed-button-svg-fill */
}
.is-builder[gray] .is-tool.is-col-tool .cell-more svg {
  width: 12px;
  height: 12px;
}

.is-rowadd-tool {
  display: none;
  position: absolute;
  bottom: -1px;
  left: 0px;
  width: 100%;
  height: 1px;
  border: none;
  z-index: 1;
  background: transparent;
  transition: none;
}
.is-rowadd-tool button {
  position: absolute;
  top: -9px;
  left: calc(50% - 10px);
  border: 1px solid #ff8e3e;
  border-radius: 500px;
  height: auto;
}

.row-outline .is-rowadd-tool,
.row-active .is-rowadd-tool {
  display: block;
}

.is-builder[gray] .is-rowadd-tool {
  border-bottom: 1px solid rgba(222, 222, 222, 0.32);
}
.is-builder[gray] .is-rowadd-tool button {
  border: 1px solid rgba(0, 0, 0, 0.13);
}

.row-add-initial {
  width: 100%;
  height: 120px;
  font-family: sans-serif;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 1px;
  justify-content: center;
  align-items: center;
  color: #333;
  border: 1px dashed rgba(169, 169, 169, 0.8);
  background: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: all ease 0.3s;
  outline: none !important;
}
.row-add-initial:hover {
  background: rgba(248, 248, 248, 0.35);
}
.row-add-initial:focus {
  outline: none;
}
.row-add-initial span {
  text-transform: none;
  display: block;
  margin-top: 5px;
  font-size: 13px;
  opacity: 0.5;
  color: #333;
}

.is-builder .row-active.row-add-initial {
  outline: none;
}

.is-tool.is-row-tool .row-grideditor {
  display: none;
}

.is-builder {
  /*
  &[grideditor] {
      & > div > div.cell-active {
          outline: $ui-grideditor-cell-outline-active;
      } 
      .row-active {
          outline: $ui-grideditor-row-outline-active;
          z-index: 1;
      } 
      .row-active.row-outline {
          outline: $ui-grideditor-rowoutline-row-outline-active;
      } 
  }
  */
}
.is-builder[minimal] .is-tool.is-row-tool .row-grideditor {
  display: block;
}
.is-builder[minimal] .is-tool.is-row-tool .row-more {
  display: none;
}
.is-builder[clean] .is-tool.is-row-tool {
  background: rgba(243, 243, 243, 0.9);
  outline: none;
}
.is-builder[clean] .is-tool.is-row-tool .row-grideditor {
  display: block;
  background: transparent;
}
.is-builder[clean] .is-tool.is-row-tool .row-grideditor svg {
  fill: #000;
  width: 13px;
  height: 13px;
  margin-left: -1px;
}
.is-builder[clean] .is-tool.is-row-tool .row-more {
  display: none;
}
.is-builder[clean] .is-tool.is-row-tool .row-handle {
  display: none;
}
.is-builder[clean] .is-tool.is-row-tool .row-remove {
  display: none;
}
.is-builder[clean] .row-outline {
  outline: none;
}
.is-builder[clean] .cell-active {
  outline: none;
}
.is-builder[clean] .row-active:not([data-protected]) {
  outline: 1px solid rgba(132, 132, 132, 0.2);
}
.is-builder[leftrowtool] .is-tool.is-row-tool {
  right: auto;
  left: -40px;
}
.is-builder[rowoutline] .row-outline {
  outline: none;
}
.is-builder[rowoutline] > div > div.cell-active {
  outline: none;
}
.is-builder[rowoutline] .row-active:not([data-protected]) {
  outline: 1px solid #00da89;
}
.is-builder[grayoutline] .row-outline {
  outline: none;
}
.is-builder[grayoutline] .cell-active:not([data-protected]) {
  outline: 1px solid rgba(132, 132, 132, 0.1);
}
.is-builder[grayoutline] .row-active:not([data-protected]) {
  outline: 1px solid rgba(132, 132, 132, 0.2);
}
.is-builder[rowoutline][grayoutline] .row-outline {
  outline: none;
}
.is-builder[rowoutline][grayoutline] .cell-active {
  outline: none;
}
.is-builder[rowoutline][grayoutline] .row-active:not([data-protected]) {
  outline: 1px solid rgba(132, 132, 132, 0.2);
}
.is-builder[hideoutline] .row-outline {
  outline: none !important;
}
.is-builder[hideoutline] .cell-active {
  outline: none !important;
}
.is-builder[hideoutline] .row-active {
  outline: none !important;
}
.is-builder[hideoutline] .row-active.row-outline {
  outline: none !important;
}
.is-builder[hidesnippetaddtool] .row-outline .is-rowadd-tool,
.is-builder[hidesnippetaddtool] .row-active .is-rowadd-tool {
  display: none;
}
.is-builder[hideelementhighlight] .cell-active .elm-active {
  background-color: transparent;
}

#_cbhtml[minimal] .is-tool.is-column-tool .cell-more {
  display: none;
}
#_cbhtml[clean] .is-tool.is-column-tool {
  display: none;
}
#_cbhtml[hideelementtool] .is-tool.is-element-tool {
  display: none !important;
}
#_cbhtml .is-element-tool .elm-settings {
  display: none;
}
#_cbhtml[emailmode] .is-element-tool .elm-add,
#_cbhtml[emailmode] .is-element-tool .elm-more,
#_cbhtml[emailmode] .is-element-tool .elm-remove {
  display: none !important;
}
#_cbhtml[emailmode] .is-element-tool .elm-settings {
  display: block;
}

.is-tooltip {
  position: absolute;
  display: none;
  line-height: 1;
  padding: 5px 8px;
  font-size: 11px;
  background: #333;
  border-radius: 0px;
  color: #fefefe;
  z-index: 100005;
  -webkit-font-smoothing: auto;
}

/*!
 * Cropper.js v1.5.12
 * https://fengyuanchen.github.io/cropperjs
 *
 * Copyright 2015-present Chen Fengyuan
 * Released under the MIT license
 *
 * Date: 2021-06-12T08:00:11.623Z
 */
.cropper-container {
  direction: ltr;
  font-size: 0;
  line-height: 0;
  position: relative;
  -ms-touch-action: none;
  touch-action: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.cropper-container img {
  image-orientation: 0deg;
  display: block;
  height: 100%;
  max-height: none !important;
  max-width: none !important;
  min-height: 0 !important;
  min-width: 0 !important;
  width: 100%;
}

.cropper-canvas, .cropper-crop-box, .cropper-drag-box, .cropper-modal, .cropper-wrap-box {
  bottom: 0;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
}

.cropper-canvas, .cropper-wrap-box {
  overflow: hidden;
}

.cropper-drag-box {
  background-color: #fff;
  opacity: 0;
}

.cropper-modal {
  background-color: #000;
  opacity: 0.5;
}

.cropper-view-box {
  display: block;
  height: 100%;
  outline: 1px solid #39f;
  outline-color: rgba(51, 153, 255, 0.75);
  overflow: hidden;
  width: 100%;
}

.cropper-dashed {
  border: 0 dashed #eee;
  display: block;
  opacity: 0.5;
  position: absolute;
}

.cropper-dashed.dashed-h {
  border-bottom-width: 1px;
  border-top-width: 1px;
  height: 33.33333%;
  left: 0;
  top: 33.33333%;
  width: 100%;
}

.cropper-dashed.dashed-v {
  border-left-width: 1px;
  border-right-width: 1px;
  height: 100%;
  left: 33.33333%;
  top: 0;
  width: 33.33333%;
}

.cropper-center {
  display: block;
  height: 0;
  left: 50%;
  opacity: 0.75;
  position: absolute;
  top: 50%;
  width: 0;
}

.cropper-center:after, .cropper-center:before {
  background-color: #eee;
  content: " ";
  display: block;
  position: absolute;
}

.cropper-center:before {
  height: 1px;
  left: -3px;
  top: 0;
  width: 7px;
}

.cropper-center:after {
  height: 7px;
  left: 0;
  top: -3px;
  width: 1px;
}

.cropper-face, .cropper-line, .cropper-point {
  display: block;
  height: 100%;
  opacity: 0.1;
  position: absolute;
  width: 100%;
}

.cropper-face {
  background-color: #fff;
  left: 0;
  top: 0;
}

.cropper-line {
  background-color: #39f;
}

.cropper-line.line-e {
  cursor: ew-resize;
  right: -3px;
  top: 0;
  width: 5px;
}

.cropper-line.line-n {
  cursor: ns-resize;
  height: 5px;
  left: 0;
  top: -3px;
}

.cropper-line.line-w {
  cursor: ew-resize;
  left: -3px;
  top: 0;
  width: 5px;
}

.cropper-line.line-s {
  bottom: -3px;
  cursor: ns-resize;
  height: 5px;
  left: 0;
}

.cropper-point {
  background-color: #39f;
  height: 5px;
  opacity: 0.75;
  width: 5px;
}

.cropper-point.point-e {
  cursor: ew-resize;
  margin-top: -3px;
  right: -3px;
  top: 50%;
}

.cropper-point.point-n {
  cursor: ns-resize;
  left: 50%;
  margin-left: -3px;
  top: -3px;
}

.cropper-point.point-w {
  cursor: ew-resize;
  left: -3px;
  margin-top: -3px;
  top: 50%;
}

.cropper-point.point-s {
  bottom: -3px;
  cursor: s-resize;
  left: 50%;
  margin-left: -3px;
}

.cropper-point.point-ne {
  cursor: nesw-resize;
  right: -3px;
  top: -3px;
}

.cropper-point.point-nw {
  cursor: nwse-resize;
  left: -3px;
  top: -3px;
}

.cropper-point.point-sw {
  bottom: -3px;
  cursor: nesw-resize;
  left: -3px;
}

.cropper-point.point-se {
  bottom: -3px;
  cursor: nwse-resize;
  height: 20px;
  opacity: 1;
  right: -3px;
  width: 20px;
}

@media (min-width: 768px) {
  .cropper-point.point-se {
    height: 15px;
    width: 15px;
  }
}
@media (min-width: 992px) {
  .cropper-point.point-se {
    height: 10px;
    width: 10px;
  }
}
@media (min-width: 1200px) {
  .cropper-point.point-se {
    height: 5px;
    opacity: 0.75;
    width: 5px;
  }
}
.cropper-point.point-se:before {
  background-color: #39f;
  bottom: -50%;
  content: " ";
  display: block;
  height: 200%;
  opacity: 0;
  position: absolute;
  right: -50%;
  width: 200%;
}

.cropper-invisible {
  opacity: 0;
}

.cropper-bg {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQAQMAAAAlPW0iAAAAA3NCSVQICAjb4U/gAAAABlBMVEXMzMz////TjRV2AAAACXBIWXMAAArrAAAK6wGCiw1aAAAAHHRFWHRTb2Z0d2FyZQBBZG9iZSBGaXJld29ya3MgQ1M26LyyjAAAABFJREFUCJlj+M/AgBVhF/0PAH6/D/HkDxOGAAAAAElFTkSuQmCC");
}

.cropper-hide {
  display: block;
  height: 0;
  position: absolute;
  width: 0;
}

.cropper-hidden {
  display: none !important;
}

.cropper-move {
  cursor: move;
}

.cropper-crop {
  cursor: crosshair;
}

.cropper-disabled .cropper-drag-box, .cropper-disabled .cropper-face, .cropper-disabled .cropper-line, .cropper-disabled .cropper-point {
  cursor: not-allowed;
}

.is-ui .is-lightbox {
  display: none;
  z-index: 100000;
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  box-sizing: border-box;
  background-color: black;
  opacity: 0;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transform: scale(0.7) translateZ(150px);
  transition: all 450ms ease-in-out;
  outline: none;
}
.is-ui .is-lightbox.light {
  background-color: rgba(255, 255, 255, 0.97);
}
.is-ui .is-lightbox > div {
  width: 100%;
  height: 100%;
}
.is-ui .is-lightbox > div.lightbox-content {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 70px;
  box-sizing: border-box;
}
.is-ui .is-lightbox > div.lightbox-content > img {
  cursor: default;
  opacity: unset !important;
}
.is-ui .is-lightbox.active {
  opacity: 1;
  transform: scale(1) translateZ(150px);
}
.is-ui .is-lightbox iframe {
  opacity: 0;
  filter: blur(30px);
  transition: all 600ms ease-in-out;
}
.is-ui .is-lightbox.active iframe {
  filter: blur(0);
  opacity: 1;
}
.is-ui .is-lightbox video {
  outline: none;
  width: 100%;
  height: 100%;
}
.is-ui .is-lightbox img {
  max-width: 100%;
  max-height: 100%;
}
.is-ui .is-lightbox .cmd-lightbox-close {
  position: absolute !important;
  top: 3px !important;
  right: 3px !important;
  width: 60px !important;
  height: 60px !important;
  color: #fff !important;
  background: none !important;
  box-shadow: none !important;
  border: none !important;
  cursor: pointer;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}
.is-ui .is-lightbox .cmd-lightbox-close svg {
  width: 35px;
  height: 35px;
  fill: #000 !important;
}
.is-ui .is-lightbox.light .cmd-lightbox-close {
  color: #000 !important;
}
.is-ui .is-lightbox .cmd-lightbox-prev,
.is-ui .is-lightbox .cmd-lightbox-next {
  flex: none;
  position: absolute !important;
  width: 80px !important;
  height: 80px !important;
  margin-top: -40px;
  color: #fff !important;
  background: none !important;
  box-shadow: none !important;
  border: none !important;
  cursor: pointer;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}
.is-ui .is-lightbox .cmd-lightbox-prev svg,
.is-ui .is-lightbox .cmd-lightbox-next svg {
  width: 55px;
  height: 55px;
  fill: #000 !important;
}
.is-ui .is-lightbox.light .cmd-lightbox-prev, .is-ui .is-lightbox.light .cmd-lightbox-next {
  color: #000 !important;
}

.block-click[data-noedit] {
  cursor: pointer;
}
.block-click[data-noedit] > * {
  pointer-events: none;
  user-select: none;
}

.block-click[contenteditable=true] {
  cursor: unset;
}
.block-click[contenteditable=true] > * {
  pointer-events: unset;
  user-select: unset;
}

/* Prevent css framework overide (Materialize) */
.is-ui [type=checkbox]:not(:checked), .is-ui [type=checkbox]:checked {
  position: unset !important;
}

.is-ui input[type=range] {
  border: none;
}

.is-ui form {
  background: unset;
  margin: unset;
  padding: unset;
  border: unset;
}

.is-ui svg {
  max-width: unset;
}

/* Prevent css framework overide (Tailwind) */
.is-ui svg {
  display: initial !important;
}

.is-section-tool svg,
.is-box-tool svg,
.is-row-tool svg,
.is-column-tool svg,
.is-col-tool svg {
  display: initial !important;
}

/* Make slider block resizable */
.is-builder > div > div[data-module=slider-builder].cell-active {
  z-index: 1 !important;
}

/* Row Lock will hide row tool */
.row-lock .is-row-tool,
.row-lock .is-rowadd-tool {
  display: none !important;
}

/* Adjustment */
@media (max-height: 700px) {
  #_cbhtml .is-modal.buttoneditor {
    height: 500px;
    margin-top: -250px;
  }
}
@media (max-height: 600px) {
  #_cbhtml .is-modal.buttoneditor {
    height: 400px;
    margin-top: -200px;
  }
}
.relative {
  position: relative;
}

.is-row-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  pointer-events: none;
  user-select: none;
}

/*
@media (min-width: 761px) and (max-width: 970px) {
    body.data-editor .sm-hidden {
        opacity: 0.5 !important;
        background-color: rgb(130 130 130 / 14%) !important;
    }
}
@media (min-width: 971px) and (max-width: 1280px) {
    body.data-editor .md-hidden {
        opacity: 0.5 !important;
        background-color: rgb(130 130 130 / 14%) !important;
    }
}
@media (min-width: 1281px) {
    body.data-editor .desktop-hidden {
        opacity: 0.5 !important;
        background-color: rgb(130 130 130 / 14%) !important;
    }
}*/
div[data-html] {
  min-height: 70px;
}

[data-module=slider-builder] *,
[data-module=text-builder] * {
  outline: none !important;
}

/* Make slider block resizable */
.padding-0[data-module=slider-builder],
.padding-0[data-module=text-builder] {
  padding: 0 2px 4px !important;
}

/* IFRAME * New Control Panel */
.is-content-view.desktop {
  width: 1366px;
  height: 853px;
}
.is-content-view.tablet-landscape {
  width: 1080px;
  height: 810px;
}
.is-content-view.tablet {
  width: 768px;
  height: 1024px;
}
.is-content-view.mobile {
  width: 390px;
  height: 844px;
}

@media all and (min-width: 1620px) {
  .is-content-view.desktop {
    width: 1420px;
    height: 887px;
  }

  .is-content-view.tablet-landscape {
    width: 1280px;
    height: 960px;
  }
}
@media all and (min-width: 1720px) {
  .is-content-view.desktop {
    width: 1520px;
    height: 949px;
  }

  .is-content-view.tablet-landscape {
    width: 1280px;
    height: 960px;
  }
}
@media all and (min-width: 1820px) {
  .is-content-view.desktop {
    width: 1620px;
    height: 1012px;
  }

  .is-content-view.tablet-landscape {
    width: 1280px;
    height: 960px;
  }
}
@media all and (max-width: 1520px) {
  .is-content-view {
    transform: scale(0.93);
  }
}
@media all and (max-width: 1450px) {
  .is-content-view {
    transform: scale(0.87);
  }
}
@media all and (max-width: 1375px) {
  .is-content-view {
    transform: scale(0.73);
  }
}
@media all and (max-width: 1175px) {
  .is-content-view {
    transform: scale(0.67);
  }
}
@media all and (max-width: 1090px) {
  .is-content-view {
    transform: scale(0.62);
  }
}
.custom-select {
  --cs-height: 46px;
  --cs-border: 1px solid #ddd;
  --cs-background: #fff;
  --cs-hover-background: #f2f2f2;
  --cs-selected-background: #eee;
  position: relative;
  display: inline-block;
  font-size: 15px;
  height: var(--cs-height);
  width: 100%;
}
.custom-select .select-styled {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 6px 0 14px;
  border: var(--cs-border);
  cursor: pointer;
  position: relative;
  user-select: none;
  box-sizing: border-box;
  height: var(--cs-height);
  overflow: hidden;
  border-radius: 3px;
  box-shadow: -2px 5px 10px -6px rgba(0, 0, 0, 0.08);
  outline: none;
}
.custom-select .select-styled span {
  display: flex;
}
.custom-select .select-styled span img {
  height: 25px;
  margin-top: 3px;
}
.custom-select .select-styled:focus-visible {
  outline: #3e93f7 2px solid;
  outline-offset: -1px;
}
.custom-select .select-styled > span:first-child {
  position: absolute;
  margin-right: 23px;
}
.custom-select .select-styled > span:nth-child(2) {
  position: absolute;
  height: 15px;
  top: calc(50% - 7.5px);
  right: 6px;
  display: flex;
}
.custom-select .select-styled svg {
  width: 15px;
  height: 15px;
}
.custom-select .select-styled[aria-expanded=true] > span:nth-child(2) {
  -webkit-transform: rotate(-180deg);
  transform: rotate(-180deg);
  top: calc(50% - 7.5px);
}
.custom-select input {
  display: none;
  width: 100%;
  height: 40px;
  border: var(--cs-border);
  border-top: none;
  padding: 0 3px 0 8px;
  box-sizing: border-box;
  margin: 0;
  position: absolute;
  top: var(--cs-height);
  left: 0;
  font-size: 15px;
  z-index: 2;
  outline-offset: 0;
  background: var(--cs-background);
}
.custom-select input:focus {
  outline: none;
}
.custom-select .select-options {
  display: none;
  position: absolute;
  top: calc(var(--cs-height) + 1px);
  left: 0;
  border: var(--cs-border);
  box-sizing: border-box;
  border-top: none;
  z-index: 1;
  list-style: none;
  margin: 0;
  padding: 0;
  width: 100%;
  background-color: var(--cs-background);
  z-index: 1;
  max-height: 200px;
  overflow-y: auto;
}
.custom-select.has-search .select-options {
  top: calc(var(--cs-height) + 40px);
}
.custom-select .select-options.active {
  display: block;
}
.custom-select .select-options li {
  padding: 0 8px 0 14px;
  cursor: pointer;
  white-space: nowrap;
  min-height: 36px;
  align-items: center;
}
.custom-select .select-options li img {
  margin: 8px 0;
  height: 25px;
}
.custom-select .select-options li:hover {
  background-color: var(--cs-hover-background);
}
.custom-select .select-options li.selected {
  background-color: var(--cs-selected-background);
}

/* Switch */
.is-ui .switch, .builder-ui .switch {
  position: relative;
  display: inline-block;
  width: 60px;
  height: 34px;
}
.is-ui .switch input, .builder-ui .switch input {
  opacity: 0;
  width: 0;
  height: 0;
}
.is-ui .switch .slider, .builder-ui .switch .slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  -webkit-transition: 0.4s;
  transition: 0.4s;
  border-radius: 34px;
}
.is-ui .switch .slider:before, .builder-ui .switch .slider:before {
  position: absolute;
  content: "";
  height: 26px;
  width: 26px;
  left: 4px;
  bottom: 4px;
  background-color: #fff;
  -webkit-transition: 0.4s;
  transition: 0.4s;
  border-radius: 50%;
}
.is-ui .switch input:checked + .slider, .builder-ui .switch input:checked + .slider {
  background-color: #0e75de;
  border-radius: 34px;
}
.is-ui .switch input:focus + .slider, .builder-ui .switch input:focus + .slider {
  box-shadow: 0 0 1px #0e75de;
}
.is-ui .switch input:checked + .slider:before, .builder-ui .switch input:checked + .slider:before {
  -webkit-transform: translateX(26px);
  -ms-transform: translateX(26px);
  transform: translateX(26px);
}

.dark .is-ui .switch .slider,
.dark .builder-ui .switch .slider {
  background-color: #7c7c7c;
}
.dark .is-ui .switch .slider:before,
.dark .builder-ui .switch .slider:before {
  background-color: rgba(255, 255, 255, 0.82);
}
.dark .is-ui .switch input:checked + .slider,
.dark .builder-ui .switch input:checked + .slider {
  background-color: #0e75de;
}
.dark .is-ui .switch input:focus + .slider,
.dark .builder-ui .switch input:focus + .slider {
  box-shadow: 0 0 1px #0e75de;
}

.dark .div-font-list > button img,
.colored-dark .div-font-list > button img,
.colored .div-font-list > button img {
  mix-blend-mode: screen;
  filter: invert(1);
}

i.bi, i.icon {
  display: inline-flex;
}

/* block tool */
/*
.is-block-tool {
    border-radius: 3px;
    overflow: hidden;
    top: 3px;
    right: 3px;
    left: auto;
    width: 25px;

    button {
        width: 25px; 
        height: 25px;
        background: transparent;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    svg {
        width: 14px;
        height: 14px;
        fill: #000;
    }
} 
.is-block.active:not(.multi):not(.editable) .is-block-tool {
    display: flex;
}
*/
/* Lightbox */
.glightbox-container {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999999 !important;
  overflow: hidden;
  -ms-touch-action: none;
  touch-action: none;
  -webkit-text-size-adjust: 100%;
  -moz-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  text-size-adjust: 100%;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  outline: none;
}

.glightbox-container.inactive {
  display: none;
}

.glightbox-container .gcontainer {
  position: relative;
  width: 100%;
  height: 100%;
  z-index: 9999;
  overflow: hidden;
}

.glightbox-container .gslider {
  -webkit-transition: -webkit-transform 0.4s ease;
  transition: -webkit-transform 0.4s ease;
  transition: transform 0.4s ease;
  transition: transform 0.4s ease, -webkit-transform 0.4s ease;
  height: 100%;
  left: 0;
  top: 0;
  width: 100%;
  position: relative;
  overflow: hidden;
  display: -webkit-box !important;
  display: -ms-flexbox !important;
  display: flex !important;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.glightbox-container .gslide {
  width: 100%;
  position: absolute;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  opacity: 0;
}

.glightbox-container .gslide.current {
  opacity: 1;
  z-index: 99999;
  position: relative;
}

.glightbox-container .gslide.prev {
  opacity: 1;
  z-index: 9999;
}

.glightbox-container .gslide-inner-content {
  width: 100%;
}

.glightbox-container .ginner-container {
  position: relative;
  width: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  max-width: 100%;
  margin: auto;
  height: 100vh;
}

.glightbox-container .ginner-container.gvideo-container {
  width: 100%;
}

.glightbox-container .ginner-container.desc-bottom,
.glightbox-container .ginner-container.desc-top {
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
}

.glightbox-container .ginner-container.desc-left,
.glightbox-container .ginner-container.desc-right {
  max-width: 100% !important;
}

.gslide iframe,
.gslide video {
  outline: none !important;
  border: none;
  min-height: 165px;
  -webkit-overflow-scrolling: touch;
  -ms-touch-action: auto;
  touch-action: auto;
}

.gslide:not(.current) {
  pointer-events: none;
}

.gslide-image {
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.gslide-image img {
  max-height: 100vh;
  display: block;
  padding: 0;
  float: none;
  outline: none;
  border: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  max-width: 100vw;
  width: auto;
  height: auto;
  -o-object-fit: cover;
  object-fit: cover;
  -ms-touch-action: none;
  touch-action: none;
  margin: auto;
  min-width: 200px;
}

.desc-top .gslide-image img,
.desc-bottom .gslide-image img {
  width: auto;
}

.desc-left .gslide-image img,
.desc-right .gslide-image img {
  width: auto;
  max-width: 100%;
}

.gslide-image img.zoomable {
  position: relative;
}

.gslide-image img.dragging {
  cursor: -webkit-grabbing !important;
  cursor: grabbing !important;
  -webkit-transition: none;
  transition: none;
}

.gslide-video {
  position: relative;
  max-width: 100vh;
  width: 100% !important;
}

.gslide-video .plyr__poster-enabled.plyr--loading .plyr__poster {
  display: none;
}

.gslide-video .gvideo-wrapper {
  width: 100%;
  /* max-width: 160vmin; */
  margin: auto;
}

.gslide-video::before {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  background: rgba(255, 0, 0, 0.34);
  display: none;
}

.gslide-video.playing::before {
  display: none;
}

.gslide-video.fullscreen {
  max-width: 100% !important;
  min-width: 100%;
  height: 75vh;
}

.gslide-video.fullscreen video {
  max-width: 100% !important;
  width: 100% !important;
}

.gslide-inline {
  background: #fff;
  text-align: left;
  max-height: calc(100vh - 40px);
  overflow: auto;
  max-width: 100%;
  margin: auto;
}

.gslide-inline .ginlined-content {
  padding: 20px;
  width: 100%;
}

.gslide-inline .dragging {
  cursor: -webkit-grabbing !important;
  cursor: grabbing !important;
  -webkit-transition: none;
  transition: none;
}

.ginlined-content {
  overflow: auto;
  display: block !important;
  opacity: 1;
}

.gslide-external {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  min-width: 100%;
  background: #fff;
  padding: 0;
  overflow: auto;
  max-height: 75vh;
  height: 100%;
}

.gslide-media {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  width: auto;
}

.zoomed .gslide-media {
  -webkit-box-shadow: none !important;
  box-shadow: none !important;
}

.desc-top .gslide-media,
.desc-bottom .gslide-media {
  margin: 0 auto;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
}

.gslide-description {
  position: relative;
  -webkit-box-flex: 1;
  -ms-flex: 1 0 100%;
  flex: 1 0 100%;
}

.gslide-description.description-left,
.gslide-description.description-right {
  max-width: 100%;
}

.gslide-description.description-bottom,
.gslide-description.description-top {
  margin: 0 auto;
  width: 100%;
}

.gslide-description p {
  margin-bottom: 12px;
}

.gslide-description p:last-child {
  margin-bottom: 0;
}

.zoomed .gslide-description {
  display: none;
}

.glightbox-button-hidden {
  display: none;
}

.glightbox-mobile .glightbox-container .gslide-description {
  height: auto !important;
  width: 100%;
  position: absolute;
  bottom: 0;
  padding: 19px 11px;
  max-width: 100vw !important;
  -webkit-box-ordinal-group: 3 !important;
  -ms-flex-order: 2 !important;
  order: 2 !important;
  max-height: 78vh;
  overflow: auto !important;
  background: -webkit-gradient(linear, left top, left bottom, from(rgba(0, 0, 0, 0)), to(rgba(0, 0, 0, 0.75)));
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.75) 100%);
  -webkit-transition: opacity 0.3s linear;
  transition: opacity 0.3s linear;
  padding-bottom: 50px;
}

.glightbox-mobile .glightbox-container .gslide-title {
  color: #fff;
  font-size: 1em;
}

.glightbox-mobile .glightbox-container .gslide-desc {
  color: #a1a1a1;
}

.glightbox-mobile .glightbox-container .gslide-desc a {
  color: #fff;
  font-weight: bold;
}

.glightbox-mobile .glightbox-container .gslide-desc * {
  color: inherit;
}

.glightbox-mobile .glightbox-container .gslide-desc .desc-more {
  color: #fff;
  opacity: 0.4;
}

.gdesc-open .gslide-media {
  -webkit-transition: opacity 0.5s ease;
  transition: opacity 0.5s ease;
  opacity: 0.4;
}

.gdesc-open .gdesc-inner {
  padding-bottom: 30px;
}

.gdesc-closed .gslide-media {
  -webkit-transition: opacity 0.5s ease;
  transition: opacity 0.5s ease;
  opacity: 1;
}

.greset {
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
}

.gabsolute {
  position: absolute;
}

.grelative {
  position: relative;
}

.glightbox-desc {
  display: none !important;
}

.glightbox-open {
  overflow: hidden;
}

.gloader {
  height: 25px;
  width: 25px;
  -webkit-animation: lightboxLoader 0.8s infinite linear;
  animation: lightboxLoader 0.8s infinite linear;
  border: 2px solid #fff;
  border-right-color: transparent;
  border-radius: 50%;
  position: absolute;
  display: block;
  z-index: 9999;
  left: 0;
  right: 0;
  margin: 0 auto;
  top: 47%;
}

.goverlay {
  width: 100%;
  height: calc(100vh + 1px);
  position: fixed;
  top: -1px;
  left: 0;
  background: #000;
  will-change: opacity;
}

.glightbox-mobile .goverlay {
  background: #000;
}

.gprev,
.gnext,
.gclose {
  z-index: 99999;
  cursor: pointer;
  width: 26px;
  height: 44px;
  border: none;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
}

.gprev svg,
.gnext svg,
.gclose svg {
  display: block;
  width: 25px;
  height: auto;
  margin: 0;
  padding: 0;
}

.gprev.disabled,
.gnext.disabled,
.gclose.disabled {
  opacity: 0.1;
}

.gprev .garrow,
.gnext .garrow,
.gclose .garrow {
  stroke: #fff;
}

.gbtn.focused {
  outline: 2px solid #0f3d81;
}

iframe.wait-autoplay {
  opacity: 0;
}

.glightbox-closing .gnext,
.glightbox-closing .gprev,
.glightbox-closing .gclose {
  opacity: 0 !important;
}

/*Skin */
.glightbox-clean .gslide-description {
  background: #fff;
}

.glightbox-clean .gdesc-inner {
  padding: 22px 20px;
}

.glightbox-clean .gslide-title {
  font-size: 1em;
  font-weight: normal;
  font-family: arial;
  color: #000;
  margin-bottom: 19px;
  line-height: 1.4em;
}

.glightbox-clean .gslide-desc {
  font-size: 0.86em;
  margin-bottom: 0;
  font-family: arial;
  line-height: 1.4em;
}

.glightbox-clean .gslide-video {
  background: #000;
}

.glightbox-clean .gprev,
.glightbox-clean .gnext,
.glightbox-clean .gclose {
  background-color: rgba(0, 0, 0, 0.75);
  border-radius: 4px;
}

.glightbox-clean .gprev path,
.glightbox-clean .gnext path,
.glightbox-clean .gclose path {
  fill: #fff;
}

.glightbox-clean .gprev {
  position: absolute;
  top: -100%;
  left: 30px;
  width: 40px;
  height: 50px;
}

.glightbox-clean .gnext {
  position: absolute;
  top: -100%;
  right: 30px;
  width: 40px;
  height: 50px;
}

.glightbox-clean .gclose {
  width: 35px;
  height: 35px;
  top: 15px;
  right: 10px;
  position: absolute;
}

.glightbox-clean .gclose svg {
  width: 18px;
  height: auto;
}

.glightbox-clean .gclose:hover {
  opacity: 1;
}

/*CSS Animations*/
.gfadeIn {
  -webkit-animation: gfadeIn 0.5s ease;
  animation: gfadeIn 0.5s ease;
}

.gfadeOut {
  -webkit-animation: gfadeOut 0.5s ease;
  animation: gfadeOut 0.5s ease;
}

.gslideOutLeft {
  -webkit-animation: gslideOutLeft 0.3s ease;
  animation: gslideOutLeft 0.3s ease;
}

.gslideInLeft {
  -webkit-animation: gslideInLeft 0.3s ease;
  animation: gslideInLeft 0.3s ease;
}

.gslideOutRight {
  -webkit-animation: gslideOutRight 0.3s ease;
  animation: gslideOutRight 0.3s ease;
}

.gslideInRight {
  -webkit-animation: gslideInRight 0.3s ease;
  animation: gslideInRight 0.3s ease;
}

.gzoomIn {
  -webkit-animation: gzoomIn 0.5s ease;
  animation: gzoomIn 0.5s ease;
}

.gzoomOut {
  -webkit-animation: gzoomOut 0.5s ease;
  animation: gzoomOut 0.5s ease;
}

@-webkit-keyframes lightboxLoader {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@keyframes lightboxLoader {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@-webkit-keyframes gfadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@keyframes gfadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@-webkit-keyframes gfadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
@keyframes gfadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
@-webkit-keyframes gslideInLeft {
  from {
    opacity: 0;
    -webkit-transform: translate3d(-60%, 0, 0);
    transform: translate3d(-60%, 0, 0);
  }
  to {
    visibility: visible;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}
@keyframes gslideInLeft {
  from {
    opacity: 0;
    -webkit-transform: translate3d(-60%, 0, 0);
    transform: translate3d(-60%, 0, 0);
  }
  to {
    visibility: visible;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}
@-webkit-keyframes gslideOutLeft {
  from {
    opacity: 1;
    visibility: visible;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  to {
    -webkit-transform: translate3d(-60%, 0, 0);
    transform: translate3d(-60%, 0, 0);
    opacity: 0;
    visibility: hidden;
  }
}
@keyframes gslideOutLeft {
  from {
    opacity: 1;
    visibility: visible;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  to {
    -webkit-transform: translate3d(-60%, 0, 0);
    transform: translate3d(-60%, 0, 0);
    opacity: 0;
    visibility: hidden;
  }
}
@-webkit-keyframes gslideInRight {
  from {
    opacity: 0;
    visibility: visible;
    -webkit-transform: translate3d(60%, 0, 0);
    transform: translate3d(60%, 0, 0);
  }
  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}
@keyframes gslideInRight {
  from {
    opacity: 0;
    visibility: visible;
    -webkit-transform: translate3d(60%, 0, 0);
    transform: translate3d(60%, 0, 0);
  }
  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}
@-webkit-keyframes gslideOutRight {
  from {
    opacity: 1;
    visibility: visible;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  to {
    -webkit-transform: translate3d(60%, 0, 0);
    transform: translate3d(60%, 0, 0);
    opacity: 0;
  }
}
@keyframes gslideOutRight {
  from {
    opacity: 1;
    visibility: visible;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  to {
    -webkit-transform: translate3d(60%, 0, 0);
    transform: translate3d(60%, 0, 0);
    opacity: 0;
  }
}
@-webkit-keyframes gzoomIn {
  from {
    opacity: 0;
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
    transform: scale3d(0.3, 0.3, 0.3);
  }
  to {
    opacity: 1;
  }
}
@keyframes gzoomIn {
  from {
    opacity: 0;
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
    transform: scale3d(0.3, 0.3, 0.3);
  }
  to {
    opacity: 1;
  }
}
@-webkit-keyframes gzoomOut {
  from {
    opacity: 1;
  }
  50% {
    opacity: 0;
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
    transform: scale3d(0.3, 0.3, 0.3);
  }
  to {
    opacity: 0;
  }
}
@keyframes gzoomOut {
  from {
    opacity: 1;
  }
  50% {
    opacity: 0;
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
    transform: scale3d(0.3, 0.3, 0.3);
  }
  to {
    opacity: 0;
  }
}
@media (min-width: 769px) {
  .glightbox-container .ginner-container {
    width: auto;
    height: auto;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
  }

  .glightbox-container .ginner-container.desc-top .gslide-description {
    -webkit-box-ordinal-group: 1;
    -ms-flex-order: 0;
    order: 0;
  }

  .glightbox-container .ginner-container.desc-top .gslide-image,
.glightbox-container .ginner-container.desc-top .gslide-image img {
    -webkit-box-ordinal-group: 2;
    -ms-flex-order: 1;
    order: 1;
  }

  .glightbox-container .ginner-container.desc-left .gslide-description {
    -webkit-box-ordinal-group: 1;
    -ms-flex-order: 0;
    order: 0;
  }

  .glightbox-container .ginner-container.desc-left .gslide-image {
    -webkit-box-ordinal-group: 2;
    -ms-flex-order: 1;
    order: 1;
  }

  .gslide-image img {
    max-height: 97vh;
    max-width: 100%;
  }

  .gslide-image img.zoomable {
    cursor: -webkit-zoom-in;
    cursor: zoom-in;
  }

  .zoomed .gslide-image img.zoomable {
    cursor: -webkit-grab;
    cursor: grab;
  }

  .gslide-inline {
    max-height: 95vh;
  }

  .gslide-external {
    max-height: 100vh;
  }

  .gslide-description.description-left,
.gslide-description.description-right {
    max-width: 275px;
  }

  .glightbox-open {
    height: auto;
  }

  .goverlay {
    background: rgba(0, 0, 0, 0.92);
  }

  .glightbox-clean .gslide-media {
    -webkit-box-shadow: 1px 2px 9px 0px rgba(0, 0, 0, 0.65);
    box-shadow: 1px 2px 9px 0px rgba(0, 0, 0, 0.65);
  }

  .glightbox-clean .description-left .gdesc-inner,
.glightbox-clean .description-right .gdesc-inner {
    position: absolute;
    height: 100%;
    overflow-y: auto;
  }

  .glightbox-clean .gprev,
.glightbox-clean .gnext,
.glightbox-clean .gclose {
    background-color: rgba(0, 0, 0, 0.32);
  }

  .glightbox-clean .gprev:hover,
.glightbox-clean .gnext:hover,
.glightbox-clean .gclose:hover {
    background-color: rgba(0, 0, 0, 0.7);
  }

  .glightbox-clean .gprev {
    top: 45%;
  }

  .glightbox-clean .gnext {
    top: 45%;
  }
}
@media (min-width: 992px) {
  .glightbox-clean .gclose {
    opacity: 0.7;
    right: 20px;
  }
}
@media screen and (max-height: 420px) {
  .goverlay {
    background: #000;
  }
}
/* skin */
/* Light */
.glightbox-light .gslide-description {
  background: #fff;
}

.glightbox-light .gdesc-inner {
  padding: 22px 20px;
}

.glightbox-light .gslide-title {
  font-size: 1em;
  font-weight: normal;
  font-family: arial;
  color: #000;
  margin-bottom: 19px;
  line-height: 1.4em;
}

.glightbox-light .gslide-desc {
  font-size: 0.86em;
  margin-bottom: 0;
  font-family: arial;
  line-height: 1.4em;
}

.glightbox-light .gslide-video {
  background: #000;
}

.glightbox-light .gprev,
.glightbox-light .gnext,
.glightbox-light .gclose {
  background-color: rgba(255, 255, 255, 0);
  border-radius: 4px;
}

.glightbox-light .gprev path,
.glightbox-light .gnext path,
.glightbox-light .gclose path {
  fill: #000;
}

.glightbox-light .gprev {
  position: absolute;
  top: -100%;
  left: 30px;
  width: 40px;
  height: 50px;
}

.glightbox-light .gnext {
  position: absolute;
  top: -100%;
  right: 30px;
  width: 40px;
  height: 50px;
}

.glightbox-light .gclose {
  width: 35px;
  height: 35px;
  top: 15px;
  right: 10px;
  position: absolute;
}

.glightbox-light .gclose svg {
  width: 18px;
  height: auto;
}

.glightbox-light .gclose:hover {
  opacity: 1;
}

/**/
.glightbox-light .gslide-media {
  -webkit-box-shadow: none !important;
  box-shadow: none !important;
}

.glightbox-light .description-left .gdesc-inner,
.glightbox-light .description-right .gdesc-inner {
  position: absolute;
  height: 100%;
  overflow-y: auto;
}

.glightbox-light .gprev,
.glightbox-light .gnext,
.glightbox-light .gclose {
  background-color: rgba(255, 255, 255, 0);
}

.glightbox-light .gprev:hover,
.glightbox-light .gnext:hover,
.glightbox-light .gclose:hover {
  background-color: rgba(255, 255, 255, 0);
}

.glightbox-light .gprev {
  top: 45%;
}

.glightbox-light .gnext {
  top: 45%;
}

@media (min-width: 992px) {
  .glightbox-light .gclose {
    opacity: 0.7;
    right: 20px;
  }
}
/**/
.glightbox-light .goverlay {
  background: white !important;
}

@media screen and (max-height: 420px) {
  .goverlay {
    background: white;
  }
}
/* override (light) */
.glightbox-light .gprev,
.glightbox-light .gnext {
  width: 55px !important;
  height: 70px !important;
}

.gclose svg, .gnext svg, .gprev svg {
  width: 50px !important;
}

.glightbox-light .gclose {
  width: 55px !important;
  height: 55px !important;
}

.glightbox-light .gclose svg {
  width: 30px !important;
}

.glightbox-light .gslide-title {
  font-size: 1.2em !important;
  font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif !important;
}

.glightbox-light .gslide-desc {
  font-size: 1em !important;
  font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif !important;
}

/* override (clean) */
.glightbox-clean .gprev,
.glightbox-clean .gnext {
  width: 55px !important;
  height: 70px !important;
}

.gclose svg, .gnext svg, .gprev svg {
  width: 50px !important;
}

.glightbox-clean .gclose {
  width: 55px !important;
  height: 55px !important;
}

.glightbox-clean .gclose svg {
  width: 30px !important;
}

.glightbox-clean .gslide-title {
  font-size: 1.2em !important;
  font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif !important;
}

.glightbox-clean .gslide-desc {
  font-size: 1em !important;
  font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif !important;
}

/* other override */
.gclose.disabled, .gnext.disabled, .gprev.disabled {
  opacity: 0;
}

/* new colorpicker */
.pickr {
  position: relative;
  overflow: visible;
  transform: translateY(0);
}
.pickr * {
  box-sizing: border-box;
  outline: none;
  border: none;
  -webkit-appearance: none;
}

.pickr .pcr-button {
  position: relative;
  height: 2em;
  width: 2em;
  padding: 0.5em;
  cursor: pointer;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif;
  border-radius: 0.15em;
  background: url('data:image/svg+xml;utf8, <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 50 50" stroke="%2342445A" stroke-width="5px" stroke-linecap="round"><path d="M45,45L5,5"></path><path d="M45,5L5,45"></path></svg>') no-repeat center;
  background-size: 0;
  transition: all 0.3s;
}
.pickr .pcr-button::before {
  position: absolute;
  content: "";
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('data:image/svg+xml;utf8, <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 2 2"><path fill="white" d="M1,0H2V1H1V0ZM0,1H1V2H0V1Z"/><path fill="gray" d="M0,0H1V1H0V0ZM1,1H2V2H1V1Z"/></svg>');
  background-size: 0.5em;
  border-radius: 0.15em;
  z-index: -1;
}
.pickr .pcr-button::before {
  z-index: initial;
}
.pickr .pcr-button::after {
  position: absolute;
  content: "";
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  transition: background 0.3s;
  background: var(--pcr-color);
  border-radius: 0.15em;
}
.pickr .pcr-button.clear {
  background-size: 70%;
}
.pickr .pcr-button.clear::before {
  opacity: 0;
}
.pickr .pcr-button.clear:focus {
  box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.85), 0 0 0 3px var(--pcr-color);
}
.pickr .pcr-button.disabled {
  cursor: not-allowed;
}

.pickr *,
.pcr-app * {
  box-sizing: border-box;
  outline: none;
  border: none;
  -webkit-appearance: none;
}
.pickr input:focus, .pickr input.pcr-active,
.pickr button:focus,
.pickr button.pcr-active,
.pcr-app input:focus,
.pcr-app input.pcr-active,
.pcr-app button:focus,
.pcr-app button.pcr-active {
  box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.85), 0 0 0 3px var(--pcr-color);
}
.pickr .pcr-palette,
.pickr .pcr-slider,
.pcr-app .pcr-palette,
.pcr-app .pcr-slider {
  transition: box-shadow 0.3s;
}
.pickr .pcr-palette:focus,
.pickr .pcr-slider:focus,
.pcr-app .pcr-palette:focus,
.pcr-app .pcr-slider:focus {
  box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.85), 0 0 0 3px rgba(0, 0, 0, 0.25);
}

.pcr-app {
  position: fixed;
  display: flex;
  flex-direction: column;
  z-index: 10000;
  border-radius: 0.1em;
  background: #fff;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s, visibility 0s 0.3s;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif;
  box-shadow: 0 0.15em 1.5em 0 rgba(0, 0, 0, 0.1), 0 0 1em 0 rgba(0, 0, 0, 0.03);
  left: 0;
  top: 0;
}
.pcr-app.visible {
  transition: opacity 0.3s;
  visibility: visible;
  opacity: 1;
}
.pcr-app .pcr-swatches {
  display: flex;
  flex-wrap: wrap;
  margin-top: 0.75em;
}
.pcr-app .pcr-swatches.pcr-last {
  margin: 0;
}
@supports (display: grid) {
  .pcr-app .pcr-swatches {
    display: grid;
    align-items: center;
    grid-template-columns: repeat(auto-fit, 1.75em);
  }
}
.pcr-app .pcr-swatches > button {
  font-size: 1em;
  position: relative;
  width: calc(1.75em - 5px);
  height: calc(1.75em - 5px);
  border-radius: 0.15em;
  cursor: pointer;
  margin: 2.5px;
  flex-shrink: 0;
  justify-self: center;
  transition: all 0.15s;
  overflow: hidden;
  background: transparent;
  z-index: 1;
}
.pcr-app .pcr-swatches > button::before {
  position: absolute;
  content: "";
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('data:image/svg+xml;utf8, <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 2 2"><path fill="white" d="M1,0H2V1H1V0ZM0,1H1V2H0V1Z"/><path fill="gray" d="M0,0H1V1H0V0ZM1,1H2V2H1V1Z"/></svg>');
  background-size: 6px;
  border-radius: 0.15em;
  z-index: -1;
}
.pcr-app .pcr-swatches > button::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--pcr-color);
  border: 1px solid rgba(0, 0, 0, 0.05);
  border-radius: 0.15em;
  box-sizing: border-box;
}
.pcr-app .pcr-swatches > button:hover {
  filter: brightness(1.05);
}
.pcr-app .pcr-swatches > button:not(.pcr-active) {
  box-shadow: none;
}
.pcr-app .pcr-interaction {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin: 0 -0.2em 0 -0.2em;
}
.pcr-app .pcr-interaction > * {
  margin: 0 0.2em;
}
.pcr-app .pcr-interaction input {
  letter-spacing: 0.07em;
  font-size: 0.75em;
  text-align: center;
  cursor: pointer;
  color: #75797e;
  background: #f1f3f4;
  border-radius: 0.15em;
  transition: all 0.15s;
  padding: 0.45em 0.5em;
  margin-top: 0.75em;
}
.pcr-app .pcr-interaction input:hover {
  filter: brightness(0.975);
}
.pcr-app .pcr-interaction input:focus {
  box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.85), 0 0 0 3px rgba(66, 133, 244, 0.75);
}
.pcr-app .pcr-interaction .pcr-result {
  color: #75797e;
  text-align: left;
  flex: 1 1 8em;
  min-width: 8em;
  transition: all 0.2s;
  border-radius: 0.15em;
  background: #f1f3f4;
  cursor: text;
}
.pcr-app .pcr-interaction .pcr-result::selection {
  background: #4285f4;
  color: #fff;
}
.pcr-app .pcr-interaction .pcr-type.active {
  color: #fff;
  background: #4285f4;
}
.pcr-app .pcr-interaction .pcr-save,
.pcr-app .pcr-interaction .pcr-cancel,
.pcr-app .pcr-interaction .pcr-clear {
  color: #fff;
  width: auto;
}
.pcr-app .pcr-interaction .pcr-save,
.pcr-app .pcr-interaction .pcr-cancel,
.pcr-app .pcr-interaction .pcr-clear {
  color: #fff;
}
.pcr-app .pcr-interaction .pcr-save:hover,
.pcr-app .pcr-interaction .pcr-cancel:hover,
.pcr-app .pcr-interaction .pcr-clear:hover {
  filter: brightness(0.925);
}
.pcr-app .pcr-interaction .pcr-save {
  background: #4285f4;
}
.pcr-app .pcr-interaction .pcr-clear,
.pcr-app .pcr-interaction .pcr-cancel {
  background: #f44250;
}
.pcr-app .pcr-interaction .pcr-clear:focus,
.pcr-app .pcr-interaction .pcr-cancel:focus {
  box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.85), 0 0 0 3px rgba(244, 66, 80, 0.75);
}
.pcr-app .pcr-selection .pcr-picker {
  position: absolute;
  height: 18px;
  width: 18px;
  border: 2px solid #fff;
  border-radius: 100%;
  user-select: none;
}
.pcr-app .pcr-selection .pcr-color-palette,
.pcr-app .pcr-selection .pcr-color-chooser,
.pcr-app .pcr-selection .pcr-color-opacity {
  position: relative;
  user-select: none;
  display: flex;
  flex-direction: column;
  cursor: grab;
  cursor: -moz-grab;
  cursor: -webkit-grab;
}
.pcr-app .pcr-selection .pcr-color-palette:active,
.pcr-app .pcr-selection .pcr-color-chooser:active,
.pcr-app .pcr-selection .pcr-color-opacity:active {
  cursor: grabbing;
  cursor: -moz-grabbing;
  cursor: -webkit-grabbing;
}

.pcr-app[data-theme=nano] {
  width: 14.25em;
  max-width: 95vw;
}
.pcr-app[data-theme=nano] .pcr-swatches {
  margin-top: 0.6em;
  padding: 0 0.6em;
}
.pcr-app[data-theme=nano] .pcr-interaction {
  padding: 0 0.6em 0.6em 0.6em;
}
.pcr-app[data-theme=nano] .pcr-selection {
  display: grid;
  grid-gap: 0.6em;
  grid-template-columns: 1fr 4fr;
  grid-template-rows: 5fr auto auto;
  align-items: center;
  height: 10.5em;
  width: 100%;
  align-self: flex-start;
}
.pcr-app[data-theme=nano] .pcr-selection .pcr-color-preview {
  grid-area: 2/1/4/1;
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: center;
  margin-left: 0.6em;
}
.pcr-app[data-theme=nano] .pcr-selection .pcr-color-preview .pcr-last-color {
  display: none;
}
.pcr-app[data-theme=nano] .pcr-selection .pcr-color-preview .pcr-current-color {
  position: relative;
  background: var(--pcr-color);
  width: 2em;
  height: 2em;
  border-radius: 50em;
  overflow: hidden;
}
.pcr-app[data-theme=nano] .pcr-selection .pcr-color-preview .pcr-current-color::before {
  position: absolute;
  content: "";
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('data:image/svg+xml;utf8, <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 2 2"><path fill="white" d="M1,0H2V1H1V0ZM0,1H1V2H0V1Z"/><path fill="gray" d="M0,0H1V1H0V0ZM1,1H2V2H1V1Z"/></svg>');
  background-size: 0.5em;
  border-radius: 0.15em;
  z-index: -1;
}
.pcr-app[data-theme=nano] .pcr-selection .pcr-color-palette {
  grid-area: 1/1/2/3;
  width: 100%;
  height: 100%;
  z-index: 1;
}
.pcr-app[data-theme=nano] .pcr-selection .pcr-color-palette .pcr-palette {
  border-radius: 0.15em;
  width: 100%;
  height: 100%;
}
.pcr-app[data-theme=nano] .pcr-selection .pcr-color-palette .pcr-palette::before {
  position: absolute;
  content: "";
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('data:image/svg+xml;utf8, <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 2 2"><path fill="white" d="M1,0H2V1H1V0ZM0,1H1V2H0V1Z"/><path fill="gray" d="M0,0H1V1H0V0ZM1,1H2V2H1V1Z"/></svg>');
  background-size: 0.5em;
  border-radius: 0.15em;
  z-index: -1;
}
.pcr-app[data-theme=nano] .pcr-selection .pcr-color-chooser {
  grid-area: 2/2/2/2;
}
.pcr-app[data-theme=nano] .pcr-selection .pcr-color-opacity {
  grid-area: 3/2/3/2;
}
.pcr-app[data-theme=nano] .pcr-selection .pcr-color-chooser,
.pcr-app[data-theme=nano] .pcr-selection .pcr-color-opacity {
  height: 0.5em;
  margin: 0 0.6em;
}
.pcr-app[data-theme=nano] .pcr-selection .pcr-color-chooser .pcr-picker,
.pcr-app[data-theme=nano] .pcr-selection .pcr-color-opacity .pcr-picker {
  top: 50%;
  transform: translateY(-50%);
}
.pcr-app[data-theme=nano] .pcr-selection .pcr-color-chooser .pcr-slider,
.pcr-app[data-theme=nano] .pcr-selection .pcr-color-opacity .pcr-slider {
  flex-grow: 1;
  border-radius: 50em;
}
.pcr-app[data-theme=nano] .pcr-selection .pcr-color-chooser .pcr-slider {
  background: linear-gradient(to right, red, yellow, lime, aqua, blue, fuchsia, red);
}
.pcr-app[data-theme=nano] .pcr-selection .pcr-color-opacity .pcr-slider {
  background: linear-gradient(to right, transparent, black), url('data:image/svg+xml;utf8, <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 2 2"><path fill="white" d="M1,0H2V1H1V0ZM0,1H1V2H0V1Z"/><path fill="gray" d="M0,0H1V1H0V0ZM1,1H2V2H1V1Z"/></svg>');
  background-size: 100%, 0.25em;
}

._rte-colpick_container {
  position: relative;
}

._rte-colpick {
  display: none !important;
}

.pop-picker {
  z-index: 10050 !important;
  width: 240px;
  height: 362px;
  padding: 10px;
  box-sizing: border-box;
}

._pop-colpick_container {
  position: relative;
  width: 100%;
}

._pop-colpick {
  display: none !important;
}

.pcr-app {
  box-shadow: none;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  background: transparent !important;
}

.pcr-app[data-theme=nano] .pcr-interaction {
  padding: 0;
}

.pcr-app[data-theme=nano] .pcr-swatches {
  display: flex;
  padding: 0;
  margin-top: 12px !important;
}

.pcr-app .pcr-interaction .pcr-result {
  width: 100%;
  flex: none;
  height: 36px !important;
  margin-top: 8px !important;
  font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif !important;
  font-weight: 300 !important;
  transition: none !important;
}

.pcr-app .pcr-swatches > button {
  font-size: 1em !important;
  position: relative !important;
  width: 30.7px !important;
  height: 30px !important;
  border-radius: 0px !important;
  cursor: pointer;
  margin: 0 !important;
  flex-shrink: 0;
  justify-self: center;
  transition: all 0.15s;
  overflow: hidden;
  background: rgba(0, 0, 0, 0) !important;
  z-index: 1;
}

.pcr-app .pcr-swatches > button::before,
.pcr-app .pcr-swatches > button::after {
  border-radius: 0px !important;
}

.pcr-app[data-theme=nano] .pcr-selection .pcr-color-preview .pcr-last-color {
  display: none !important;
}

.pcr-app[data-theme=nano] .pcr-selection {
  grid-gap: 1em !important;
  height: 160px !important;
}

.pcr-app[data-theme=nano] .pcr-selection .pcr-color-chooser, .pcr-app[data-theme=nano] .pcr-selection .pcr-color-opacity {
  height: 0.8em;
  margin: 0 0.6em;
}

.pcr-app[data-theme=nano] .pcr-selection .pcr-color-preview .pcr-current-color {
  width: 2.5em;
  height: 2.5em;
}

.pcr-app .pcr-interaction .pcr-clear, .pcr-app .pcr-interaction .pcr-cancel {
  line-height: 1;
  width: 74px;
  height: 24px;
  font-size: 0.9em;
  background: #cfcfcf66;
  color: #111;
  outline: none;
}

.pcr-app .pcr-interaction input {
  margin: 0.75em 0 0 0;
}

.pcr-app .pcr-interaction {
  justify-content: flex-end;
}

.pcr-app .pcr-interaction .pcr-clear:focus,
.pcr-app .pcr-interaction .pcr-cancel:focus {
  box-shadow: none !important;
}

.dark .pcr-app .pcr-interaction .pcr-clear,
.dark .pcr-app .pcr-interaction .pcr-cancel {
  color: white !important;
}

.hide-drag-class {
  opacity: 0;
}
