export default {
	name: "useRegisterSW",
	data() {
		return {
			updateSW: undefined,
			__offlineReady: false,
			__needRefresh: false  
		};
	},
	async mounted() {
		try {
			const { registerSW } = await import("virtual:pwa-register");
			const vm = this;
			this.updateSW = registerSW({
				immediate: true,
				onOfflineReady() {
					vm.__offlineReady = true;
					vm.__onOfflineReadyFn();
				},
				onNeedRefresh() {
					vm.__needRefresh = true;
					vm.__onNeedRefreshFn();

				},
				onRegistered(swRegistration) {
					swRegistration && vm.handleSWManualUpdates(swRegistration);   
				},
				onRegisterError(e) {
					vm.handleSWRegisterError(e);    
				}  
			});
		} catch {
			console.log("PWA disabled.");
		}

	},
	methods: {
		async __closePromptUpdateSW() {
			this.__offlineReady = false;
			this.__needRefresh = false;
		},
		openUrl(url) {
			window.open(url, '_blank');
		},
		__onOfflineReadyFn() {
			console.log("onOfflineReady");
		},
		__onNeedRefreshFn() {
			console.log("onNeedRefresh");
		},
		__updateServiceWorker() {
			this.updateSW && this.updateSW(true);
			window.location.reload();
		},
		handleSWManualUpdates(swRegistration) {}, 
		handleSWRegisterError(error) {} 
	}
};