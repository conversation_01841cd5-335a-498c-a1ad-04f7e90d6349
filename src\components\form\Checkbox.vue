<template>
	<!-- 
	USAGE
	<checkbox
		:label="`Turn on settings`"
		:sub-label="`If this enabled the settings will on`"
		:value="isToggleValue"
		v-model="isToggleValue"
	/>
-->
	<div class="relative flex items-start">
		<div class="flex items-center h-5">
			<input
				:id="label"
				aria-describedby="checkbox-description" 
				type="checkbox"
				class="border-gray-300 rounded pointer"
				:class="{'form-checkbox focus:ring-gray-300 h-4 w-4 text-gray-300': disabled, 'focus:ring-primary-500 h-4 w-4 text-primary-600': !disabled}"
				:checked="value"
				:disabled="disabled"
				@change="toggle"
			>
		</div>
		<div class="ml-2 text-sm">
			<label
				v-if="label"
				:for="label"
				class="text-gray-700 pointer"
				:class="{'text-gray-300': disabled}"
			>{{ label }}</label>
			<p
				v-if="subLabel"
				id="checkbox-description"
				class="text-gray-500 pointer"
				:class="{'text-gray-300': disabled}"
			>
				{{ subLabel }}
			</p>
		</div>
	</div>
</template>

<script>
export default {
	props: {
		value: {
			type: Boolean,
			default: false,
		},
		label: {
			type: String,
			default: '',
		},
		subLabel: {
			type: String,
			default: '',
		},
		disabled: {
			type: Boolean,
			default: false,
		},
	},
	mounted() {
	},
	methods: {
		toggle() {
			this.$emit('update:modelValue', !this.value);
		},
	}
};
</script>