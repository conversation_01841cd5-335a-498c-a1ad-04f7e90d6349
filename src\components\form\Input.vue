<template>
  <div class="relative">
    <!-- global -->
    <div v-if="type !== 'password' && type !== 'direct-form' && type !== 'timer'">
      <input
        class="w-full shadow-sm"
        :class="theClass"
        :type="type"
        :name="name"
        :data-test="dataTest"
        :value="value"
        :placeholder="placeholder"
        :autocomplete="autocomplete"
        :disabled="isDisabled"
        :style="customStyle"
        :min="min"
        :max="max"
        @input="onInput"
        @change="onChange"
      >
    </div>
    <!-- input direct form -->
    <div v-if="type === 'direct-form'">
      <textarea
        ref="formInputVideo"
        class="w-full shadow-sm mt-[1px] ml-[1px] pr-[60px]"
        :style="textAreaStyle"
        :class="theClass"
        :name="name"
        :data-test="dataTest"
        :value="value"
        :placeholder="placeholder"
        :autocomplete="autocomplete"
        :disabled="isDisabled"
        @input="onInput"
        @change="onChange"
      />
    </div>
    <!-- password -->
    <div v-if="type === 'password'">
      <input
        class="w-full shadow-sm h-[35px]"
        :class="theClass"
        :data-test="dataTest"
        :type="passwordMode"
        :name="name"
        :value="value"
        :maxlength="maxlength"
        :placeholder="placeholder"
        :autocomplete="autocomplete"
        @input="onInput"
        @change="onChange"
      >
      <div
        class="absolute right-0 top-0 mt-3 mr-2"
        @click="togglePassword"
      >
        <EyeOffIcon
          v-if="passwordMode === 'password'"
          class="flex-shrink-0 ml-[-20px] mr-2 h-4 w-4 text-gray-400"
          aria-hidden="true"
        />
        <EyeIcon
          v-if="passwordMode === 'text'"
          class="flex-shrink-0 ml-[-20px] mr-2 h-4 w-4 text-gray-400"
          aria-hidden="true"
        />
      </div>
    </div>
    <template v-if="type === 'timer'">
      <scrubber-masked
        :class="theClass"
        :min="minTime"
        :max="maxTime"
        :steps="step"
        :style="customStyle"
        :value="value"
        :disabled="disabled"
        style="text-align: center"
        @blur="onBlur"
        @focus="onFocus"
        @input="onNumberChange"
      />
    </template>
  </div>
</template>

<script>

import {
  EyeOffIcon,
  EyeIcon,
} from '@heroicons/vue/solid';
import ScrubberMasked from '@/components/form/ScrubberMasked.vue';

export default {
  components: {
    EyeOffIcon,
    EyeIcon,
    ScrubberMasked
  },
  props: {
    type: {
      type: String,
      default: () => 'text',
    },
    value: {
      type: String,
      default: () => '',
      required: true,
    },
    placeholder: {
      type: String,
      default: () => '',
    },
    autocomplete: {
      type: String,
      default: () => 'off',
    },
    isDisabled: {
      type: Boolean,
      default: () => false,
    },
    dataTest: {
      type: String,
      default: () => '',
    },
    customStyle: {
      type: String,
      default: () => '',
    },
    maxlength: {
      type: String,
      default: () => '',
    },
    max: {
      type: Number,
      default: () => 100000,
    },
    min: {
      type: Number,
      default: () => 0,
    },
    minTime: {
      type: Number,
      default: () => 0,
    },
    maxTime: {
      type: Number,
      default: () => 0,
    },
    uppercase: {
      type: Boolean,
      default: () => false,
    }
  },
  data() {
    return {
      passwordMode: 'password',
      isFocus: false,
    };
  },
  computed: {
    name() {
      return this.value && typeof this.value === 'string' ? this.value.toLowerCase() : '';
    },
    theClass() {
      let myClass = `appearance-none block px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 text-sm`;
      if (this.uppercase) myClass = `uppercase appearance-none block px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 text-sm`;
      if (this.isDisabled) myClass += ` text-[#999] bg-gray-100`;
      return myClass;
    },
    textAreaStyle() {
      // eslint-disable-next-line vue/no-async-in-computed-properties
      this.$nextTick( () => {
        const input = this.$refs.formInputVideo;
        input.style.height = (input.scrollHeight)+"px";
      });
      if (this.value && this.value.length < 70 || this.value.length === 0) {
        return 'resize: none; overflow: hidden; height: 38px;';
      }
      return 'resize: none; overflow: hidden;';
    }
  },
  watch: {},
  created() {},
  mounted() {},
  beforeUnmount() {},
  methods: {
    onInput(event) {
      // Can add validation here
      this.$emit('update:modelValue', event.target.value);
    },
    onChange(event) { // Supports .lazy
      // Can add validation here
      this.$emit('update:modelValue', event.target.value);
    },
    togglePassword() {
      this.passwordMode = this.passwordMode === 'password' ? 'text' : 'password';
    },
    onNumberChange(value) {
      if (!value.target) {
        this.$emit('update:modelValue', value);
      } else {
        this.$emit('input', value.target.value);
      }
    },
    onFocus() {
      this.isFocus = true;
    },
    onBlur() {
      this.isFocus = false;
      this.$emit('blur');
    },
  },
};
</script>