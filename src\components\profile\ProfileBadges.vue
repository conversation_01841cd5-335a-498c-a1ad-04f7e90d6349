<template>
  <section class="card-profile">
    <h2 class="text-xl font-semibold text-gray-800 mb-6">
      {{ $t("Badges") }}
    </h2>
  
    <loader-circle v-if="isFetching" />

    <div v-if="!isFetching" class="relative">
      <template v-if="badges.length === 0 && !fetchError">
        <p class="text-gray-500 text-center italic p-10">
          {{ $t("Here come your earned badges") }}.
        </p>
      </template>

      <template v-else-if="fetchError">
        <p class="text-red-500 text-center italic p-10">
          {{ $t("Failed to load badges. Please try again.") }}
        </p>
        <div class="flex justify-center">
          <Button label="Retry" severity="warning" @click="fetchList" />
        </div>
      </template>

      <div
        v-else
        class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6 lg:max-h-[300px] lg:overflow-y-auto pr-3 pb-6 lg:pb-2"
      >
        <div
          v-for="(item, index) in badges"
          :key="index"
          class="relative aspect-square w-full group lg:flex items-center justify-center"
        >
          <img
            :src="item.file_url"
            class="hidden lg:block w-full h-full rounded-lg shadow-md object-cover"
            alt="Badge"
            @error="handleImageError(index)"
          >
          <img
            :src="item.file_url"
            class="lg:hidden w-full h-full rounded-lg shadow-md object-cover"
            alt="Badge"
            @error="handleImageError(index)"
            @click.stop="previewBadge(item)"
          >
          <a class="lg:hidden mt-2 flex items-center justify-end" :href="item.file_url" download>
            <i
              class="pi pi-download mr-2"
              style="color: #3b82f6"
            /><div class="truncate text-primary-600">
              {{ item.name }}
            </div>
          </a>
          <div
            class="hidden lg:flex absolute inset-0 items-center justify-center bg-black bg-opacity-50 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"
          >
            <Button
              icon="pi pi-eye"
              class="mr-1"
              aria-label="Preview Badge"
              severity="secondary"
              @click.stop="previewBadge(item)"
            />
            <Button
              icon="pi pi-download"
              severity="secondary"
              aria-label="Download Badge"
              @click.stop="downloadBadge(item)"
            />
          </div>
        </div>
      </div>
    </div>
  </section>
  <PreviewBadge
    ref="previewCert"
    v-model:visible="isShowPreview"
    :item="selectedBadge"
    @onClose="closeModalTemplate"
  />
</template>

<script>
import badgeApi from "@/api/badge";
import PreviewBadge from "@/components/profile/PreviewBadge.vue";
export default {
  components: {
    PreviewBadge,
  },
  props: {},
  data() {
    return {
      isFetching: false,
      badges: [],
      isShowPreview: false,
      selectedBadge: null,
      fetchError: false,
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {
    this.fetchList();
  },
  beforeUnmount() {},
  methods: {
    closeModalTemplate() {
      this.isShowPreview = false;
      this.selectedBadge = null;
    },
    fetchList() {
      this.isFetching = true;
      this.fetchError = false;
      const params = {
        order_by: "created_at",
        sort_by: "desc",
        limit: 9999,
        page: 1,
      };
      if (this.keyword) params.keyword = this.keyword;
      const callback = (response) => {
        this.badges = response?.data?.map(item => item.badges);
        this.isFetching = false;
      };
      const errorCallback = (error) => {
        this.fetchError = true;
        const message = error.response.data.message;
        this.__showNotif("error", this.$t('Error'), message);
        this.isFetching = false;
      };
      badgeApi.getListMine(params, callback, errorCallback);
    },
    downloadBadge(badge) {
      const url = badge.file_url;
      const link = document.createElement("a");
      link.href = url;
      link.download = url.substring(url.lastIndexOf("/") + 1);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },
    previewBadge(badge) {
      this.selectedBadge = badge;
      this.isShowPreview = true;
    },
    handleImageError(index) {
      this.badges[index].file_url = "/placeholder-image.png";
    },
  },
};
</script>