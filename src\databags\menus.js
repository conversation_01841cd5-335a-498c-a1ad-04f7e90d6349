const SUPER_ADMIN_ROLE = 'super_admin';
const ADMIN_ROLE = 'admin';
const MANAGER_ROLE = 'manager';
const MEMBER_ROLE = 'member';

export const MAIN_MENUS = [
  { 
    roles: [],
    items: [
      {
        label: 'Dashboard',
        icon: 'pi pi-fw pi-home',
        to: '/',
        roles: [],
      },
      {
        label: 'Courses',
        icon: 'pi pi-fw pi-book',
        to: '/courses',
        roles: [SUPER_ADMIN_ROLE, ADMIN_ROLE, MANAGER_ROLE, MEMBER_ROLE ],
      },
      {
        label: 'Webinars',
        icon: 'pi pi-fw pi-video',
        to: '/webinars',
        roles: [SUPER_ADMIN_ROLE, ADMIN_ROLE, MEMBER_ROLE, MANAGER_ROLE],
      },
      {
        label: 'Library',
        icon: 'pi pi-fw pi-table',
        to: '/libraries',
        roles: [SUPER_ADMIN_ROLE, ADMIN_ROLE, MEMBER_ROLE, MANAGER_ROLE],
      },
      {
        label: 'Calendar',
        icon: 'pi pi-fw pi-calendar',
        to: '/calendars',
        roles: [SUPER_ADMIN_ROLE, ADMIN_ROLE, MEMBER_ROLE, MANAGER_ROLE],
      },
      {
        label: 'Groups',
        icon: 'pi pi-fw pi-users',
        to: '/groups',
        roles: [SUPER_ADMIN_ROLE, MANAGER_ROLE],
      },
      {
        label: 'Certifications',
        icon: 'pi pi-fw pi-bookmark',
        to: '/certifications',
        roles: [SUPER_ADMIN_ROLE, MANAGER_ROLE],
      },
      {
        label: 'Tags',
        icon: 'pi pi-fw pi-tags',
        to: '/tags',
        roles: [SUPER_ADMIN_ROLE, ADMIN_ROLE],
      },
      {
        label: 'Categories',
        icon: 'pi pi-fw pi-th-large',
        to: '/categories',
        roles: [SUPER_ADMIN_ROLE, ADMIN_ROLE],
      },
      {
        label: 'Badges',
        icon: 'pi pi-fw pi-check-circle',
        to: '/badges',
        roles: [SUPER_ADMIN_ROLE, ADMIN_ROLE],
      },
      {
        label: 'Users',
        icon: 'pi pi-fw pi-users',
        to: '/admin/users',
        roles: [SUPER_ADMIN_ROLE, ADMIN_ROLE],
      },
      {
        label: 'Analytics',
        icon: 'pi pi-fw pi-chart-bar',
        to: '/admin/analytics',
        roles: [SUPER_ADMIN_ROLE, ADMIN_ROLE],
      },
      {
        label: 'Settings',
        icon: 'pi pi-fw pi-cog',
        to: '/admin/settings',
        roles: [SUPER_ADMIN_ROLE, ADMIN_ROLE],
      },
      {
        label: 'Support',
        icon: 'pi pi-fw pi-question-circle',
        to: '/admin/supports',
        roles: [SUPER_ADMIN_ROLE, ADMIN_ROLE],
      },
    ],
  }
  

  // Example nested menu
  // {
  // 	label: 'Hierarchy',
  // 	items: [
  // 		{
  // 			label: 'Submenu 1',
  // 			icon: 'pi pi-fw pi-bookmark',
  // 			items: [
  // 				{
  // 					label: 'Submenu 1.1',
  // 					icon: 'pi pi-fw pi-bookmark',
  // 					items: [
  // 						{ label: 'Submenu 1.1.1', icon: 'pi pi-fw pi-bookmark' },
  // 						{ label: 'Submenu 1.1.2', icon: 'pi pi-fw pi-bookmark' },
  // 						{ label: 'Submenu 1.1.3', icon: 'pi pi-fw pi-bookmark' }
  // 					]
  // 				},
  // 				{
  // 					label: 'Submenu 1.2',
  // 					icon: 'pi pi-fw pi-bookmark',
  // 					items: [{ label: 'Submenu 1.2.1', icon: 'pi pi-fw pi-bookmark' }]
  // 				}
  // 			]
  // 		},
  // 		{
  // 			label: 'Submenu 2',
  // 			icon: 'pi pi-fw pi-bookmark',
  // 			items: [
  // 				{
  // 					label: 'Submenu 2.1',
  // 					icon: 'pi pi-fw pi-bookmark',
  // 					items: [
  // 						{ label: 'Submenu 2.1.1', icon: 'pi pi-fw pi-bookmark' },
  // 						{ label: 'Submenu 2.1.2', icon: 'pi pi-fw pi-bookmark' }
  // 					]
  // 				},
  // 				{
  // 					label: 'Submenu 2.2',
  // 					icon: 'pi pi-fw pi-bookmark',
  // 					items: [{ label: 'Submenu 2.2.1', icon: 'pi pi-fw pi-bookmark' }]
  // 				}
  // 			]
  // 		}
  // 	]
  // },
];

export const PROFILE_DROPDOWN_MENUS = [
  {
    label: 'Profile',
    icon: 'pi pi-user',
    href: '/profile',
  },
  {
    label: 'Change Password',
    icon: 'pi pi-unlock',
    href: '/change-password',
  },
  {
    label: 'Logout',
    icon: 'pi pi-sign-out',
    href: '/logout',
  },
  {
    separator: true
  }
];

export const COURSE_CREATE_MENUS = [
  { 
    roles: [],
    items: [
      {
        label: 'Basic Information',
        icon: '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 28 28" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-info"><circle cx="12" cy="12" r="10"/><path d="M12 16v-4"/><path d="M12 8h.01"/></svg>',
      },
      {
        label: 'Lessons',
        icon: '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 28 28" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-book-open-check"><path d="M12 21V7"/><path d="m16 12 2 2 4-4"/><path d="M22 6V4a1 1 0 0 0-1-1h-5a4 4 0 0 0-4 4 4 4 0 0 0-4-4H3a1 1 0 0 0-1 1v13a1 1 0 0 0 1 1h6a3 3 0 0 1 3 3 3 3 0 0 1 3-3h6a1 1 0 0 0 1-1v-1.3"/></svg>',
      },
      {
        label: 'Options',
        icon: '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 28 28" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-settings-2"><path d="M20 7h-9"/><path d="M14 17H5"/><circle cx="17" cy="17" r="3"/><circle cx="7" cy="7" r="3"/></svg>',
      },
      {
        label: 'Analytics',
        icon: '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 28 28" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chart-column"><path d="M3 3v16a2 2 0 0 0 2 2h16"/><path d="M18 17V9"/><path d="M13 17V5"/><path d="M8 17v-3"/></svg>',
      },
      {
        label: 'Preview & Publish',
        icon: '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 28 28" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-send"><path d="M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z"/><path d="m21.854 2.147-10.94 10.939"/></svg>',
      },
      {
        separator: true
      }
    ]
  }
];

export const COURSE_DROPDOWN_MENUS = [
  {
    label: 'Search',
    href: '/search',
  },
  {
    label: 'Exit Course',
    href: '/exit',
  },

];