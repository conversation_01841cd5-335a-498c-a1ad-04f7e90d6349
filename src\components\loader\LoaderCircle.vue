<template>
  <div
    v-show="isBg"
    :style="{ 'z-index': zIndex }"
    class="p-8 bg-gray-200 opacity-50 text-center top-0 bottom-0 my-auto mx-auto right-0 left-0 absolute h-10 w-10 rounded-lg"
  />
  <svg
    :style="{ 'z-index': zIndex }"
    class="animate-spin text-primary-600 text-center top-0 bottom-0 my-auto mx-auto right-0 left-0 h-10 w-10"
    :class="customClass"
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
  >
    <circle
      class="opacity-10"
      cx="12"
      cy="12"
      r="10"
      stroke="currentColor"
      stroke-width="4"
    />
    <path
      class="opacity-100"
      fill="currentColor"
      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
    />
  </svg>
</template>

<script>
export default {
  components: {},
  props: {
    isBg: {
      type: Boolean,
      default: () => false,
    },
    zIndex: {
      type: Number,
      default: () => 50,
    },
    customClass: {
      type: String,
      default: () => 'absolute'
    },
  },
  data() {
    return {};
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  beforeUnmount() {},
  methods: {},
};
</script>
