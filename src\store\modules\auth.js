import createPersistedState from "vuex-persistedstate";
import { client, updateLocale } from '@/libraries/http-client';
import authApi from '../../api/auth';

const TOKEN_KEY = 'access_token';

export default {
  namespaced: true,
  plugins: [createPersistedState()],
  state: () => ({
    user: null,
    token: "",
    expiresAt: null,
    isFetchingUser: false,
    role: '',
    isShowGallery: false,
    isShowOnboard: '0',
    siteLogo: "https://minio.beta.mylms.no/betalms/siteLogo/images/812-300.png",
    siteFavicon: `https://minio.beta.mylms.no/betalms/siteFavicon/images/180.png`,
    siteLocale: 'en'
  }),
  mutations: {
    setUser(state, user) {
      state.user = user;
    },
    setToken(state, token) {
      state.token = token;
      // Put access token to client header
      localStorage.setItem(TOKEN_KEY, token);
      client.defaults.headers.Authorization = `Bearer ${token}`;
    },
    setExpiresAt(state, expiresAt) {
      state.expiresAt = expiresAt;
    },
    fetchUserStart(state) {
      state.isFetchingUser = true;
    },
    fetchUserEnd(state) {
      state.isFetchingUser = false;
    },
    setRole(state, role) {
      localStorage.setItem('role', role);
      state.role = role;
    },
    setIsShowGallery(state, value) {
      state.isShowGallery = value;
    },
    setIsShowOnboard(state, value) {
      state.isShowOnboard = value;
    },
    setSiteLogo(state, value) {
      state.siteLogo = value;
    },
    setSiteFavicon(state, value) {
      state.siteFavicon = value;
    },
    setSiteLocale(state, value) {
      state.siteLocale = value;
    },
    setUserFeatures(state, payload) {
      payload.forEach(updatedFeature => {
        const index = state.user.features.findIndex(
          (feature) => feature.id === updatedFeature.id
        );
        if (index !== -1) {
          // Update the permission object within the feature
          state.user.features[index].permission = {
            ...state.user.features[index].permission,
            can_read: updatedFeature.can_read,
            can_write: updatedFeature.can_write,
            can_delete: updatedFeature.can_delete,
          };
        }
      });
    }
  },
  actions: {
    updateUserFeatures({commit}, value) {
      commit("setUserFeatures", value);
    },
    updateSiteLocale({ commit }, value) {
      commit("setSiteLocale", value);
      updateLocale(value);
    },
    updateSiteLogo({ commit }, value) {
      commit("setSiteLogo", value);
    },
    updateSiteFavicon({ commit }, value) {
      commit("setSiteFavicon", value);
      // Remove existing favicon
      document
        .querySelectorAll("link[rel~='icon']")
        .forEach((faviconTag) => faviconTag.remove());
        
      // Create new favicon
      const link = document.createElement("link");
      link.rel = "icon";
      link.type = "image/png"; // Set MIME type
      link.href = value;
      document.head.appendChild(link);
    },
    toggleMenuGallery({ commit }, value) {
      commit("setIsShowGallery", value);
    },
    setIsShowOnboard({ commit }, value) {
      commit("setIsShowOnboard", value);
    },
    clearAuth({ commit }) {
      commit("setUser", null);
      commit("setToken", "");
      localStorage.removeItem(TOKEN_KEY);
      localStorage.removeItem('my-app-bro');
      localStorage.removeItem('role');
    },
    setSession({ commit }, data) {
      if (data.token) commit("setToken", data.token);
      if (data.expiresAt) commit("setExpiresAt", data.expiresAt);         
    },
    setToken({ commit }, token) {
      commit("setToken", token);
    },
    setExpiresAt({ commit }, expires_at) {
      commit("setExpiresAt", expires_at);
    },
    setUser({ commit }, user) {
      commit("setUser", user);
    },
    fetchUser({ commit }, cb=null) {
      if (this.isFetchingUser) {
        return;
      }
      commit("fetchUserStart");
      const callback = function (response) {
        const user = response.data;
        if (cb) {
          cb(user);
        }
        localStorage.setItem('locale', user.language);
        commit("fetchUserEnd");
        commit("setUser", user);
        const locale = user.language;
        this.$i18n.locale = locale;
        if (locale) localStorage.setItem("locale", locale);
        updateSiteLocale(locale);
        let role = localStorage.getItem('role');
        const roles = JSON.parse(user.roles);
        const isSuperAdmin = roles.includes('super_admin');
        const isClient = roles.includes('client');
        if (isSuperAdmin) role = 'super_admin';
        else if (isClient) role = 'client';
        if (role && roles.includes(role)) {
          commit("setRole", role);
        } else {
          const firstRole = roles[0];
          commit("setRole", firstRole);
        }
        
      };
      const errorCallback = function () {
        commit("fetchUserEnd");
      };
      authApi.getProfile(callback, errorCallback);
    },
  },
  getters: {
    hasSession(state) {
      return state.token !== '';
    },
    siteLogo(state) {
      return state.siteLogo;
    },
    siteLocale(state) {
      return state.siteLocale;
    },
    getToken(state) {
      return state.token;
    },
    isFetchingUser(state) {
      return state.isFetchingUser;   
    },
    user(state) {
      return state.user;   
    },
    userFeatures(state) {
      return state.user && state.user.features ? state.user.features : null;
    },
    userRole(state) {
      return state.user && state.user.role ? state.user.role : null;   
    },
    userLanguage(state) {
      return state.user && state.user.language ? state.user.language : null;
    },
    isSuperAdmin(state) {
      return state.user && (state.role === 'super_admin');   
    },
    isClient(state) {
      return state.user && (state.role === 'client');   
    },
    getIsShowGallery(state) {
      return state.isShowGallery;
    },
    getIsShowOnboard(state) {
      return state.isShowOnboard;
    }
  },
};