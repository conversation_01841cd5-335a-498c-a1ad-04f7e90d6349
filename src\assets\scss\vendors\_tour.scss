.v-tour__target--highlighted {
    box-shadow: 0 0 0 99999px rgba(0,0,0,.4)!important;
}
.v-step__arrow::before {
    visibility: hidden!important;
}

.v-step__header {
    background: white!important;
    color: #545E6C!important;
    max-height: 10px;
}

.v-step__content{
    margin-bottom: 2em!important;
}

.v-step {
    background: white!important;
    color: #545E6C!important;
    font-weight: 500;
    font-size: 12px;
    text-align: left!important;
    padding: 10px 20px 10px 20px!important;
    border-radius: 10px!important;
    max-width: 300px!important;
    // padding-right: 20px!important;
}

.v-step-mobile {
    background: white!important;
    color: #545E6C!important;
    font-weight: 500;
    font-size: 12px;
    text-align: left!important;
    padding: 10px 20px 10px 20px!important;
    border-radius: 10px!important;
    max-width: 300px!important;
    // padding-right: 20px!important;
    inset: initial !important;
}

.v-step__content {
    margin-bottom: 1.5em !important;
}
