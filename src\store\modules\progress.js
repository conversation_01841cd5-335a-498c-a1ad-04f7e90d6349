// import createPersistedState from "vuex-persistedstate";

export default {
  namespaced: true,
  // plugins: [createPersistedState()]
  state: () => ({
    progress: 0,
    isLoading: false,
  }),
  mutations: {
    setProgress(state, value) {
      state.progress = value;
    },
    startLoading(state) {
      state.isLoading = true;
      state.progress = 0; // Reset progress when starting
    },
    stopLoading(state) {
      state.isLoading = false;
      state.progress = 100; // Set to 100% when finished
    },
  },
  actions: {
    setProgress({ commit }, value) {
      commit('setProgress', value);
    },
    startLoading({ commit }) {
      commit('startLoading');
    },
    stopLoading({ commit }) {
      commit('stopLoading');
    },
		
  },
  getters: {
    getGlobalProgressLoading(state) {
      return state.progress;
    }

  },
};