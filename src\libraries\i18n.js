import { createI18n } from 'vue-i18n';
/*
 * The i18n resources in the path specified in the plugin `include` option can be read
 * as vue-i18n optimized locale messages using the import syntax
 */
import en from '@/locales/en.json';
import no from '@/locales/no.json';

let locale;
try {
  locale = localStorage.getItem('locale') || 'no';
} catch (e) {
  console.error('Error accessing localStorage:', e);
  locale = 'en';
}

const i18n = createI18n({
  locale: locale,
  messages: {
    en,
    no,
  }
});

export default i18n;
