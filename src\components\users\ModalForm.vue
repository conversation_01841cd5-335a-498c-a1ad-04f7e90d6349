<template>
  <Dialog
    :visible="isVisible"
    modal
    :header="!isEdit ? `${$t('Add')} ${formattedLabel}` : `${$t('Edit')} ${formattedLabel}`"
    :style="{ width: '40rem' }"
    class="relative"
    @update:visible="isVisible = $event"
  >
    <form
      class="space-y-3 relative overflow-y-auto"
      @submit.prevent="submit"
    >
      <!-- user info -->
      <div class="text-lg font-bold">{{ $t('User Information') }}:</div>
      <div class="grid grid-cols-12 gap-8">
        <div class="col-span-6">
          <div>
            <label
              for="first_name"
              class="block text-sm font-medium text-gray-700"
            >{{ $t('First name') }}*</label>
            <div class="mt-1">
              <InputText
                v-model="user.first_name"
                :type="`text`"
                :value="user.first_name"
                class="w-full"
              />
            </div>
          </div>
        </div>
        <div class="col-span-6">
          <div>
            <label
              for="last_name"
              class="block text-sm font-medium text-gray-700"
            >{{ $t('Last name') }}*</label>
            <div class="mt-1">
              <InputText
                v-model="user.last_name"
                :type="`text`"
                :value="user.last_name"
                class="w-full"
              />
            </div>
          </div>
        </div>  
      </div>

      <div>
        <label
          for="email"
          class="block text-sm font-medium text-gray-700"
        >{{ $t('Email address') }}*</label>
        <div class="mt-1">
          <InputText
            v-model="user.email"
            :type="`email`"
            :value="user.email"
            class="w-full"
          />
          <span
            v-if="!isValidEmailAddress && user.email && user.email.length !== 0"
            class="text-red-500 text-xs"
          >{{ $t('Invalid Email Address') }}</span>
        </div>
      </div>

      <div>
        <label
          for="phone"
          class="block text-sm font-medium text-gray-700"
        >{{ $t('Mobile Phone') }}</label>
        <div class="mt-1">
          <InputText
            v-model="user.phone"
            :type="`number`"
            :value="user.phone"
            class="w-full"
          />
        </div>
      </div>

      <div>
        <label
          for="title"
          class="block text-sm font-medium text-gray-700"
        >{{ $t('Title (i.e. job, student..)') }}</label>
        <div class="mt-1">
          <InputText
            v-model="user.title"
            :value="user.title"
            class="w-full"
          />
        </div>
      </div>  

      <div>
        <label
          for="organization"
          class="block text-sm font-medium text-gray-700"
        >{{ $t('Company, school, organization or other') }}</label>
        <div class="mt-1 ">
          <InputText
            v-model="user.organization"
            :value="user.organization"
            class="w-full"
          />
        </div>
      </div>  

      <!-- platform -->
      <div class="grid grid-cols-12 gap-8">
        <div class="col-span-6">
          <label
            for="language"
            class="block text-sm font-medium text-gray-700"
          >{{ $t('Preferred language') }}</label>
          <div class="mt-1">
            <Select
              v-model="user.language"
              :options="userLanguages"
              optionLabel="name"
              optionValue="id"
              :placeholder="$t('Select a Language')"
              class="w-full"
            />
          </div>
        </div>

        <div class="col-span-6">
          <label
            for="roles"
            class="block text-sm font-medium text-gray-700"
          >{{ $t('Roles') }}*</label>
          <div class="mt-1">
            <Select
              v-model="user.role_id"
              :options="roles"
              optionLabel="name"
              optionValue="id"
              :placeholder="$t('Select a Role')"
              class="w-full"
            />
          </div>
        </div>
      </div>

      <div>
        <label
          for="groups"
          class="block text-sm font-medium text-gray-700"
        >{{ $t('Groups') }}</label>
        <MultiSelect
          v-model="user.groups"
          :options="groups"
          optionValue="id"
          optionLabel="name"
          filter
          display="chip"
          placeholder="Select one or more groups"
          class="w-full mt-1 h-full"
        />
      </div>

      <!-- password -->
      <div>
        <label
          for="password"
          class="block text-sm font-medium text-gray-700"
        >{{ $t('New Password') }}</label>
        <div class="mt-1">
          <t-input
            v-model="user.password"
            :dataTest="'password'"
            :type="`password`"
            :value="user.password"
            class="w-full"
          />
        </div>
      </div>

      <div>
        <label
          for="password"
          class="block text-sm font-medium text-gray-700"
        >{{ $t('Confirm New Password') }}</label>
        <div class="mt-1">
          <t-input
            v-model="user.password_confirmation "
            :dataTest="'password_confirmation '"
            :type="`password`"
            :value="user.password_confirmation "
            class="w-full"
          />
          <p
            v-if="user.password && user.password_confirmation && user.password !== user.password_confirmation "
            class="mt-1 text-sm text-red-600"
          >
            {{ $t('Passwords do not match') }}
          </p>
          <p
            v-if="user.password && !user.password_confirmation "
            class="mt-1 text-sm text-red-600"
          >
            {{ $t('Please confirm password') }}
          </p>
          <p
            v-if="!user.password && user.password_confirmation "
            class="mt-1 text-sm text-red-600"
          >
            {{ $t('Passwords do not match') }}
          </p>
        </div>
      </div>

      <div>
        <label
          for="short_bio"
          class="block text-sm font-medium text-gray-700"
        >{{ $t('Short Bio') }}</label>
        <div class="mt-1">
          <Textarea
            v-model="user.short_bio"
            rows="3"
            cols="30"
            class="w-full"
          />
        </div>
      </div>

      <div class="pb-[4em]">
        <label
          for="img_url"
          class="block text-sm font-medium text-gray-700"
        >{{ $t('Profile Picture') }}</label>
        <uploader
          id="fileUploadMediaUser"
          class="mt-2"
          type="image"
          :preview="true"
          :multiple="false"
          :progressUploadFile="progressUploadFile"
          :existingPreviewImageOrVideo="user.img_url"
          @upload="onFileSelect"
          @remove="onRemoveFile"
        />
      </div>
    </form>
    <div class="flex justify-end absolute bottom-0 bg-white w-full py-5 left-0 rounded pr-4">
      <Button
        :disabled="isSaving || isUploadFile"
        outlined
        type="button"
        :label="$t('Cancel')"
        class="mr-2"
        @click="close"
      />
      <Button
        type="button"
        :loading="isSaving || isUploadFile"
        :disabled="isSaving || !isFormValid || isUploadFile"
        :label="isEdit ? $t('Save Changes') : $t('Save')"
        @click="submit"
      />
    </div>
  </Dialog>
</template>

<script>
import { USER_DEFAULT, USER_LANGUAGES } from '@/databags/user';
import userApi from '@/api/user';
import { isValidEmail } from '@/libraries/helper';
import fileApi from '@/api/files';
import MultiSelect from 'primevue/multiselect';
import groupApi from '@/api/group';
import TInput from "@/components/form/Input.vue";

export default {
  components: {
    MultiSelect,
    TInput
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    item: {
      type: Object,
      default: () => ({}),
    },
    roles: {
      type: Array,
      default: () => [],
    },
    right: {
      type: String,
      default: 'member',
    },
  },
  data() {
    return {
      isSaving: false,
      user: this.__duplicateVar(USER_DEFAULT),
      userLanguages: this.__duplicateVar(USER_LANGUAGES),
      progressUploadFile: 0,
      isUploadFile: false,
      groups: [],
    };
  },
  computed: {
    isVisible: {
      get() {
        return this.visible;
      },
      set(value) {
        this.$emit('update:visible', value);
      }
    },
    formattedLabel() {
      return this.right.replace(/_/g, ' ').replace(/\b\w/g, char => char.toUpperCase());
    },
    isEdit() {
      return this.item && this.item.id;
    },
    userLanguage() {
      return this.user.language ? this.user.language.id : 'en';
    },
    params() {
      const params = {
        email: this.user.email,
        password: this.user.password,
        password_confirmation : this.user.password_confirmation,
        full_name: this.user.full_name,
        phone: this.user.phone,
        role_id: this.user.role_id,
        language: this.user.language,
        img_url: this.__getDynamicPath(this.user.img_url),
        job_title: this.user.job_title,
        organization: this.user.organization,
        location: this.user.location,
        short_bio: this.user.short_bio,
        endpoint: this.right,
        group_ids: JSON.stringify(this.user.groups),
        title: this.user.title,
        first_name: this.user.first_name,
        last_name: this.user.last_name,
      };
      return params;
    },
    isValidEmailAddress() {
      return isValidEmail(this.user.email);
    },
    isValidPassword() {
      if (this.user.password || this.user.password_confirmation ) {
        return this.user.password === this.user.password_confirmation ;
      }
      return true;
    },
    isFormValid() {
      return (
        this.isValidEmailAddress
        && this.isValidPassword
				&& this.user.last_name
				&& this.user.first_name
				&& this.user.role_id
      );
    },
  },
  watch: {
    isShow() {
      this.resetForm();
    },
    item() {
      this.resetForm();
      this.setData();
    },
  },
  created() {
    this.fetchGroups();
  },
  methods: {
    close() {
      this.$emit('onClose');
    },
    setData() {
      if (this.item) {
        this.user = this.__duplicateVar(this.item);
        this.user.groups = this.item.groups.map(group => group.id);
      }
    },
    resetForm() {
      this.user = this.__duplicateVar(USER_DEFAULT);
    },
    submit() {
      if (this.isEdit) this.update();
      else this.save();
    },
    update() {
      console.log(this.params);
      const params = this.params;
      if (this.user.password) params.password = this.user.password;
      if (this.user.password_confirmation) params.password_confirmation = this.user.password_confirmation;

      this.isSaving = true;
      const callback = (response) => {
        const item = response.data;
        this.$emit('onUpdate', item);
        this.isSaving = false;

        const message = response.message;
        this.__showNotif('success', this.$t('User'), message);

        this.resetForm();
      };
      const errorCallback = (error) => {
        const message = error.response.data.message;
        this.__showNotif('error', this.$t('Error'), message);
        this.isSaving = false;
      };
      userApi.update(this.user.id, params, callback, errorCallback);
    },
    save() {
      const params = this.params;
      if (this.user.password) params.password = this.user.password;
      if (this.user.password_confirmation) params.password_confirmation = this.user.password_confirmation;

      this.isSaving = true;
      const callback = (response) => {
        const item = response.data;
        this.$emit('onCreate', item);
        this.isSaving = false;
        this.progressUploadFile = 0;

        const message = response.message;
        this.__showNotif('success', this.$t('User'), message);

        this.resetForm();
      };
      const errorCallback = (error) => {
        const message = error.response.data.message;
        this.__showNotif('error', this.$t('Error'), message);
        this.isSaving = false;
      };
      userApi.create(this.params, callback, errorCallback);
    },
    onFileSelect(event) {
      this.uploadFile(event);
    },
    onRemoveFile() {
      this.user.img_url = '';
      this.file_name = '';
      this.progressUploadFile = 0;
    },
    uploadFile(files) {
      this.isUploadFile = true;
      if (files.length > 0) {
        const file = files[0];
        if (!file) {
          this.__showNotif('warning', 'Upload File', 'Sorry, currently we can\'t upload the file');
          this.isUploadFile = false;
          return;
        }

        // Initialize progress
        this.progressUploadFile = 0;

        // Prepare FormData
        const params = new FormData();
        params.append('file', file);
        params.append('folder', 'broadcast');

        // Callbacks
        const callback = (response) => {
          this.user.img_url = response.fullPath;
          this.file_name = file.name;
          this.isUploadFile = false;
        };

        const errorCallback = () => {
          this.__showNotif('warning', 'Upload File', 'Sorry, currently we can\'t upload the file. Please try again later.');
          this.isUploadFile = false;
        };

        const progressCallback = (progress) => {
          this.progressUploadFile = progress;
        };

        // Call the API
        fileApi.upload(params, callback, errorCallback, progressCallback);
      } else {
        this.__showNotif('warning', 'Upload File', 'No file selected.');
        this.isUploadFile = false;
      }
    },
    fetchGroups() {
      this.isFetching = true;
      const params = {
        limit: 9999,
        page: 1,
      };
      const callback = (response) => {
        const data = response.data;
        this.groups = data;
      };
      const errorCallback = (error) => {
        const message = error.response.data.message;
        this.__showNotif('error', this.$t('Error'), message);
        this.isFetching = false;
      };
      groupApi.getList(params, callback, errorCallback);
    },
  },
};
</script>
