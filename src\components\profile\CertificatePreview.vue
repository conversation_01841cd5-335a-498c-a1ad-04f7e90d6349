<template>
  <div v-if="show" class="certificate-container h-full flex justify-center items-center">
    <!-- Certificate frame with the background image -->
    <div :id="certification.slug" class="certificate-frame" :style="{ backgroundImage: `url(${bgImage})` }">
      <!-- Layered content -->
      <div class="certificate-content">
        <!-- User Name in center -->
        <div class="certificate-name mb-8">
          {{ userName }}
        </div>
        <!-- Signature at bottom-right (20% above bottom) -->
        <div class="certificate-signature">
          <img :src="signatureFile" alt="Signature">
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import moment from "moment";

export default {
  name: "CertificatePreview",
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    certification: {
      type: Object,
      default: () => ({}),
    },
  },
  computed: {
    ...mapGetters({
      user: "auth/user",
    }),

    // Background image
    bgImage() {
      return this.certification?.file_url || "";
    },

    // User name or fallback
    userName() {
      return this.user?.full_name || "USER NAME";
    },

    // Course name from certification or fallback
    moduleName() {
      return this.certification?.course?.name || "COURSE NAME";
    },

    // Signature file path
    signatureFile() {
      return this.certification?.signature_file || "";
    },

    // Example date using `certification.date`; adapt to your field
    dateLabel() {
      return this.certification?.date
        ? moment(this.certification.date).format("DD. MMMM YYYY")
        : "-";
    },
  },
};
</script>

<style scoped lang="scss">
.certificate-container {
  width: 100%;
  max-width: 100vw;
  box-sizing: border-box;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Modern responsive aspect ratio */
.certificate-frame {
  position: relative;
  width: 100%;
  aspect-ratio: 16 / 9;
  background-repeat: no-repeat;
  background-size: contain;
  background-position: center;
}

/* Overlay content */
.certificate-content {
  position: absolute;
  inset: 0;

  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 1rem;
}

/* Name in the center */
.certificate-name {
  font-family: "Zapfino", Helvetica, Arial, sans-serif;
  font-weight: bold;
  color: #e99e55;
  font-size: clamp(1.5rem, 5vw, 3rem);
  margin-bottom: 1rem;
}

/* Optional course name if added */
.certificate-course-name {
  font-weight: bold;
  font-size: clamp(1rem, 3vw, 1.5rem);
  margin: 0.5rem 0;
}

/* Date position: bottom-left-ish */
.certificate-date {
  position: absolute;
  left: 10%;
  bottom: 8%;
  font-size: clamp(0.8rem, 2vw, 1.2rem);
}

/* Signature: bottom-right */
.certificate-signature {
  position: absolute;
  right: 10%;
  bottom: 8%;

  img {
    width: 100%;
    max-width: 30vw;
    height: auto;
  }
}

</style>
