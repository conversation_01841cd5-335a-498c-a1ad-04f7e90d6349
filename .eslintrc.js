module.exports = {
  ignorePatterns: ["*.min.js", "dist/", "src/assets/*"],
  root: true,
  env: {
    node: true,
    browser: true,
    jquery: true,
  },
  extends: [
    // add more generic rulesets here, such as:
    // 'eslint:recommended',
    'plugin:vue/vue3-recommended',
    // 'plugin:vue/vue3-essential', // This option doesn't impose formatting rules
    // 'plugin:vue/vue3-strongly-recommended', // This option imposes formatting rules on your code to improve readability 
  ],
  rules: {
    'no-console': process.env.NODE_ENV === 'production' ? 'error' : 'off',
    'no-debugger': process.env.NODE_ENV === 'production' ? 'error' : 'off',
    // disallow parameter object manipulation except for specific exclusions
    'no-param-reassign': ['error', {
      props: true,
      ignorePropertyModificationsFor: [
        'state', // for vuex state
        'acc', // for reduce accumulators
        'e' // for e.returnvalue
      ]
    }],
    'no-tabs': 0,
    "indent": ["error", 2, { "SwitchCase": 1 }], // 2 spaces for general JS/TS
    "vue/html-indent": ["error", 2, { // 2 spaces for Vue templates
      "attribute": 1,
      "baseIndent": 1,
      "closeBracket": 0,
      "alignAttributesVertically": true,
      "ignores": []
    }],
    'import/no-extraneous-dependencies': 0,
    'max-len': 0,
    'no-shadow': 0,
    'no-plusplus': 0,
    'func-names': ['warn', 'as-needed'],
    'no-underscore-dangle': 0,
    'no-useless-escape': 0,
    'linebreak-style': 0,
    'radix': 0,
    'prefer-destructuring': 0,
    'template-curly-spacing' : 'off',
    "vue/max-attributes-per-line": "off",
    "vue/v-on-event-hyphenation": ["off", "never", {
      "autofix": false,
      "ignore": []
    }],
    "vue/require-explicit-emits": ["off", {
      "allowProps": true
    }],
    "vue/multi-word-component-names": ["off", {
      "ignores": []
    }],

    "vue/require-default-prop":'off',
    "vue/no-v-html":0,
    "semi": [1, "always"],
    "vue/attribute-hyphenation": ["error", "never", {
      "ignore": []
    }],
    'comma-spacing': ["warn", { "before": false, "after": true }],
    'keyword-spacing': ["warn", { "before": true }],
    'space-before-blocks': ["warn", "always"],
    "vue/singleline-html-element-content-newline": "off", // Disable the rule

  },
};
