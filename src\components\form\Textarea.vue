<template>
	<textarea
		:rows="rows"
		:class="theClass"
		:name="name"
		:value="value"
		:placeholder="placeholder"
		:autocomplete="autocomplete"
		:disabled="isDisabled"
		@input="onInput"
		@change="onChange"
	/>
</template>

<script>
export default {
	components: {
	},
	props: {
		value: {
			type: String,
			default: () => '',
			required: true,
		},
		placeholder: {
			type: String,
			default: () => '',
		},
		autocomplete: {
			type: String,
			default: () => 'off',
		},
		rows: {
			type: String,
			default: () => '3',
		},
		isDisabled: {
			type: Boolean,
			default: () => false,
		},
	},
	data() {
		return {
		};
	},
	computed: {
		name() {
			return this.value ? this.value.toLowerCase() : '';
		},
		theClass() {
			const myClass = `shadow-md block w-full sm:text-sm placeholder-gray-400 focus:ring-pink-500 focus:border-pink-500 border border-gray-300 rounded-md`;
			return myClass;
		},
	},
	watch: {},
	created() {},
	mounted() {},
	beforeUnmount() {},
	methods: {
		onInput(event) {
			// Can add validation here
			this.$emit('update:modelValue', event.target.value);
		},
		onChange(event) { // Supports .lazy
			// Can add validation here
			this.$emit('update:modelValue', event.target.value);
		},
	},
};
</script>