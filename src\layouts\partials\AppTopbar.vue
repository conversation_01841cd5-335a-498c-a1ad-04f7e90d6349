<script setup>
import { ref, computed, onMounted, onUnmounted } from "vue";
import { useRoute } from 'vue-router';
import { useLayout } from "@/layouts/partials/composables/layout";
import { PROFILE_DROPDOWN_MENUS } from "@/databags/menus";
const route = useRoute();
const { onMenuToggle, toggleDarkMode, isDarkTheme, layoutState } = useLayout();

const profileDropdownMenus = ref(PROFILE_DROPDOWN_MENUS);

const windowWidth = ref(0);

const updateWindowWidth = () => {
  windowWidth.value = window.innerWidth;
};

onMounted(() => {
  window.addEventListener("resize", updateWindowWidth);
  // Set initial value on mount
  updateWindowWidth(); // Important!
});

onUnmounted(() => {
  window.removeEventListener("resize", updateWindowWidth);
});

const isMobile = computed(() => {
  return windowWidth.value < 991;
});

// Mobile navigation routes
const mobileNavRoutes = ['NewsFeed', 'Inquiries', 'Chat', 'NewInquiry', 'Profile'];
const isMobileNavRoute = computed(() => mobileNavRoutes.includes(route.name));

// Get page title for mobile nav routes
const getPageTitle = computed(() => {
  const titleMap = {
    'NewsFeed': 'News Feed',
    'Inquiries': 'Inquiry',
    'Chat': 'Chat',
    'NewInquiry': 'New Inquiry',
    'Profile': 'Profile'
  };
  return titleMap[route.name] || 'Dashboard';
});

// Computed property for setting class based on the state
const menuClass = computed(() => {
  if (windowWidth.value > 991 && layoutState.staticMenuDesktopInactive) {
    return "w-full";
  }
  if (windowWidth.value <= 991) {
    return "w-full";
  }
  return "";
});
</script>

<template>
  <div class="layout-topbar w-full">
    <!-- Mobile header for mobile nav routes -->
    <div v-if="isMobile && isMobileNavRoute" class="mobile-header">
      <h1 class="mobile-page-title">{{ getPageTitle }}</h1>
      <button class="mobile-menu-button" @click="onMenuToggle">
        <i class="pi pi-bars" style="font-size: 1.5em" />
      </button>
    </div>

    <!-- Desktop header -->
    <div v-else class="layout-topbar-logo-container space-x-4">
      <button class="layout-topbar-action ml-2" @click="onMenuToggle">
        <i class="pi pi-bars" style="font-size: 1.5em" />
      </button>
      <router-link to="/">
        <img :key="siteLogo" class="h-[30px]" :src="dynamicSiteLogo">
      </router-link>
    </div>

    <div class="layout-topbar-actions">
      <!-- <div class="layout-config-menu">
				<button
					type="button"
					class="layout-topbar-action"
					@click="toggleDarkMode"
				>
					<i :class="['pi', { 'pi-moon': isDarkTheme, 'pi-sun': !isDarkTheme }]" />
				</button>
			</div> -->

      <!-- Notifications -->
      <div>
        <Button
          icon="pi pi-bell"
          class="p-button-rounded p-button-text relative"
          aria-label="Notifications"
          @click="toggleNotification"
        >
          <i class="pi pi-bell" />
          <span
            v-if="notifications.length"
            class="text-xs absolute top-1 right-1 text-white bg-red-600 px-1 rounded-full"
          >{{ notifications.length }}</span>
        </Button>

        <Popover ref="popoverNotification" class="min-w-[300px]">
          <template v-if="notifications.length > 0">
            <div
              v-for="(notification, index) in notifications"
              :key="index"
              class="p-3 border-bottom-1 surface-border hover:bg-slate-100 rounded-md"
            >
              <div class="text-base font-semibold">
                {{ notification.title }}
              </div>
              <div class="text-sm text-color-secondary">
                {{ notification.message }}
              </div>
            </div>
          </template>
          <template v-else>
            <div class="p-3 text-center text-gray-500">
              {{ $t("You have no new notifications.") }}
            </div>
          </template>
        </Popover>
      </div>

      <!-- Profile dropdown -->
      <button
        v-styleclass="{
          selector: '@next',
          enterFromClass: 'hidden',
          enterActiveClass: 'animate-scalein',
          leaveToClass: 'hidden',
          leaveActiveClass: 'animate-fadeout',
          hideOnOutsideClick: true,
        }"
        class="layout-topbar-menu-button layout-topbar-action"
        @click="$refs.profileDropdown.toggle($event)"
      >
        <i class="pi pi-ellipsis-v" />
      </button>

      <div class="layout-topbar-menu hidden lg:block">
        <div class="layout-topbar-menu-content">
          <button
            class="layout-topbar-action"
            @click="$refs.profileDropdown.toggle($event)"
          >
            <i class="pi pi-user" />
            <span>Profile</span>
          </button>
          <Menu
            ref="profileDropdown"
            :model="profileDropdownMenus"
            :popup="true"
            class="w-full md:w-60"
          >
            <template #item="{ item, props }">
              <router-link
                v-if="item.href"
                v-slot="{ href, navigate }"
                :to="item.href"
                custom
              >
                <a v-ripple :href="href" v-bind="props.action" @click="navigate">
                  <span :class="item.icon" />
                  <span class="ml-2">{{ $t(item.label) }}</span>
                </a>
              </router-link>
              <a
                v-else
                v-ripple
                :href="item.url"
                :target="item.target"
                v-bind="props.action"
              >
                <span :class="item.icon" />
                <span class="ml-2">{{ $t(item.label) }}</span>
              </a>
            </template>
            <template #end>
              <button
                v-ripple
                class="flex alifn-center relative overflow-hidden w-full border-0 bg-transparent flex items-start p-2 pl-4 hover:bg-surface-100 dark:hover:bg-surface-800 rounded-none cursor-pointer transition-colors duration-200"
              >
                <Avatar
                  :image="user.img_url"
                  :icon="user && !user.img_url ? 'pi pi-user' : ''"
                  class="mr-2 mt-1"
                  size="normal"
                  shape="circle"
                />
                <span class="inline-flex flex-col items-start">
                  <span class="font-bold">{{ user.full_name }}</span>
                  <span class="text-sm">{{ user?.role?.name }}</span>
                </span>
              </button>
            </template>
          </Menu>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import Popover from "primevue/popover";

export default {
  data() {
    return {
      notifications: [],
    };
  },
  computed: {
    ...mapGetters({
      user: "auth/user",
      siteLogo: "auth/siteLogo",
    }),
    dynamicSiteLogo() {
      return this.siteLogo ? this.siteLogo : "https://minio.beta.mylms.no/betalms/siteLogo/images/812-300.png";
    },
  },
  methods: {
    toggleNotification(event) {
      this.$refs.popoverNotification.toggle(event);
    },
  },
};
</script>

<style lang="scss" scoped>
.mobile-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px;
  width: 100%;
}

.mobile-page-title {
  font-size: 20px;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.mobile-menu-button {
  background: none;
  border: none;
  padding: 8px;
  cursor: pointer;
  color: #374151;
  transition: color 0.2s ease;
}

.mobile-menu-button:hover {
  color: #111827;
}

/* Responsive adjustments */
@media (max-width: 991px) {
  .mobile-header {
    display: flex;
  }
}
</style>
