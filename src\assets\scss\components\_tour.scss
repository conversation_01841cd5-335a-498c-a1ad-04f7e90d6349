.tour-preview {
    width: 390px!important;
    height: 520px!important;
    z-index: 9998!important;
    background-color: white!important;
    position: fixed!important;
    .scale{
      position: absolute!important;
      right: 68.1px!important;
      top: 28px!important;
    }
    .close{
      right: 30.1px!important;
      opacity: 1!important;
      top: 28px!important;
    }
    .close:hover{
      opacity: .5!important;
    }
    .teaser{
      background-color: white!important;
      height: 209px!important;
      border-bottom: 2px solid #333333 ;
      img {
        height: 206px!important;
        width: 346px!important;
      }
    }
    .content {
      padding: 10px 22px 22px 22px!important;
      background-color: white!important;
      height: 312px!important;
      .title {
        color: #333333 !important;
        width: 305px;
        padding-right: 10px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .description {
        color: black!important;
        font-size: 14px!important;
        line-height: 20px!important;
      }
      .footer {
        margin-bottom: 1em;
        .footer-btn {
          background-color: #333333 !important;
          color: white!important;
          text-transform: uppercase!important;
          padding: 10px 10px 10px 10px!important;
        }
        .footer-link {
          color: black!important;
        }
        .footer-dots .dot {
          width: 6px!important;
          height: 6.5px!important;
        }
      }
    }
    .related-topic-text {
      margin-top: 10px;
      color: #FFFF;
        &:hover {
          color: #DB2677;	
        }
    }
    .footer-bg {
      background-color: #333333 ; width:100%; height: 92px;
      left: 0;
    }
    .hr-section {
      margin-right: 8px;
      border-left: 2px solid white; height: 20px; margin-top:2px;
    }
    .bg-button-close{
      margin-top: 1.35em;
      margin-right: 1.409em;
    }
    .bg-button-expand{
        margin-top: 1.35em;
        margin-right: 3.8em!important;
    }
    .wrapper-icon-restart {
      width: 20px;
      .icon-restart{
        margin-left: 1.3em;
      }
    }
  }
  
  .tour-preview.is-scaled {
	width: 710px!important;
	height: 689px!important;
	.teaser{
		height: 424px!important;
		img {
			height: 424px!important;
			width: 710px!important;
			object-fit: fill;
		}
	}
	.content {
		background-color: white!important;
		height: 359px!important;
		padding: 10px 40px 40px 40px!important;
		.title {
			color: #333333 !important;
      padding-right: 10px;
			width: 670px;
      overflow: inherit;
			text-overflow: clip;
      white-space: normal;
		}
		.description {
			line-height: 23px!important;
			font-size: 16px!important;
		}
		.footer{
			margin-top: auto - 100px!important;
			margin-bottom: 3em;
			.footer-dots .dot {
				width: 10px!important;
				height: 10.5px!important;
			}
		}
	}
	.footer-bg {
    margin-top: 1px;
		background-color: #333333 ; width:100%; height: 110px;
	}
	.related-topic-scaled {
		margin-left: 42px!important;
	}
	.related-topic-font-scaled {
		margin-top: 2px!important;
		margin-bottom: 0.3px!important;
		font-size: 15px!important;
	}
	.wrapper-icon-restart {
		width: 17px;
		.icon-restart{
			margin-left: 2.6em;
		}
	}
}

.tour-preview .content .footer .footer-dots .dot {
  margin-right: 4px!important;
}

.flex-related {
	display: flex;
	flex-wrap: wrap;
}

.related-topics {
	height: 70.25px!important;
}

.line-break-related {
	width: 100%;
}