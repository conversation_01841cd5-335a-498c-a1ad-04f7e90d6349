.very-weak {
	color: #FF4500;
}

.weak {
	color: orange;
}

.strong {
	color: #9ACD32;
}

.very-strong {
	color: #008000;
}

.pointer {
	cursor: pointer;
}

.text-light-gray {
	color: #d5dae4;
}

::-webkit-scrollbar-track
{
	-webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3);
	background-color: #F2F2F2;
	border-radius: 10px;
}

::-webkit-scrollbar
{
	width: 6px;
	height: 6px;
	background-color: #F2F2F2;
	border-radius: 10px;
}

::-webkit-scrollbar-thumb
{
	background-color: #9FA6B2;
	border-radius: 10px;
}

.scrollable-container-cropper {
	overflow: auto;
	scrollbar-width: thin; /* For Firefox */
	scrollbar-color: transparent transparent; /* For Firefox */
}

/* For WebKit browsers (Chrome, Safari) */
.scrollable-container-cropper::-webkit-scrollbar {
	width: 6px;
}

.scrollable-container-cropper::-webkit-scrollbar-track {
	background-color: transparent;
	-webkit-box-shadow: none;
}

.scrollable-container-cropper::-webkit-scrollbar-thumb {
	background-color: transparent;
}

figure {
	margin: 0;
	display: grid;
	grid-template-rows: 1fr auto;
	margin-bottom: 10px;
	break-inside: avoid;
  }
  
  figure > img {
	grid-row: 1 / -1;
	grid-column: 1;
  }
  
  figure a {
	color: black;
	text-decoration: none;
  }
  
  figcaption {
	grid-row: 2;
	grid-column: 1;
	background-color: rgba(255,255,255,.5);
	padding: .2em .5em;
  }

  .c-text {
	cursor: text;
}


.CodeMirror-hscrollbar {
	height: 8px;
}

.disabled{
	pointer-events: none;
	background-color: #F4F4F4;
}

.disabled-input{
	pointer-events: none;
	background-color: #F4F4F4;
  color: #999;
}

.disabled-pointer{
	pointer-events: none;
}

.default-cursor {
	cursor: default;
}

//   new evolution using ratio on css hehehehehehehe hayuuk
.section-gallery {
	display: grid;
	gap: 20px;
	grid-template-columns: repeat(auto-fill, minmax(100px, 1fr)); /* Play with min-value */
  }
  
  .img-gallery {
	background-color: gainsboro; /* To visualize empty space */
	aspect-ratio: 16/9; 
	/*
	  "contain" to see full original image with eventual empty space
	  "cover" to fill empty space with truncating
	  "fill" to stretch
	*/
	object-fit: contain;
	width: 100%;
  }

.video-wrapper {
	position: relative;
	height: calc(100vh - 140px);
	overflow: hidden;
  }
  
  .video-wrapper video {
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	
  }

.rounded-template-selected {
	border-radius: 0.5rem;
	border-style: solid;
}

.rounded-template {
	border-radius: 0.5rem;
	border-style: solid;
	border-width: 0.5px;
}

.form-checkbox {
	/* Set the color for the unchecked checkbox's background */
	background-color: rgba(128, 128, 128, 0.221); /* Replace #fff with your desired background color */
	/* Add some padding for better spacing (optional) */
	padding: 2px;
	/* Optional: Remove default browser styles */
	appearance: none;
	-webkit-appearance: none;
	-moz-appearance: none;
  }

  
.mx-input {
  min-height: 42px!important;
  border-radius: 6px!important;
}

.icon-play-global {
  width: 40px;
  height: 40px;
  transition: transform 0.3s ease, color 0.3s ease; /* Smooth transitions */
}

.icon-play-global:hover {
  transform: scale(1.3); /* 10% increase in size */
}

/* 1600px and up */
@media (min-width: 1600px) {
  .icon-play-global {
    width: 7rem;
    height: 7rem;
  }
}

/* 1300px to 1599px */
@media (min-width: 1300px) and (max-width: 1599px) {
  .icon-play-global {
    width: 5rem;
    height: 5rem;
  }
}

/* 1024px to 1299px */
@media (min-width: 1024px) and (max-width: 1299px) {
  .icon-play-global {
    width: 3rem;
    height: 3rem;
  }
}

/* 768px to 1023px */
@media (min-width: 768px) and (max-width: 1023px) {
  .icon-play-global {
    width: 10rem;
    height: 10rem;
  }
}

/* 450px to 768px */
@media (min-width: 450px) and (max-width: 768px) {
  .icon-play-global {
    width: 10rem;
    height: 10rem;
  }
}

/* 449px and down */
@media (max-width: 449px) {
  .icon-play-global {
    width: 4rem;
    height: 4rem;
  }
}

 /* The outer container stays in the grid flow with fixed dimensions */
 .card-container {
  position: relative;
  overflow: visible; /* allow scaled card to overlap without shifting layout */
  /* Set a fixed height or aspect ratio so all grid items stay consistent */
  /* For example, using a fixed height: */
  height: 0;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
  margin-bottom: 60px;
}

/* The inner card is absolutely positioned and covers the container */
.tilt-card {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  transition: transform 0.1s ease-in-out;
  transform-origin: center;
}

/* On hover, scale only the inner content */
.card-container:hover .tilt-card {
  transform: scale(1.01);
  z-index: 10; /* keep it above neighboring items */
}

.border-hover-card:hover {
  border: 1px solid #8c96AD; /* Thinner border */
  border-radius: 16px;
  box-shadow: 0 0 0 2px #E8EDf0;
}

.truncate-lines {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  &.lines-1 {
    -webkit-line-clamp: 1;
  }
  &.lines-2 {
    -webkit-line-clamp: 2;
  }
  &.lines-3 {
    -webkit-line-clamp: 3;
  }
  &.lines-4 {
    -webkit-line-clamp: 4;
  }
  &.lines-5 {
    -webkit-line-clamp: 5;
  }
}

/* The outer container stays in the grid flow with fixed dimensions */
.card-container-without-padding-bottom {
  position: relative;
  overflow: visible; /* allow scaled card to overlap without shifting layout */
  /* Set a fixed height or aspect ratio so all grid items stay consistent */
  /* For example, using a fixed height: */
  height: 0;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
}

/* The inner card is absolutely positioned and covers the container */
.tilt-card {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  transition: transform 0.1s ease-in-out;
  transform-origin: center;
}

/* On hover, scale only the inner content */
.card-container-without-padding-bottom:hover .tilt-card {
  transform: scale(1.01);
  z-index: 10; /* keep it above neighboring items */
}