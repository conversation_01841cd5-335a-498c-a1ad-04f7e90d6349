html {
    font-size: 14px;
}


body {
    font-family: 'Man<PERSON><PERSON>', sans-serif;
    // font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
    color: var(--text-color);
    background-color: var(--surface-ground);
    margin: 0;
    padding: 0;
    min-height: 100%;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

a {
    text-decoration: none;
}

.layout-wrapper {
    min-height: 100vh;
}

.p-multiselect {
  padding: 0!important;
}

.p-multiselect-filter {
  width: calc(100% - 28px)!important;
  margin-left: 28px;
}

.p-paginator {
  padding: 0px!important;
  padding-left: 10px!important;
}

.global-breadcumb.p-breadcrumb {
  background-color: transparent!important;
  padding: 0px 10px!important;
}

.p-button-label {
  font-weight: 400!important;
}

.p-datatable-header {
  border: none!important;
  margin-bottom: 12px;
}

.p-dialog-header-actions {
  margin-right: -9px!important;
}

.p-dialog-header-actions .p-icon {
  width: 20px !important; /* Adjust the width */
  height: auto; /* Maintain aspect ratio */
  color: black!important;
}


.p-dialog {
  border-radius: 8px!important;
}

/* Media query for mobile devices (example: max-width: 768px) */
@media (max-width: 768px) {
  .p-dialog {
    max-height: 99vh!important; /* Adjust max-height for mobile */
    margin-left: 5px!important;
    margin-right: 5px!important;
  }
  .p-dialog-content {
    padding-left: 15px!important;
    padding-right: 15px!important;
  }
}


.p-dialog-title {
  font-size: 16px!important;
}

.p-dialog-header {
  padding-bottom: 10px!important;
}

.p-chip {
  &.active {
    color: white!important;
    background-color: #3B82F6!important;
  }
}

.p-chip-remove-icon {
}