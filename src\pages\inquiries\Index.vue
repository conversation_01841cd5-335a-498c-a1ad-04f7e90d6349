<script setup>
import { ref } from 'vue';

const inquiries = ref([
  {
    id: 1,
    title: 'Technical Support Request',
    status: 'pending',
    date: '2024-01-15',
    priority: 'high'
  },
  {
    id: 2,
    title: 'Account Access Issue',
    status: 'in-progress',
    date: '2024-01-14',
    priority: 'medium'
  },
  {
    id: 3,
    title: 'Feature Request',
    status: 'resolved',
    date: '2024-01-13',
    priority: 'low'
  },
  {
    id: 4,
    title: 'Billing Question',
    status: 'pending',
    date: '2024-01-12',
    priority: 'medium'
  }
]);

const getStatusColor = (status) => {
  switch (status) {
    case 'pending': return '#f59e0b';
    case 'in-progress': return '#3b82f6';
    case 'resolved': return '#10b981';
    default: return '#6b7280';
  }
};

const getPriorityColor = (priority) => {
  switch (priority) {
    case 'high': return '#ef4444';
    case 'medium': return '#f59e0b';
    case 'low': return '#10b981';
    default: return '#6b7280';
  }
};

const navigateToDetail = (inquiry) => {
  console.log('Navigate to inquiry detail:', inquiry);
};
</script>

<template>
  <div class="inquiries-page">
    <div class="inquiries-container">
      <div 
        v-for="inquiry in inquiries" 
        :key="inquiry.id"
        class="inquiry-item"
        @click="navigateToDetail(inquiry)"
      >
        <div class="inquiry-item-content">
          <div class="inquiry-item-header">
            <h3 class="inquiry-item-title">{{ inquiry.title }}</h3>
            <div class="inquiry-item-badges">
              <span 
                class="inquiry-status-badge"
                :style="{ backgroundColor: getStatusColor(inquiry.status) }"
              >
                {{ inquiry.status }}
              </span>
            </div>
          </div>
          <div class="inquiry-item-meta">
            <span 
              class="inquiry-priority"
              :style="{ color: getPriorityColor(inquiry.priority) }"
            >
              {{ inquiry.priority }} priority
            </span>
            <span class="inquiry-date">{{ inquiry.date }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.inquiries-page {
  padding: 20px 16px;
  padding-bottom: 80px; /* Space for bottom navigation */
  min-height: 100vh;
  background-color: #f9fafb;
}

.inquiries-container {
  max-width: 100%;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.inquiry-item {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 100px;
}

.inquiry-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.inquiry-item-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.inquiry-item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.inquiry-item-title {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin: 0;
  line-height: 1.4;
  flex: 1;
  margin-right: 12px;
}

.inquiry-item-badges {
  display: flex;
  gap: 8px;
}

.inquiry-status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  color: white;
  text-transform: capitalize;
}

.inquiry-item-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  margin-top: auto;
}

.inquiry-priority {
  font-weight: 500;
  text-transform: capitalize;
}

.inquiry-date {
  color: #9ca3af;
  font-weight: 400;
}

/* Desktop styles */
@media (min-width: 992px) {
  .inquiries-page {
    padding: 40px;
    padding-bottom: 40px;
  }
  
  .inquiries-container {
    max-width: 800px;
    gap: 20px;
  }
  
  .inquiry-item {
    padding: 24px;
    min-height: 120px;
  }
  
  .inquiry-item-title {
    font-size: 18px;
  }
}
</style>
