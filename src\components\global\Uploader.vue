<template>
  <div v-if="preview" class="w-full items-center justify-center text-center" :class="{ 'opacity-60 pointer-events-none bg-gray-200': disabled }">
    <div
      class="mt-1 border-2 border-gray-300 border-dashed rounded-md px-6 flex justify-center min-h-[100px]"
      :class="{ 'border-gray-400 bg-gray-100': disabled }"
      @dragover="dragover"
      @dragleave="dragleave"
      @drop="drop"
    >
      <div class="space-y-1 text-center mt-[4%]">
        <div v-if="isShowThumbnail">
          <!-- preview image purpose -->
          <img
            v-show="filelist.length > 0 && type !== 'excel' && !existingPreviewImageOrVideo
            "
            :id="`previewUpload${id}`"
            class="mx-auto object-cover max-h-[100px] max-w-[200px]"
            src="#"
            alt="LMS"
          >
          <div
            v-if="
              (existingPreviewImageOrVideo && type === 'image') ||
                (existingPreviewImageOrVideo &&
                  getFileType(existingPreviewImageOrVideo) === 'image')
            "
          >
            <img
              v-show="
                existingPreviewImageOrVideo && filelist.length == 0 && type !== 'excel'
              "
              :id="`existingPreviewImageOrVideo${id}`"
              class="mx-auto object-cover max-h-[100px] max-w-[200px]"
              :src="existingPreviewImageOrVideo"
              alt="LMS"
            >
            <img
              v-show="
                existingPreviewImageOrVideo && filelist.length == 1 && type !== 'excel'
              "
              :id="`existingPreviewImageOrVideo${id}`"
              class="mx-auto object-cover max-h-[100px] max-w-[200px]"
              :src="existingPreviewImageOrVideo"
              alt="LMS"
            >
          </div>
          <!-- video -->
          <div
            v-if="
              (existingPreviewImageOrVideo && type === 'video') ||
                (existingPreviewImageOrVideo && customFileTypes.includes('video')) ||
                (existingPreviewImageOrVideo &&
                  getFileType(existingPreviewImageOrVideo) === 'video')
            "
            class="pointer"
            @click="!disabled && (isShowPreviewModalVideo = true)"
          >
            <video-player
              :src="existingPreviewImageOrVideo"
              class="shadow-md rounded-xl h-[160px] w-[300px]"
              @contextmenu.prevent
            />
          </div>
        </div>

        <!-- svg logo -->
        <svg
          v-show="
            titlePlaceHolder === 'Video' &&
              ((type !== 'image' && !existingPreviewImageOrVideo) ||
                (filelist.length == 0 && type === 'image' && !existingPreviewImageOrVideo))
          "
          class="mx-auto h-8 w-8 text-gray-400"
          :class="{ 'text-gray-500': disabled }"
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z" />
          <path d="M14 2v4a2 2 0 0 0 2 2h4" />
          <path d="m10 11 5 3-5 3v-6Z" />
        </svg>
        <svg
          v-show="
            titlePlaceHolder === 'Image' &&
              ((type !== 'image' && !existingPreviewImageOrVideo) ||
                (filelist.length == 0 && type === 'image' && !existingPreviewImageOrVideo))
          "
          class="mx-auto h-8 w-8 text-gray-400"
          :class="{ 'text-gray-500': disabled }"
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <path d="M16 5h6" />
          <path d="M19 2v6" />
          <path d="M21 11.5V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h7.5" />
          <path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21" />
          <circle cx="9" cy="9" r="2" />
        </svg>
        <svg
          v-show="
            titlePlaceHolder !== 'Video' &&
              titlePlaceHolder !== 'Image' &&
              ((type !== 'image' && !existingPreviewImageOrVideo) ||
                (filelist.length == 0 && type === 'image' && !existingPreviewImageOrVideo))
          "
          class="mx-auto h-8 w-8 text-gray-400"
          :class="{ 'text-gray-500': disabled }"
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z" />
          <path d="M14 2v4a2 2 0 0 0 2 2h4" />
        </svg>

        <div class="flex text-sm text-gray-600" :class="{ 'text-gray-500': disabled }">
          <label
            :for="disabled ? '' : id"
            class="relative rounded-md font-medium focus-within:outline-none"
            :class="disabled ? 'cursor-not-allowed text-gray-500' : 'cursor-pointer text-primary-600 hover:text-primary-500'"
          >
            <span
              v-if="
                (filelist.length === 0 && !existingPreviewImageOrVideo) ||
                  (type === 'excel' && !existingPreviewImageOrVideo) ||
                  multiple
              "
              :for="disabled ? '' : id"
            >{{ `${$t("Upload")} ${titlePlaceHolder ? $t(titlePlaceHolder) : $t('a')} ${$t("file")}` }}</span>
            <input
              :id="id"
              ref="file"
              :accept="acceptedFiles"
              :multiple="multiple"
              :name="id"
              type="file"
              class="sr-only"
              :disabled="disabled"
              @change="onChange"
            >
          </label>
          <p
            v-if="
              (filelist.length === 0 && !existingPreviewImageOrVideo) ||
                (type === 'excel' && !existingPreviewImageOrVideo) ||
                multiple
            "
            class="pl-1"
          >
            {{ $t("or drag and drop here") }}
          </p>
          <!-- display name -->
          <ul
            v-if="filelist.length && multiple && type !== 'excel' && isShowThumbnail"
            v-cloak
            class="mt-4"
          >
            <li v-for="(file, index) in filelist" :key="index" class="text-sm p-1">
              {{ file.name }}
              <button
                class="ml-2"
                :class="disabled ? 'text-gray-400 cursor-not-allowed' : 'text-red-400 cursor-pointer'"
                type="button"
                title="Remove file"
                :disabled="disabled"
                @click="!disabled && remove(filelist.indexOf(file))"
              >
                {{ $t("remove") }}
              </button>
            </li>
          </ul>
          <div
            v-if="
              ((filelist.length && !multiple && type !== 'excel') ||
                (existingPreviewImageOrVideo && !multiple && type !== 'excel')) &&
                isShowThumbnail
            "
            :key="index"
            class="text-sm p-1 "
          >
            <div class="truncate-lines lines-1">{{ filelist[0]?.name || existingPreviewImageOrVideo }}</div>
            <button
              class="ml-2"
              :class="disabled ? 'text-gray-400 cursor-not-allowed' : 'text-red-400 cursor-pointer'"
              type="button"
              title="Remove file"
              :disabled="disabled"
              @click="!disabled && remove(filelist.indexOf(file))"
            >
              {{ $t("remove") }}
            </button>
          </div>
        </div>
        <p class="text-xs text-gray-500">
          <!-- PNG, JPG, GIF up to 10MB -->
        </p>
      </div>
    </div>
    <div v-if="progressUploadFile > 0 && progressUploadFile <= 100">
      <ProgressBar
        :value="progressUploadFile"
      />
      <div v-html="informationCounts" />
    </div>  
  </div>
  <div v-else :class="{ 'opacity-60 pointer-events-none': disabled }">
    <label
      :for="disabled ? '' : id"
      class="h-[36px] w-40 p-2 px-6 relative rounded-md font-medium focus-within:outline-none"
      :class="disabled ? 'bg-gray-400 cursor-not-allowed' : 'bg-[#EEEEEE] cursor-pointer'"
    >
      <span v-if="filelist.length === 0 && type !== 'excel'" :for="id">{{ title }}</span>
      <input
        :id="id"
        ref="file"
        :accept="acceptedFiles"
        :multiple="multiple"
        :name="id"
        type="file"
        class="sr-only"
        :disabled="disabled"
        @change="onChange"
      >
    </label>
  </div>
</template>


<script>
import TButton from "@/components/global/Button.vue";
import ProgressBar from "primevue/progressbar";
import { VideoPlayer } from "@videojs-player/vue";
import "video.js/dist/video-js.css";

export default {
  name: "DropArea",
  components: {
    ProgressBar,
    VideoPlayer,
  },
  props: {
    id: {
      type: String,
      default: "fileUploadInput",
    },
    role: {
      type: String,
      default: "admin",
    },
    disabled: {
      type: Boolean,
      default: false
    },
    multiple: {
      type: Boolean,
      default: true,
    },
    maxSize: {
      type: Number,
      default: 100,
    },
    type: {
      type: String,
      default: "",
    },
    preview: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: "Upload",
    },
    progressUploadFile: {
      type: Number,
      default: 0,
    },
    existingPreviewImageOrVideo: {
      type: String,
      default: "",
    },
    customFileTypes: {
      type: Array,
      default: () => [],
    },
    titlePlaceHolder: {
      type: String,
      default: () => '',
    },
    isShowThumbnail: {
      type: Boolean,
      default: true,
    },
    informationCounts: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      isDraggedOver: false,
      isShowPreviewModalVideo: false,
      filelist: [],
      fileTypes: {
        document: [
          "application/pdf",
          "application/msword",
          "application/vnd.openxmlformats-officedocument.wordprocessingml.document", // .docx
          "application/vnd.ms-excel",
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", // .xlsx
          "text/plain", // .txt
        ],
        pdf: [".pdf"],
        video: [
          "video/mp4",
          "video/quicktime",
          "video/x-msvideo", // .avi
          "video/x-ms-wmv", // .wmv
          "video/mpeg", // .mpeg
          "video/webm", // .webm
          "video/*", // Fallback for any video type
        ],
        image: [
          "image/png",
          "image/jpg",
          "image/jpeg",
          "image/gif",
          "image/bmp",
          "image/svg+xml",
          "image/webp",
          "image/*", // Fallback for any image type
        ],
        excel: [
          "application/vnd.ms-excel",
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", // .xlsx
          ".xls",
          ".xlsx",
        ],
        audio: [
          "audio/mpeg", // .mp3
          "audio/ogg", // .ogg
          "audio/wav", // .wav
          "audio/*", // Fallback for any audio type
        ],
        archive: [
          "application/zip", // .zip
          "application/x-rar-compressed", // .rar
          "application/x-7z-compressed", // .7z
          "application/gzip", // .gz
          "application/x-tar", // .tar
          "application/x-bzip", // .bz
          "application/x-bzip2", // .bz2
          "application/x-zip-compressed",
        ],
      },
    };
  },
  computed: {
    acceptedFiles() {
      if (this.customFileTypes.length > 1) {
        const types = this.customFileTypes.split(",");
        const accepted = [];

        types.forEach((type) => {
          if (this.fileTypes[type.trim()]) {
            accepted.push(...this.fileTypes[type.trim()]);
          }
        });
        return accepted.join(",");
      } else {
        return this.fileTypes[this.type] ? this.fileTypes[this.type].join(",") : "";
      }
    },
  },
  methods: {
    getFileType(url) {
      const fileExtension = url.split(".").pop().toLowerCase();
      for (const [type, extensions] of Object.entries(this.fileTypes)) {
        if (
          extensions.some(
            (ext) =>
              ext.toLowerCase() === `.${fileExtension}` || ext.includes(fileExtension)
          )
        ) {
          return type; // Return the type if a match is found
        }
      }

      return "unknown"; // Return 'unknown' if no match is found
    },
    onChange() {
      this.filelist = [...this.$refs.file.files];
      if (this.filelist[0] && this.filelist[0].type.includes("image")) {
        const previewElementId = `previewUpload${this.id}`; // Construct the dynamic ID
        const previewElement = document.getElementById(previewElementId); // Get the DOM element by ID

        if (previewElement) {
          previewElement.src = URL.createObjectURL(this.filelist[0]); // Set the src dynamically
        } else {
          console.error(`Element with ID '${previewElementId}' not found.`);
        }
        this.$emit("upload", this.filelist);
      } else {
        this.$emit("upload", this.filelist);
      }
    },
    remove(i) {
      this.filelist.splice(i, 1);
      if (document.querySelector(`#${this.id}`))
        document.querySelector(`#${this.id}`).value = "";
      this.$emit("remove");
    },
    dragover(event) {
      if (this.disabled) return;
      event.preventDefault();
      // Add some visual fluff to show the user can drop its files
      if (!event.currentTarget.classList.contains("bg-primary-300")) {
        event.currentTarget.classList.remove("bg-gray-100");
        event.currentTarget.classList.add("bg-primary-300");
      }
    },
    dragleave(event) {
      if (this.disabled) return;
      // Clean up
      event.currentTarget.classList.add("bg-gray-100");
      event.currentTarget.classList.remove("bg-primary-300");
    },
    drop(event) {
      if (this.disabled) return;
      event.preventDefault();
      const files = event.dataTransfer.files;
      if (files.length === 0) {
        event.currentTarget.classList.add("bg-gray-100");
        event.currentTarget.classList.remove("bg-primary-300");
        return;
      }

      this.$refs.file.files = files;
      this.onChange(); // Trigger the onChange event manually
      // Clean up
      event.currentTarget.classList.add("bg-gray-100");
      event.currentTarget.classList.remove("bg-primary-300");
    },
  },
};
</script>
