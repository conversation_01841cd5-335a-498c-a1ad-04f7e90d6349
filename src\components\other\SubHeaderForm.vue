<template>
  <div v-if="!isEdit" class="flex items-center" :class="{'italic text-gray-400': !localText, 'text-gray-600': localText, 'pointer': ['super_admin', 'admin', 'manager'].includes(userRole.right)}" @click="updateDescription()">
    {{ !localText && ['super_admin', 'admin', 'manager'].includes(userRole.right) ? $t('Add description here') : localText }}
    <i v-if=" ['super_admin', 'admin', 'manager'].includes(userRole.right) && localText" style="font-size: 12px;" class="ml-2 pointer pi pi-pencil text-primary-600" />
  </div>
  <div v-if="isEdit && ['super_admin', 'admin', 'manager'].includes(userRole.right)" class="flex items-center  mt-2">
    <InputText
      v-model="localText"
      :type="`text`"
      :value="localText"
      class="md:w-[420px] w-full"
      @blur="updateSettings()"
      @keyup.enter="updateSettings()"
    />
    <Button
      icon="pi pi-save"
      size="medium"
      class="ml-2"
      @click="updateSettings()"
    />
  </div>
</template>

<script>
import settingApi from "@/api/setting";
import { onClickOutside } from '@vueuse/core';
import { mapGetters } from "vuex";

/* eslint-disable vue/html-closing-bracket-spacing */
export default {
  components: {},
  props: {
    subText: {
      type: String,
      default: () => '',
    },
    type: {
      type: String,
      default: () => '',
    },
  },
  data() {
    return {
      localText: '',
      isEdit: false,
      getCurrentType: null,
      objectMenuDescription: null
    };
  },
  computed: {
    ...mapGetters({
      userRole: "auth/userRole",
    }),
  },
  watch: {},
  created() {},
  mounted() {
    let menuDescription  = '';
    menuDescription = localStorage.getItem("menuDescription");
    if (menuDescription) {
      this.objectMenuDescription = JSON.parse(menuDescription);
      this.getCurrentType = this.objectMenuDescription.find(item => item.key === this.type);
      this.localText = this.getCurrentType?.value?.includes('This is description for') ? '' : this.getCurrentType?.value;
    }
  },
  beforeUnmount() {},
  methods: {
    getSettingValue(settings, key) {
      const item = settings.find((setting) => setting.key === key);
      return item ? item.value : null;
    },
    updateDescription() {
      if (["super_admin", "admin", "manager"].includes(this.userRole.right)) {
        this.isEdit = true;
      }
    },
    updateSettings() {
      const index = this.objectMenuDescription.findIndex(item => item.key === this.type);
      this.objectMenuDescription[index].value = this.localText;
      const params = {
        type: 'json',
        value: JSON.stringify(this.objectMenuDescription)
      };
      const callback = (response) => {
        this.isSaving = false;
        const message = response.message;
        if (this.objectMenuDescription) localStorage.setItem("menuDescription", JSON.stringify(this.objectMenuDescription));
        this.isEdit = false;
        this.__showNotif("success", "message", message);
      };
      const errorCallback = (error) => {
        const message = error.response.data.message;
        this.__showNotif("error", this.$t('Error'), message);
        this.isSaving = false;
      };
      settingApi.update('menu_description', params, callback, errorCallback);
    },
  },
};
</script>
