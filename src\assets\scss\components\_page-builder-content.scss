.page-builder-content {

    :root {
        --bg-color: #f7fafc;
        --fg-color: #000000;


        --font: "Rubik";
        --spacing: "-0.04em";
    }
    background-color: var(--bg-color);
    .container {
        padding-top: 20px;
        padding-bottom: 20px;
    }

    section h1,
    section h2,
    section h3,
    section h4 {
        font-family: var(--font);
        letter-spacing: var(--spacing);
        font-weight: 600;
    }

    section h1,
    section h2 {
        font-size: 45px;
        line-height: 60px;
    }

    section h3 {
        margin-top: 20px;
        font-size: 35px;
        line-height: 55px;
    }

    section h1 {
        margin-bottom: 10px;
    }

    section h2 {
        margin-bottom: 25px;
    }

    section p {
        font-family: "Rubik", sans-serif;
        font-weight: 300;
        font-size: 20px;
        line-height: 50px;
        color: #4f5c65;
    }

    section {
        padding: 25px;
        padding-top: 100px;
        padding-bottom: 100px;
    }



    

    // section:nth-child(even) {
    //     background-color: var(--bg-color)!important;
    //     color: var(--fg-color)!important;
    // }
    
    // #page:nth-child(odd) {
    //     color: var(--fg-color);
    //     border-color: var(--fg-color);
    // }



    .btn-secondary {
        font-family: "Rubik", sans-serif;
        border: 1px solid #000;
        font-size: 20px;
        padding: 8px 25px 8px 25px;
        margin-top: 25px;
        background-color: transparent;
    }

    .btn-secondary:hover {
        border: 1px solid #999;
        background-color: transparent;
    }


    .fa-big {
        font-size: 45px;
        margin-bottom: 15px;
    }

    .responsive-container {
        position: relative;
        overflow: hidden;
        width: 100%;
        padding-top: 56.25%;
        /* 16:9 Aspect Ratio (divide 9 by 16 = 0.5625) */
    }

    /* Then style the iframe to fit in the container div with full height and width */

    .responsive-iframe {
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
        width: 100%;
        height: 100%;
    }

    .edit {
        display: block;
        width: 50px;
        height: 50px;
        background-image: url(img/cog.png);
        background-size: cover;
        position: absolute;
        top: 25px;
        right: 25px;
        cursor: pointer;
    }

    .editor {
        overflow-y: auto;
    }

    #adder,
    #designer,
    #saver {
        position: fixed;
        top: 0;
        width: 400px;
        height: 100%;
        background-color: white;
        border-left: 1px solid #ddd;
        box-shadow: 0px 3px 15px rgba(0, 0, 0, 0.2);
        overflow-y: auto;
    }

    .editable>.container {
        border: 1px solid transparent;
    }

    .editable>.container:hover {
        // border: 1px dashed #333;
        cursor: pointer;
    }

    .fade-enter-active,
    .fade-leave-active {
        transition: opacity 0.5s ease;
    }

    .fade-enter-from,
    .fade-leave-to {
        opacity: 0;
    }

    .slidein-right {
        width: 400px;
        position: fixed;
        z-index: 9999;
        top: 0;
        height: 100%;
        background-color: white;
        box-shadow: 1px 1px 10px rgba(0, 0, 0, 0.5);
        right: 0px;
    }

    .slide-right-enter-active,
    .slide-right-leave-active {
        transition: 0.5s ease;
    }

    .slide-right-enter-from,
    .slide-right-leave-to {
        right: -400px;
    }

    .slidein-left {
        left: 0;
    }

    .slide-left-enter-active,
    .slide-left-leave-active {
        transition: 0.5s ease;
    }

    .slide-left-enter-from,
    .slide-left-leave-to {
        left: -300px;
    }

    .editor textarea {
        min-height: 200px;
        resize: none;
    }

    .editor-header {
        padding-bottom: 5px;
        background-color: #f8f8f8;
        padding: 10px;
        padding-left: 20px;
        padding-right: 20px;
        height: 50px;
        border-bottom: 1px solid #dddddd;
        position: fixed;
        z-index: 99;
    }

    .slidein-left .editor-header {
        width: 300px;
    }

    .slidein-right .editor-header {
        width: 400px;
    }

    .editor-header h4 {
        font-size: 16px;
        padding-top: 5px;
    }

    .close:hover {
        cursor: pointer;
    }

    .editor-content {
        padding: 20px;
        padding-top: 50px;
    }

    .editor-content:first-child {
        margin-top: 0;
    }

    .label {
        text-transform: uppercase;
        letter-spacing: 0.03em;
        font-size: 14px;
        margin-bottom: 5px;
        margin-top: 20px;
        color: #212529;
    }

    .save {
        margin-top: 30px;
    }

    .box {
        display: block;
        border: 1px solid #ddd;
        margin-bottom: 10px;
    }

    .box:hover {
        background-color: #f8f8f8;
        cursor: pointer;
        text-decoration: none;
    }

    #dock {
        position: fixed;
        bottom: 15px;
        text-align: center;
        width: 200px;
        left: calc(50% - 90px);
    }

    #dock img {
        width: 60px;
        margin: 5px;
    }

    .grow {
        transition: all 0.2s ease-in-out;
    }

    .grow:hover {
        transform: scale(1.1);
        cursor: pointer;
    }

    #editor .btn-outline-danger {
        border: 1px solid #6c757d;
        color: #6c757d;
    }

    #editor .btn-primary {
        position: fixed;
        right: 15px;
        bottom: 15px;
        width: 370px;
    }

    .swatch {
        display: inline-block;
        vertical-align: top;
        width: 32px;
        height: 32px;
        margin-right: 11px;
        margin-bottom: 6px;
        border-radius: 6px;
        cursor: pointer;
        text-align: center;
        border: 1px solid #ddd;
    }

    .fa-center {
        margin-top: 8px;
    }

    .w-100px {
        width: 100px;
    }

    a.list-group-item {
        color: black;
    }

    a.list-group-item:hover {
        background-color: #f8f8f8;
        text-decoration: none;
        cursor: pointer;
    }

    .info {
        padding-top: 5px;
        color: #777;
    }
}