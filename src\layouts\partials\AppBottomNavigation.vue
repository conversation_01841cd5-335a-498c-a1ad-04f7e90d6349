<script setup>
import { computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';

const route = useRoute();
const router = useRouter();

const navigationItems = [
  {
    name: 'News Feed',
    icon: 'pi pi-list',
    route: '/news-feed',
    routeName: 'NewsFeed'
  },
  {
    name: 'Inquiries',
    icon: 'pi pi-check-square',
    route: '/inquiries',
    routeName: 'Inquiries'
  },
  {
    name: 'New Inquiry',
    icon: 'pi pi-plus-circle',
    route: '/new-inquiry',
    routeName: 'NewInquiry'
  },
  {
    name: 'Chat',
    icon: 'pi pi-comments',
    route: '/chat',
    routeName: 'Chat'
  },
  {
    name: 'Profile',
    icon: 'pi pi-user',
    route: '/profile',
    routeName: 'Profile'
  }
];

const isActive = (item) => {
  return route.name === item.routeName;
};

const navigateTo = (item) => {
  router.push(item.route);
};
</script>

<template>
  <div class="mobile-bottom-nav">
    <div class="mobile-bottom-nav-container">
      <button
        v-for="item in navigationItems"
        :key="item.name"
        :class="[
          'mobile-nav-item',
          { 'mobile-nav-item-active': isActive(item) }
        ]"
        @click="navigateTo(item)"
      >
        <div class="mobile-nav-icon-container">
          <i :class="item.icon" class="mobile-nav-icon" />
        </div>
      </button>
    </div>
  </div>
</template>

<style scoped>
.mobile-bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-top: 1px solid #e5e7eb;
  z-index: 1000;
  padding: 8px 0;
}

.mobile-bottom-nav-container {
  display: flex;
  justify-content: space-around;
  align-items: center;
  max-width: 100%;
  margin: 0 auto;
  padding: 0 16px;
}

.mobile-nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: none;
  border: none;
  padding: 8px 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 50%;
  min-width: 48px;
  min-height: 48px;
}

.mobile-nav-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.mobile-nav-icon {
  font-size: 20px;
  color: #6b7280;
  transition: color 0.2s ease;
}

.mobile-nav-item-active .mobile-nav-icon-container {
  background-color: #fbbf24;
}

.mobile-nav-item-active .mobile-nav-icon {
  color: #000;
}

.mobile-nav-item:hover .mobile-nav-icon-container {
  background-color: #f3f4f6;
}

.mobile-nav-item:hover .mobile-nav-icon {
  color: #374151;
}

.mobile-nav-item-active:hover .mobile-nav-icon-container {
  background-color: #fbbf24;
}

.mobile-nav-item-active:hover .mobile-nav-icon {
  color: #000;
}

/* Hide on desktop */
@media (min-width: 992px) {
  .mobile-bottom-nav {
    display: none;
  }
}
</style>
