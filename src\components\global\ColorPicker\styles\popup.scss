.bee-color-wrap {
  margin-right: 10px;
  width: 50px;
  height: 24px;
  box-shadow: 3px 0 5px #00000014;
  position: relative;
  cursor: pointer;
  overflow: hidden;
  display: inline-block;
  vertical-align: middle;

  &.transparent {
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAIAAADZF8uwAAAAGUlEQVQYV2M4gwH+YwCGIasIUwhT25BVBADtzYNYrHvv4gAAAABJRU5ErkJggg==);
    background-repeat: repeat;
  }

  &.round {
    width: 22px;
    height: 22px;
    border-radius: 50%;
    border: 1px solid #d8d8d8;
  }

  .current-color {
    width: 100%;
    height: 100%;
  }
}
