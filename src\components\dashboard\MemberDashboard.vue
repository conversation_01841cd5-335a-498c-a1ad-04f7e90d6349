<template>
  <!-- custom welcome -->
  <div class="card grid grid-cols-1 gap-4 mt-6 md:grid-cols-12">
    <div class="col-span-1 md:col-span-6 items-center justify-center md:order-last order-first">
      <div v-if="video_message">
        <div
          v-if="isImage(video_message)"
          class="aspect-w-16 aspect-h-9 w-full overflow-hidden rounded-lg"
        >
          <img
            :src="video_message"
            alt="Greeting Media"
            class="object-cover w-full h-full"
          >
        </div>
        <div v-else-if="isVideo(video_message)" class="relative card-container-without-padding-bottom">
          <div
            v-show="video_thumbnail && !isVideoPlaying"
            class="aspect-w-16 aspect-h-9 w-full overflow-hidden rounded-lg cursor-pointer tilt-card"
            @click="playVideo"
          >
            <img
              :src="video_thumbnail"
              alt="Video Thumbnail"
              class="object-cover w-full h-full"
            >
          </div>

          <video-player
            v-if="isVideoPlaying"
            ref="videoWelcome"
            :src="video_message"
            controls
            :options="videoOptions"
            :loop="false"
            class="w-full rounded-lg overflow-hidden vjs-fluid"
            @ready="onPlayerReady"
          />
        </div>
        <p v-else class="text-sm text-gray-500">Unsupported media type.</p>
      </div>
    </div>

    <div class="col-span-1 md:col-span-6">
      <DescriptionBox
        style="margin-left: -14px"
        :description="greeting_message"
        :isDisplay="true"
        :isLong="true"
      />
    </div>
  </div>
  <div>
    <!-- content 1 (my learning) -->
    <div class="mt-8">
      <div class="px-0 mb-4">
        <div class="flex flex-col sm:flex-row items-center justify-between">
          <div class="mb-2 sm:mb-0 w-full sm:w-auto">
            <div class="font-bold text-xl">
              {{ $t("My Learning") }}
            </div>
          </div>
          <div class="flex w-full sm:w-auto space-x-4">
            <IconField class="w-full sm:w-auto">
              <InputIcon>
                <i class="pi pi-search" />
              </InputIcon>
              <InputText
                v-model="myCourse.keyword"
                :placeholder="$t('Search')"
                class="w-full"
                @input="onChangeMyCourseKeyword"
              />
              <InputIcon v-if="myCourse.keyword" @click="myCourse.keyword = ''">
                <i class="pointer pi pi-times" />
              </InputIcon>
            </IconField>
          </div>
        </div>
      </div>
    </div>
    <div class="mt-4">
      <Card>
        <template #content>
          <!-- Filter -->
          <div v-if="tags.length > 0 && myCourse.keyword" class="mb-10">
            <div class="mb-2 space-x-10">
              <span class="font-bold">
                {{ $t("Filter Tags") }}
              </span>
              <span v-if="myCourse.filterTags.length > 0" class="pointer" @click="clearFilterTags()">
                {{ $t("Clear filter") }}
              </span>
            </div>
            <div class="space-y-2">
              <Chip
                v-for="(tag, index) in tags"
                :key="index"
                :label="tag.name"
                class="pointer mr-2"
                :class="{ active: myCourse.filterTags.includes(tag.name) }"
                @click="toggleTag(tag.name)"
              />
            </div>
          </div>

          <!-- Loader -->
          <loader-circle v-if="myCourse.isFetching" customClass="relative my-32" />

          <!-- List -->
          <DataView v-if="!myCourse.isFetching" :value="myCourse.items">
            <template #list="slotProps">
              <div class="pb-16 flex flex-col md:grid md:grid-cols-2 lg:grid-cols-4 gap-x-8 gap-y-12">
                <div
                  v-for="(item, index) in slotProps.items"
                  :key="index"
                  class="relative group card-container-without-padding-bottom mb-[86px] md:mb-[60px]"
                  :class="{'mb-[80px]': myCourse.items.length > 4}"
                >
                  <div class="gap-4">
                    <div
                      class="tilt-card pointer relative overflow-hidden rounded-2xl flex items-center bg-gray-100 group border-hover-card"
                      @click="showPreview(item)"
                    >
                      <div class="relative w-full" style="padding-bottom: 56.25%">
                        <img
                          v-if="item.thumbnail"
                          class="absolute top-0 left-0 w-full h-full object-cover rounded-2xl"
                          :src="item.thumbnail"
                          :alt="item.name"
                        >
                        <img
                          v-else
                          class="absolute top-0 left-0 w-full h-full object-cover rounded-2xl"
                          :src="__getRandomImage()"
                          :alt="item.name"
                        >
                      </div>

                      <!-- button actions member -->
                      <div class="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300" @click.stop="">
                        <Button :label="$t('Overview')" icon="pi pi-eye" iconPos="left" class="mr-2" style="color: #3b82f6; background: white" @click.stop="showPreview(item)" />
                        <Button v-if="item?.histories && !item?.histories.is_completed" :label="$t('Resume Course')" class="start-course-button" @click="startCourse(item)" />
                        <Button v-else :label="$t('Start Course')" class="start-course-button" @click="startCourse(item)" />
                      </div>
                    </div>

                    <div class="justify-between gap-6">
                      <div class="items-start">
                        <div>
                          <div class="text-lg font-medium mt-2 truncate">
                            {{ item.name }}
                          </div>
                          <div>
                            <div
                              class="text-gray-500 dark:text-gray-400 text-sm max-w-[40vw] truncate"
                              v-html="item.tagline"
                            />
                          </div>
                        </div>
                      </div>
                      <!-- progress bar -->
                      <div class="min-h-4">
                        <ProgressBar
                          class="mt-6"
                          style="height: 2px"
                          :showValue="false"
                          :value="
                            __calculatePercentageWatched(
                              item?.histories?.completed_items,
                              item?.histories?.total_items
                            )
                          "
                        />
                        <div
                          v-if="
                            __calculatePercentageWatched(
                              item?.histories?.completed_items,
                              item?.histories?.total_items
                            ) > 0
                          "
                          class="text-xs text-gray-500"
                        >
                          {{
                            __calculatePercentageWatched(
                              item?.histories?.completed_items,
                              item?.histories?.total_items
                            )
                          }}%
                        </div>
                      </div>

                      <div class="flex justify-between gap-8 mt-2">
                        <!-- Member -->
                        <div
                          class="font-bold text-primary-600 pointer"
                          @click="startCourse(item)"
                        >
                          <span
                            v-if="item?.histories && !item?.histories.is_completed"
                          >{{ $t("Resume Course") }}
                          </span>
                          <span
                            v-else
                          >{{ $t("Start Course") }}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </template>
            <template #empty>
              <div class="py-32 text-center">
                {{ $t("No data available.") }}
              </div>
            </template>
          </DataView>

          <div
            v-if="
              myCourse.currentPage * myCourse.limit < myCourse.total &&
                myCourse.totalPage > 1
            "
            class="flex justify-end mt-4 font-bold text-primary-600 mt-14 pointer"
            @click="$router.push('/courses')"
          >
            {{ $t("View more") }}
          </div>
        </template>
      </Card>
    </div>

    <!-- content 2 (suggested course) -->
    <div class="mt-14">
      <div class="px-4 sm:px-0 mb-4">
        <div class="flex items-center justify-between">
          <div>
            <div class="font-bold text-xl">
              {{ $t("Suggested Course") }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="mt-4">
      <Card>
        <template #content>
          <!-- List -->
          <DataView :value="suggested.items">
            <template #list="slotProps">
              <div class="pb-16 flex flex-col md:grid md:grid-cols-2 lg:grid-cols-4 gap-x-8 gap-y-20">
                <div
                  v-for="(item, index) in slotProps.items"
                  :key="index"
                  class="relative group card-container-without-padding-bottom mb-[86px] md:mb-[60px]"
                >
                  <div class="gap-4">
                    <div
                      class="tilt-card pointer relative overflow-hidden rounded-2xl flex items-center bg-gray-100 group border-hover-card"
                      @click="showPreview(item)"
                    >
                      <div class="relative w-full" style="padding-bottom: 56.25%">
                        <img
                          v-if="item.thumbnail"
                          class="absolute top-0 left-0 w-full h-full object-cover rounded-2xl"
                          :src="item.thumbnail"
                          :alt="item.name"
                        >
                        <img
                          v-else
                          class="absolute top-0 left-0 w-full h-full object-cover rounded-2xl"
                          :src="__getRandomImage()"
                          :alt="item.name"
                        >
                      </div>

                      <div class="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300" @click.stop="">
                        <Button :label="$t('Overview')" icon="pi pi-eye" iconPos="left" class="mr-2" style="color: #3b82f6; background: white" @click.stop="showPreview(item)" />
                        <Button v-if="item?.histories && !item?.histories.is_completed" :label="$t('Resume Course')" class="start-course-button" @click="startCourse(item)" />
                        <Button v-else :label="$t('Start Course')" class="start-course-button" @click="startCourse(item)" />
                      </div>
                    </div>
                    <div class="justify-between gap-6">
                      <div class="items-start">
                        <div>
                          <div class="text-lg font-medium mt-2 truncate">
                            {{ item.name }}
                          </div>
                          <div>
                            <div
                              class="text-gray-500 dark:text-gray-400 text-sm max-w-[40vw] truncate"
                              v-html="item.tagline"
                            />
                          </div>
                        </div>
                      </div>
                      <ProgressBar
                        class="mt-6"
                        style="height: 2px"
                        :showValue="false"
                        :value="
                          __calculatePercentageWatched(
                            item?.histories?.completed_items,
                            item?.histories?.total_items
                          )
                        "
                      />
                      <div
                        v-if="
                          __calculatePercentageWatched(
                            item?.histories?.completed_items,
                            item?.histories?.total_items
                          ) > 0
                        "
                        class="text-xs text-gray-500"
                      >
                        {{
                          __calculatePercentageWatched(
                            item?.histories?.completed_items,
                            item?.histories?.total_items
                          )
                        }}%
                      </div>
                      <div class="flex justify-between gap-8 mt-2">
                        <!-- Member -->
                        <div
                          class="font-bold text-primary-600 pointer"
                          @click="startCourse(item)"
                        >
                          {{ $t("Start Course") }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </template>
            <template #empty>
              <div class="py-32 text-center">
                {{ $t("Keep your eyes peeled – more exciting content is on the way!") }}
              </div>
            </template>
          </DataView>

          <div
            v-if="
              suggested.currentPage * suggested.limit < suggested.total &&
                suggested.totalPage > 1
            "
            class="flex justify-end mt-4 font-bold text-primary-600 mt-14 pointer"
            @click="viewMoreSuggested()"
          >
            {{ $t("View more") }}
          </div>
        </template>
      </Card>
    </div>
    <div
      class="w-full"
      :class="{'flex flex-col md:flex-row gap-4': webinarFeature.is_enable && webinarPermission.can_read &&
        certificateFeature.is_enable && certificatePermission.can_read}"
    >
      <!-- content 3 (webinar) -->
      <div
        class="w-full"
        :class="{'md:w-1/2 md:mr-6': webinarFeature.is_enable && webinarPermission.can_read &&
          certificateFeature.is_enable && certificatePermission.can_read}"
      >
        <div v-if="(webinarFeature.is_enable && webinarPermission.can_read)" class="mt-14">
          <div class="px-4 sm:px-0 mb-4">
            <div class="flex items-center justify-between">
              <div>
                <div class="font-bold text-xl">
                  {{ $t("Webinar") }}
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-if="(webinarFeature.is_enable && webinarPermission.can_read)" class="mt-4">
          <Card class="h-[404px] overflow-y-auto">
            <template #content>
              <!-- Empty -->
              <div
                v-if="webinar.items.length === 0"
                class="text-center py-20 text-gray-500"
              >
                {{ $t("No webinar available") }}
              </div>
              <!-- List -->
              
              <div v-for="(item, index) in webinar.items" :key="index">
                <a
                  class="pointer"
                  :href="`/webinars/${item.id}/show`"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <div class="">
                    <div class="pointer p-4 rounded-lg w-full grid grid-cols-6 gap-4">
                      <div class="col-span-2 flex items-center">
                        <div
                      
                          class="relative w-full overflow-hidden rounded-lg bg-gray-100"
                          style="padding-top: 60.25%;"
                        >
                          <img
                            v-if="item.thumbnail"
                            class="absolute top-0 left-0 w-full h-full object-cover"
                            :src="item.thumbnail"
                            :alt="item.name"
                          >
                          <img
                            v-else
                            class="absolute top-0 left-0 w-full h-full object-cover"
                            :src="__getRandomImage()"
                            :alt="item.name"
                          >
                        </div>
                      </div>
                      <div class="col-span-4">
                        <div class="font-medium">
                          <a
                            class="pointer font-bold text-lg"
                            :href="`/webinars/${item.id}/show`"
                            target="_blank"
                            rel="noopener noreferrer"
                          >
                            {{ item.name }}
                          </a>
                          <div>
                            {{ getDate(item.start_date, item.end_date) }}
                          </div>
                        </div>
                        <div
                          class="mt-2 max-w-6xl"
                          v-html="
                            item.description && item.description.length > 150
                              ? item.description.substring(0, 150) + '...'
                              : item.description
                          "
                        />
                      </div>
                    </div>
                  </div>
                </a>
              </div>
              <div
                v-if="
                  webinar.currentPage * webinar.limit < webinar.total && webinar.totalPage > 1
                "
                class="flex justify-end mt-4 font-bold text-primary-600 mt-14 pointer"
                @click="viewMoreWebinar()"
              >
                {{ $t("View more") }}
              </div>
            </template>
          </Card>
        </div>
      </div>
      <div
        class="w-full" 
        :class="{'md:w-1/2 md:ml-6': webinarFeature.is_enable && webinarPermission.can_read &&
          certificateFeature.is_enable && certificatePermission.can_read}"
      />
    </div>
  </div>
</template>

<script>
import Card from "primevue/card";
import DataView from "primevue/dataview";
import { delay } from "@/libraries/helper";
import ProgressBar from "primevue/progressbar";
import DescriptionBox from "@/components/form/DescriptionBox.vue";
import settingApi from "@/api/setting";
import { VideoPlayer } from "@videojs-player/vue";
import "video.js/dist/video-js.css";
import Chip from "primevue/chip";
import { mapGetters } from 'vuex';
import categoryApi from "@/api/category";

export default {
  components: {
    Card,
    DataView,
    ProgressBar,
    DescriptionBox,
    VideoPlayer,
    Chip,
  },
  props: {},
  data() {
    return {
      isFetching: false,
      tags: [],
      myCourse: {
        filterTags: [],
        isFetching: false,
        currentPage: 1,
        totalPage: 1,
        orderBy: "created_at",
        sortBy: "desc",
        limit: 4,
        keyword: "",
        items: [],
        total: 0,
      },
      suggested: {
        isFetching: false,
        currentPage: 1,
        totalPage: 1,
        orderBy: "created_at",
        sortBy: "desc",
        limit: 4,
        keyword: "",
        items: [],
        total: 0,
      },
      webinar: {
        isFetching: false,
        currentPage: 1,
        totalPage: 1,
        orderBy: "created_at",
        sortBy: "desc",
        limit: 4,
        keyword: "",
        items: [],
        total: 0,
      },
      isShowPreview: false,
      selectedItem: null,
      video_message: null,
      video_thumbnail: null,
      greeting_message: null,
      isVideoPlaying: false,
      player: null,
      videoOptions: {
        playbackRates: [0.5, 1, 1.5, 2], // Add playback speed options
        aspectRatio: "16:9",
        autoplay: true,
      },
    };
  },
  computed: {
    ...mapGetters({
      userRole: "auth/userRole",
      userFeatures: "auth/userFeatures",
    }),
    webinarFeature() {
      return this.userFeatures.find((item) => item.name === "webinar");
    },
    webinarPermission() {
      return this.webinarFeature?.permission ? this.webinarFeature.permission : {};
    },
    certificateFeature() {
      return this.userFeatures.find((item) => item.name === "certificate");
    },
    certificatePermission() {
      return this.certificateFeature?.permission
        ? this.certificateFeature.permission
        : {};
    },
  },
  watch: {
    keyword() {
      delay(() => {}, 500);
    },
  },
  created() {
    this.fetchListMyCourse();
    this.fetchListSuggested();
    this.fetchTags();
    if (this.webinarFeature.is_enable && this.webinarPermission.can_read) this.fetchListWebinar();
  },
  mounted() {
    this.initSettings();
  },
  beforeUnmount() {},
  methods: {
    playVideo() {
      this.isVideoPlaying = true;
    },
    onPlayerReady(player) {
      console.log("Player instance:", this.$refs.videoWelcome);
    },
    isImage(link) {
      if (!link) return false;
      const lowerLink = link.toLowerCase();
      return (
        lowerLink.endsWith(".jpg") ||
        lowerLink.endsWith(".jpeg") ||
        lowerLink.endsWith(".png") ||
        lowerLink.endsWith(".gif") ||
        lowerLink.endsWith(".webp") ||
        lowerLink.endsWith(".svg")
      );
    },
    isVideo(link) {
      if (!link) return false;
      const lowerLink = link.toLowerCase();
      return (
        lowerLink.endsWith(".mp4") ||
        lowerLink.endsWith(".webm") ||
        lowerLink.endsWith(".ogg") ||
        lowerLink.endsWith(".mov") ||
        lowerLink.endsWith(".avi")
      );
    },
    initSettings(key, data) {
      const params = {};
      this.isSaving = true;
      const callback = response => {
        this.isFetching = false;
        const data = response.data;
        for (let index = 0; index < data.length; index++) {
          const element = data[index];
          if (element.key === "greeting_message") this.greeting_message = element.value;
          if (element.key === "video_message") this.video_message = element.value;
          if (element.key === "video_thumbnail") this.video_thumbnail = element.value;
        }
      };
      const errorCallback = error => {
        const message = error.response.data.message;
        this.__showNotif("error", this.$t('Error'), message);
        this.isFetching = false;
      };
      settingApi.getList(params, callback, errorCallback);
    },
    startCourse(item) {
      this.$router.push(`/courses/${item.id}/show`);
    },
    closeModal() {
      this.isShowPreview = false;
    },
    showPreview(item) {
      this.selectedItem = item;
      this.isShowPreview = true;
    },
    getDate(start_date, end_date) {
      return this.__dateFormatWebinar(start_date, end_date);
    },
    resetFilter() {
      // Reset sorting
      this.myCourse.keyword = "";
      this.myCourse.orderBy = "updated_at"; // Default sort field
      this.myCourse.sortBy = "asc";
      this.fetchList(true);
    },
    viewMoreMyCourse() {
      this.myCourse.currentPage++;
      this.fetchListMyCourse();
    },
    viewMoreSuggested() {
      this.suggested.currentPage++;
      this.fetchListSuggested();
    },
    viewMoreWebinar() {
      this.webinar.currentPage++;
      this.fetchListWebinar();
    },
    onChangeMyCourseKeyword() {
      delay(() => {
        this.fetchListMyCourse(true);
      }, 500);
    },
    fetchTags() {
      this.tags = [];
      this.isFetching = true;
      const params = {
        limit: 9999,
        page: 1,
      };
      const callback = (response) => {
        const data = response.data;
        this.tags = data;
        this.isFetching = false;
      };
      const errorCallback = (error) => {
        const message = error.response.data.message;
        this.__showNotif("error", this.$t('Error'), message);
        this.isFetching = false;
      };
      categoryApi.getList(params, callback, errorCallback);
    },
    toggleTag(id) {
      const index = this.myCourse.filterTags.indexOf(id);
      if (index === -1) {
        this.myCourse.filterTags.push(id);
      } else {
        this.myCourse.filterTags = this.myCourse.filterTags.filter((tagId) => tagId !== id);
      }

      this.fetchListMyCourse(false);
    },
    clearFilterTags() {
      this.myCourse.filterTags = [];

      this.fetchListMyCourse(false);
    },
  },
};
</script>
