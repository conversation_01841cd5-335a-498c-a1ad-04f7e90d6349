<template>
	<div
		:id="elementId"
		class="relative"
	>
		<div
			class="h-[38px] w-[38px] rounded border border-gray-300 drop-shadow-mds"
			:style="swatchStyle"
			@click="openColorPicker"
		/>
		<div
			v-if="isColorPickerVisible"
			class="z-10 absolute top-[100%]"
		>
			<Chrome
				v-model="colors"
				:value="colors"
				@input="onColorChange"
			/>
			<t-button
				class="min-w-[227px]"
				@click="closeColorPicker"
			>
				Close
			</t-button>
		</div>
	</div>
</template>

<script>
import { generateRandomId } from '@/libraries/helper';
import { Chrome } from '@ckpack/vue-color';
import TButton from '@/components/global/Button.vue';


export default {
	components: {
		Chrome,
		TButton,
	},
	props: {
		value: {
			type: String,
			default: () => '#FFFFFFFF',
			required: true,
		},
		itemId: {
			type: String,
			default: () => '',
			required: true,
		},
		itemType: {
			type: String,
			default: () => 'txt',
			required: true,
		}
	},
	data() {
		return {
			isColorPickerVisible: false,
			colors: {},
			uuid: '',
			isFocus: false,
		};
	},
	computed: {
		elementId() {
			return `color-${this.uuid}`;
		},
		swatchStyle() {
			return {
				backgroundColor: this.colors.hex8,
				zIndex:5,
			};
		},
	},
	watch: {
		colors() {
			const hex = this.colors.hex8;
			this.$emit('input', hex, this.itemId, this.itemType);
		},
	},
	created() {
		this.initData();
		window.addEventListener('click', this.onClick);
	},
	mounted() {},
	beforeUnmount() {
		window.removeEventListener('click', this.onClick);
	},
	methods: {
		initData() {
			this.uuid = generateRandomId();
			if (this.value) this.colors.hex8 = this.value;
		},
		openColorPicker() {
			this.isFocus = true;
			this.isColorPickerVisible = true;
		},
		closeColorPicker() {
			this.isFocus = false;
			this.isColorPickerVisible = false;
		},
		onColorChange(color) {
			const hex = color.hex8;
			this.colors = hex;
			this.$emit('input', hex, this.itemId, this.itemType);
		},
		onClick({ target }) {
			if (!target.closest(`#${this.elementId}`)) {
				this.isFocus = false;
				this.isColorPickerVisible = false;
			}
		},
		onFocus() {
			this.isFocus = true;
		},
	},
};
</script>
