<template>
  <Dialog
    :visible="isVisible"
    modal
    :header="!isEdit ? $t('Add Role') : $t('Edit Role')"
    :style="{ width: '45rem' }"
    @update:visible="isVisible = $event"
  >
    <form class="space-y-3" @submit.prevent="submit">
      <div v-if="!isFetchPermission">
        <label for="access" class="block text-sm font-medium text-gray-700">{{
          $t("Access Permissions")
        }}</label>
        <div class="mt-4 space-y-4">
          <div
            v-for="(permission, index) in permissions"
            :key="index"
            class="flex items-center space-x-6 p-4 border rounded-lg shadow-sm bg-gray-50"
          >
            <div class="font-semibold text-gray-700 w-40">
              {{ permission.feature_name }}
            </div>
            <div class="flex items-center space-x-4">
              <label class="flex items-center space-x-2">
                <input
                  :id="`canread_${index}`"
                  v-model="permission.can_read"
                  autofocus
                  :value="permission.can_read"
                  :true-value="1"
                  :false-value="0"
                  type="checkbox"
                  class="form-checkbox h-5 w-5 text-blue-600"
                >
                <span>{{ $t("Read") }}</span>
              </label>
              <label class="flex items-center space-x-2">
                <input
                  :id="`canwrite_${index}`"
                  v-model="permission.can_write"
                  :value="permission.can_write"
                  :true-value="1"
                  :false-value="0"
                  type="checkbox"
                  class="form-checkbox h-5 w-5 text-blue-600"
                >
                <span>{{ $t("Write") }}</span>
              </label>
              <label class="flex items-center space-x-2">
                <input
                  :id="`candelete_${index}`"
                  v-model="permission.can_delete"
                  :value="permission.can_delete"
                  :true-value="1"
                  :false-value="0"
                  type="checkbox"
                  class="form-checkbox h-5 w-5 text-blue-600"
                >
                <span>{{ $t("Delete") }}</span>
              </label>
            </div>
          </div>
        </div>
      </div>

      <div class="flex justify-end">
        <Button
          :disabled="isSaving"
          outlined
          type="button"
          :label="$t('Cancel')"
          class="mr-2"
          @click="close"
        />
        <Button
          type="button"
          :loading="isSaving"
          :disabled="isSaving"
          :label="$t('Save Changes')"
          @click="submit"
        />
      </div>
    </form>
  </Dialog>
</template>

<script>
import { ROLE_DEFAULT } from "@/databags/role";
import roleApi from "@/api/role";
import { mapActions } from "vuex";
export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    item: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      isSaving: false,
      isFetchPermission: false,
      role: {
        ...this.__duplicateVar(ROLE_DEFAULT),
        access: {
          read: false,
          write: false,
          delete: false,
        },
      },
      permissions: [],
    };
  },
  computed: {
    isVisible: {
      get() {
        return this.visible;
      },
      set(value) {
        this.$emit("update:visible", value);
      },
    },
    isEdit() {
      return this.item && this.item.id;
    },
    params() {
      return {
        permissions: this.permissions.map((permission) => ({
          id: permission.id,
          can_read: permission.can_read || 0,
          can_write: permission.can_write || 0,
          can_delete: permission.can_delete || 0,
        })),
      };
    },
    isFormValid() {
      return this.permissions.some(
        (permission) =>
          permission.can_read || permission.can_write || permission.can_delete
      );
    },
  },
  watch: {
    isVisible() {
      if (this.isVisible && this.isEdit) {
        this.fetchPermissions(this.item.id);
      }
    },
  },
  methods: {
    ...mapActions({
      fetchUser: "auth/fetchUser",
    }),
    close() {
      this.$emit("onClose");
    },
    resetForm() {
      this.role = {
        ...this.__duplicateVar(ROLE_DEFAULT),
        access: {
          read: false,
          write: false,
          delete: false,
        },
      };
      this.permissions = [];
    },
    fetchPermissions(roleId) {
      this.isFetchPermission = true;
      roleApi.getPermission(
        roleId,
        (response) => {
          this.permissions = response.data;
          this.isFetchPermission = false;
        },
        (error) => {
          this.isFetchPermission = true;
          this.__showNotif("error", this.$t('Error'), error.response.data.message);
        }
      );
    },
    submit() {
      this.update();
    },
    update() {
      const params = this.params;
      this.isSaving = true;
      roleApi.updatePermission(
        this.item.id,
        params,
        (response) => {
          const item = response.data;
          this.fetchUser();
          this.$emit("onUpdate", item);
          this.isSaving = false;
          this.__showNotif("success", this.$t('User'), response.message);
          this.resetForm();
        },
        (error) => {
          this.__showNotif("error", this.$t('Error'), error.response.data.message);
          this.isSaving = false;
        }
      );
    },
  },
};
</script>
