import { client } from '@/libraries/http-client';
import { buildQuery } from '@/libraries/helper';

const endpoint = '/analytics';

export default {

  	// Get global
  getGraphGlobal(params, cb, errorCb) {
    const responseHandler = (response) => {
      if (cb) cb(response.data);
    };
    const errorHandler = (e) => {
      if (errorCb) errorCb(e);
    };
    const query = buildQuery(params);
    const url = `${endpoint}/graph?${query}`;
    client.get(url)
      .then(responseHandler)
      .catch(errorHandler);
  },

  // Get
  get(params, cb, errorCb) {
    const responseHandler = (response) => {
      if (cb) cb(response.data);
    };
    const errorHandler = (e) => {
      if (errorCb) errorCb(e);
    };
    const query = buildQuery(params);
    const url = `${endpoint}?${query}`;
    client.get(url)
      .then(responseHandler)
      .catch(errorHandler);
  },

  // Get group
  getGroup(groupId, cb, errorCb) {
    const responseHandler = (response) => {
      if (cb) cb(response.data);
    };
    const errorHandler = (e) => {
      if (errorCb) errorCb(e);
    };
    const url = `${endpoint}/group/${groupId}`;
    client.get(url)
      .then(responseHandler)
      .catch(errorHandler);
  },

  // Get group
  getCourse(courseId, cb, errorCb) {
    const responseHandler = (response) => {
      if (cb) cb(response.data);
    };
    const errorHandler = (e) => {
      if (errorCb) errorCb(e);
    };
    const url = `${endpoint}/course/${courseId}`;
    client.get(url)
      .then(responseHandler)
      .catch(errorHandler);
  },

  // Get group
  getMemberListOnGroup(params, groupId, cb, errorCb) {
    const responseHandler = (response) => {
      if (cb) cb(response.data);
    };
    const errorHandler = (e) => {
      if (errorCb) errorCb(e);
    };
    const query = buildQuery(params);
    const url = `${endpoint}/group/${groupId}/list?${query}`;
    client.get(url)
      .then(responseHandler)
      .catch(errorHandler);
  },

  	// Get group graph
  getMemberGraphOnGroup(params, groupId, cb, errorCb) {
    const responseHandler = (response) => {
      if (cb) cb(response.data);
    };
    const errorHandler = (e) => {
      if (errorCb) errorCb(e);
    };
    const query = buildQuery(params);
    const url = `${endpoint}/group/${groupId}/graph?${query}`;
    client.get(url)
      .then(responseHandler)
      .catch(errorHandler);
  },

  // Get group by course
  getMemberCourseGroup(params, courseId, cb, errorCb) {
    const responseHandler = (response) => {
      if (cb) cb(response.data);
    };
    const errorHandler = (e) => {
      if (errorCb) errorCb(e);
    };
    const query = buildQuery(params);
    const url = `${endpoint}/course/${courseId}/list?${query}`;
    client.get(url)
      .then(responseHandler)
      .catch(errorHandler);
  },

  
  	// Get group graph
  getMemberGraphOnCourse(params, courseId, cb, errorCb) {
    const responseHandler = (response) => {
      if (cb) cb(response.data);
    };
    const errorHandler = (e) => {
      if (errorCb) errorCb(e);
    };
    const query = buildQuery(params);
    const url = `${endpoint}/course/${courseId}/graph?${query}`;
    client.get(url)
      .then(responseHandler)
      .catch(errorHandler);
  },

  // Get group
  getMemberListOnCourse(params, courseId, cb, errorCb) {
    const responseHandler = (response) => {
      if (cb) cb(response.data);
    };
    const errorHandler = (e) => {
      if (errorCb) errorCb(e);
    };
    const query = buildQuery(params);
    const url = `${endpoint}/course/${courseId}/list?${query}`;
    client.get(url)
      .then(responseHandler)
      .catch(errorHandler);
  },
};
