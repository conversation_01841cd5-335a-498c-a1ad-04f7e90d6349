<template>
  <section class="card-profile">
    <h2 class="text-xl font-semibold text-gray-800 mb-6">
      {{ $t("Certificates") }}
    </h2>

    <loader-circle v-if="isFetching" />

    <div v-if="!isFetching" class="relative">
      <template v-if="certificates.length === 0 && !fetchError">
        <p class="text-gray-500 text-center italic p-10">
          {{ $t("Here come your earned certifications") }}.
        </p>
      </template>

      <template v-else-if="fetchError">
        <p class="text-red-500 text-center italic p-10">
          {{ $t("Failed to load certificates. Please try again.") }}
        </p>
        <div class="flex justify-center">
          <Button label="Retry" severity="warning" @click="fetchList" />
        </div>
      </template>

      <div
        v-else
        class="grid grid-cols-1 lg:grid-cols-3 gap-6 lg:max-h-[300px] lg:overflow-y-auto pr-3 pb-2"
      >
        <div
          v-for="(item, index) in certificates"
          :key="index"
          class="relative aspect-square w-full group lg:flex items-center justify-center pb-6 lg:pb-0"
        >
          <img
            :src="item.file_url"
            class="hidden lg:block w-full h-full rounded-lg shadow-md object-cover"
            alt="certificate"
            @error="handleImageError(index)"
          >
          <img
            :src="item.file_url"
            class="lg:hidden w-full h-full rounded-lg shadow-md object-cover"
            alt="certificate"
            @error="handleImageError(index)"
            @click.stop="previewCertificate(item)"
          >
          <a class="lg:hidden mt-2 flex items-center justify-end" :href="item.file_url" download>
            <i
              class="pi pi-download mr-2"
              style="color: #3b82f6"
            /><div class="truncate text-primary-600">
              {{ item.name }}
            </div>
          </a>
          <div
            class="absolute inset-0 hidden lg:flex items-center justify-center bg-black bg-opacity-50 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"
          >
            <Button
              icon="pi pi-eye"
              class="mr-1"
              aria-label="Preview Badge"
              severity="secondary"
              @click.stop="previewCertificate(item)"
            />
            <Button
              icon="pi pi-download"
              severity="secondary"
              aria-label="Download Badge"
              @click.stop="downloadCertificate(item)"
            />
          </div>
        </div>
      </div>
    </div>
  </section>
  <PreviewCertificate
    ref="previewCert"
    v-model:visible="isShowCertificate"
    :item="selectedCertificate"
    @onClose="closeModalTemplate"
  />
</template>

<script>
import certificateApi from "@/api/certificate";
import PreviewCertificate from "@/components/profile/PreviewCertificate.vue";

export default {
  components: {
    PreviewCertificate,
  },
  props: {},
  data() {
    return {
      isFetching: false,
      certificates: [],
      isShowCertificate: false,
      selectedCertificate: null,
      fetchError: false,
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {
    this.fetchList();
  },
  beforeUnmount() {},
  methods: {
    fetchList() {
      this.isFetching = true;
      this.fetchError = false;
      const params = {
        order_by: "created_at",
        sort_by: "desc",
        limit: 9999,
        page: 1,
      };
      if (this.keyword) params.keyword = this.keyword;
      const callback = (response) => {
        this.certificates = response?.data?.map(item => item.certificate);

        this.isFetching = false;
      };
      const errorCallback = (error) => {
        this.fetchError = true;
        const message = error.response.data.message;
        this.__showNotif("error", this.$t('Error'), message);
        this.isFetching = false;
      };
      certificateApi.getListMine(params, callback, errorCallback);
    },
    closeModalTemplate() {
      this.isShowCertificate = false;
      this.selectedCertificate = null;
    },
    downloadCertificate(certificate) {
      this.selectedCertificate = certificate;
      this.isShowCertificate = true;
      setTimeout(() => {
        this.$refs.previewCert.downloadCert(certificate.slug);
      }, 500);
    },
    previewCertificate(certificate) {
      this.selectedCertificate = certificate;
      this.isShowCertificate = true;
    },
    handleImageError(index) {
      // Handle image load errors, e.g., replace with a placeholder
      this.certificates[index].file_url = '/placeholder-image.png'; // Or use a default image path
    },
  },
};
</script>