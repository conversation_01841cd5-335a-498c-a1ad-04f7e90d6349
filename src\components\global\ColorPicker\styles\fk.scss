.bee-fk-colorPicker {
  position: relative;
  box-sizing: border-box;
  border-radius: 3px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.15);
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  background-color: white;
  width: 249px;
  padding-bottom: 10px;

  &__inner {
    padding: 12px;
  }

  &__header {
    margin-bottom: 12px;
    z-index: 999;
    text-align: left;

    .back {
      border: solid black;
      border-width: 0 1px 1px 0;
      display: inline-block;
      padding: 4px;
      margin-left: 2px;
      transform: rotate(135deg);
      -webkit-transform: rotate(135deg);
    }
  }

  &__display {
    position: relative;
    width: 100%;
    margin: 0;
    text-align: left;

    .transparent {
      background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAIAAADZF8uwAAAAGUlEQVQYV2M4gwH+YwCGIasIUwhT25BVBADtzYNYrHvv4gAAAABJRU5ErkJggg==);
      background-repeat: repeat;
    }

    .current-color {
      margin-right: 10px;
      width: 50px;
      height: 24px;
      box-shadow: 3px 0 5px #00000014;
      position: relative;
      cursor: pointer;
      overflow: hidden;
      display: inline-block;
      vertical-align: middle;

      .color-cube {
        width: 100%;
        height: 100%;
      }
    }

    .hexColor-prefix {
      position: relative;
      padding: 0 4px;
      font-size: 14px;
      display: inline-block;
      vertical-align: middle;
    }

    input {
      width: 25px;
      text-align: center;
      outline: 0;
      border-top: 0;
      border-right: 0;
      border-left: none;
      display: inline-block;
      vertical-align: middle;
      padding-bottom: 3px;
      border-bottom: 1px solid #e3e2e8;
    }

    .hexColor-input {
      width: 64px;
    }

    .action {
      float: right;
      vertical-align: middle;

      .clear-btn {
        border-color: transparent;
        color: #409eff;
        background: transparent;
        padding-left: 0;
        padding-right: 0;
        display: inline-block;
        vertical-align: middle;
        cursor: pointer;
      }

      .copy-btn {
        border-color: transparent;
        color: rgba(19, 206, 102, 0.8);
        background: transparent;
        padding-left: 0;
        padding-right: 0;
        display: inline-block;
        margin-left: 10px;
        vertical-align: middle;
        cursor: pointer;
      }
    }
  }
}
