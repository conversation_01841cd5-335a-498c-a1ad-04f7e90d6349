<script setup>
import { ref, onMounted } from 'vue';

const feedItems = ref([
  {
    id: 1,
    title: 'Sample News Item 1',
    content: 'This is a sample news feed item content...',
    date: '2024-01-15',
    author: '<PERSON>'
  },
  {
    id: 2,
    title: 'Sample News Item 2',
    content: 'Another sample news feed item content...',
    date: '2024-01-14',
    author: '<PERSON>'
  },
  {
    id: 3,
    title: 'Sample News Item 3',
    content: 'Yet another sample news feed item content...',
    date: '2024-01-13',
    author: '<PERSON>'
  },
  {
    id: 4,
    title: 'Sample News Item 4',
    content: 'More sample news feed item content...',
    date: '2024-01-12',
    author: '<PERSON>'
  }
]);

const navigateToDetail = (item) => {
  // Navigate to feed item detail
  console.log('Navigate to detail:', item);
};
</script>

<template>
  <div class="news-feed-page">
    <div class="news-feed-container">
      <div 
        v-for="item in feedItems" 
        :key="item.id"
        class="news-feed-item"
        @click="navigateToDetail(item)"
      >
        <div class="news-feed-item-content">
          <h3 class="news-feed-item-title">{{ item.title }}</h3>
          <p class="news-feed-item-text">{{ item.content }}</p>
          <div class="news-feed-item-meta">
            <span class="news-feed-item-author">{{ item.author }}</span>
            <span class="news-feed-item-date">{{ item.date }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.news-feed-page {
  padding: 20px 16px;
  padding-bottom: 80px; /* Space for bottom navigation */
  min-height: 100vh;
  background-color: #f9fafb;
}

.news-feed-container {
  max-width: 100%;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.news-feed-item {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 120px;
}

.news-feed-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.news-feed-item-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.news-feed-item-title {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 8px 0;
  line-height: 1.4;
}

.news-feed-item-text {
  font-size: 14px;
  color: #6b7280;
  margin: 0 0 12px 0;
  flex: 1;
  line-height: 1.5;
}

.news-feed-item-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #9ca3af;
}

.news-feed-item-author {
  font-weight: 500;
}

.news-feed-item-date {
  font-weight: 400;
}

/* Desktop styles */
@media (min-width: 992px) {
  .news-feed-page {
    padding: 40px;
    padding-bottom: 40px;
  }
  
  .news-feed-container {
    max-width: 800px;
    gap: 20px;
  }
  
  .news-feed-item {
    padding: 24px;
    min-height: 140px;
  }
  
  .news-feed-item-title {
    font-size: 18px;
  }
  
  .news-feed-item-text {
    font-size: 16px;
  }
}
</style>
