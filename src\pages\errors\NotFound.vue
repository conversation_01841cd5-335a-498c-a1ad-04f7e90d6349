<template>
	<div class="h-screen">
		<main class="max-w-7xl w-full mx-auto px-4 sm:px-6 lg:px-8">
			<div class="flex-shrink-0 pt-16" />
			<div class="max-w-xl mx-auto py-4">
				<div class="text-center">
					<p class="text-xl font-semibold tracking-wide">
						404 - Whoops
					</p>
					<p class="mt-2 text-sm">
						The thing that you try to find is not available? may we offer another treat?
					</p>
					<div class="mt-4 flex place-content-center">
						<div class="text-sm bg-primary-600 h-[25px] pt-[3px] rounded-sm text-white px-4 uppercase mr-2 pointer">
							<router-link to="/">
								Take me home
							</router-link>
						</div>
					</div>
				</div>
			</div>
		</main>
	</div>
</template>

<script>

import { mapGetters,  } from 'vuex';

export default {
	components: {
	},
	data() {
		return {
		};
	},
	computed: {
		...mapGetters({
			user: 'auth/user',
		}),
	},
	created() {
	},
	mounted() {
	},
	methods: {
	},
};
</script>