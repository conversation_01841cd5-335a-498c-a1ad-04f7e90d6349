<template>
  <div>
    <!-- Heading -->
    <div class="px-4 sm:px-0 mb-4">
      <h3 class="text-base/7 font-semibold text-gray-900">
        {{ $t("Roles") }}
      </h3>
      <div class="flex items-center justify-between">
        <p class="mt-1 max-w-2xl text-sm/6 text-gray-500">
          {{ $t("Manage user roles for better data filtering and access control.") }}
        </p>
        <div class="flex space-x-4">
          <IconField>
            <InputIcon>
              <i class="pi pi-search" />
            </InputIcon>
            <InputText
              v-model="keyword"
              :placeholder="$t('Type in search term')"
              class="min-w-[246px]"
            />
            <!-- Clear Icon -->
            <InputIcon v-if="keyword" @click="keyword = ''">
              <i class="pointer pi pi-times" />
            </InputIcon>
          </IconField>
        </div>
      </div>
    </div>
    <loader-circle v-if="isFetching" />
    <div v-if="!isFetching" class="card">
      <DataTable
        ref="myTable"
        :value="items"
        tableStyle="min-width: 50rem"
        @update:sortOrder="updateSortOrder"
        @update:sortField="updatesortField"
      >
        <template #header>
          <div class="flex justify-between">
            <Button
              v-if="rolePermission.can_write"
              type="button"
              icon="pi pi-plus"
              :label="$t('Create Role')"
              @click="showCreate()"
            />
            <!-- <Button
							type="button"
							icon="pi pi-filter-slash"
							label="Clear"
							outlined
							@click="resetAll()"
						/> -->
          </div>
        </template>
        <template #empty> No Roles found. </template>
        <template v-if="!isReset">
          <Column header="No." headerStyle="width:3rem">
            <template #body="slotProps">
              {{ slotProps.index + 1 + (currentPage - 1) * limit }}.
            </template>
          </Column>
          <Column sortable field="name" header="Name" style="width: 80%" />
          <Column sortable field="right" header="Right" style="width: 80%" />
          <Column
            v-if="rolePermission.can_write || rolePermission.can_delete"
            header="Actions"
            style="width: 20%"
          >
            <template #body="slotProps">
              <div class="flex justify-content-left">
                <Button
                  v-if="rolePermission.can_write"
                  icon="pi pi-lock"
                  severity="warn"
                  variant="outlined"
                  aria-label="Edit"
                  class="mr-2"
                  @click="editRoleItem(slotProps)"
                />
                <Button
                  v-if="rolePermission.can_write"
                  icon="pi pi-pencil"
                  variant="outlined"
                  aria-label="Edit"
                  class="mr-2"
                  @click="editItem(slotProps)"
                />

                <Button
                  v-if="rolePermission.can_delete"
                  icon="pi pi-trash"
                  severity="danger"
                  variant="outlined"
                  aria-label="Delete"
                  @click="deleteItem(slotProps.data)"
                />
              </div>
            </template>
          </Column>
        </template>
      </DataTable>
      <div v-if="items.length > 0" class="grid grid-cols-3 px-0 mx-0 mt-8">
        <div />
        <div>
          <Paginator
            :rows="limit"
            :first="(currentPage - 1) * limit"
            :totalRecords="total"
            :rowsPerPageOptions="[10, 20, 30]"
            template="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport"
            :currentPageReportTemplate="`({first} ${$t('to')} {last} ${$t('of')} {totalRecords})`"
            class="mt-8"
            @page="pageUpdate"
          />
        </div>
        <div class="flex items-center w-full items-center justify-end px-0 mx-0">
          <div class="pt-7">
            {{ $t("Record(s) per page") }}
          </div>
          <div>
            <Paginator
              :rows="limit"
              :first="(currentPage - 1) * limit"
              :totalRecords="total"
              :rowsPerPageOptions="[10, 20, 30]"
              template="RowsPerPageDropdown"
              :currentPageReportTemplate="`({first} ${$t('to')} {last} ${$t('of')} {totalRecords})`"
              class="mt-8 pr-0 mr-0"
              @page="pageUpdate"
            />
          </div>
        </div>
      </div>
    </div>
    <ModalForm
      v-model:visible="isModalVisible"
      :item="selectedItem"
      @onCreate="onCreate"
      @onUpdate="onUpdate"
      @onClose="closeModal"
    />
    <ModalRoleForm
      v-model:visible="isModaRolelVisible"
      :item="selectedItem"
      @onUpdate="onUpdate"
      @onClose="closeModal"
    />
  </div>
</template>

<script>
import roleApi from "@/api/role";
import { delay } from "@/libraries/helper";
import ModalForm from "@/components/roles/ModalForm.vue";
import ModalRoleForm from "@/components/roles/ModalRoleForm.vue";
import { mapGetters } from "vuex";

export default {
  components: {
    ModalForm,
    ModalRoleForm,
  },
  props: {},
  data() {
    return {
      isFetching: false,
      currentPage: 1,
      totalPage: 1,
      orderBy: "created_at",
      sortBy: "desc",
      limit: 10,
      keyword: "",
      items: [],
      total: 0,
      isReset: false,
      isModalVisible: false,
      isModaRolelVisible: false,
      selectedItem: null,
    };
  },
  computed: {
    ...mapGetters({
      userFeatures: "auth/userFeatures",
    }),
    roleFeature() {
      return this.userFeatures.find((item) => item.name === "role");
    },
    rolePermission() {
      return this.roleFeature?.permission ? this.roleFeature.permission : {};
    },
  },
  watch: {
    keyword() {
      delay(() => {
        this.fetchList(true);
      }, 500);
    },
    isModalVisible() {
      if (!this.isModalVisible) {
        this.selectedItem = null;
      }
    },
    isModaRolelVisible() {
      if (!this.isModaRolelVisible) {
        this.selectedItem = null;
      }
    },
  },
  created() {
    this.fetchList();
  },
  mounted() {},
  beforeUnmount() {},
  methods: {
    fetchList(isReset = false) {
      if (isReset) {
        this.currentPage = 1;
        this.items = [];
      }
      this.isFetching = true;
      const params = {
        order_by: this.orderBy,
        sort_by: this.sortBy,
        limit: this.limit,
        page: this.currentPage,
      };
      if (this.keyword) params.keyword = this.keyword;
      const callback = (response) => {
        const data = response.data;
        this.totalPage = response.meta.last_page;
        this.total = response.meta.total;
        this.items = data;
        this.isFetching = false;
      };
      const errorCallback = (error) => {
        const message = error.response.data.message;
        this.__showNotif("error", this.$t('Error'), message);
        this.isFetching = false;
      };
      roleApi.getList(params, callback, errorCallback);
    },
    updatesortField(event) {
      this.orderBy = event;
    },
    updateSortOrder(event) {
      if (event === 1) {
        this.sortBy = "desc";
      } else {
        this.sortBy = "asc";
      }
      this.fetchList();
    },
    pageUpdate(event) {
      this.currentPage = event.page + 1;
      if (event.rows !== this.limit) this.currentPage = 1;
      this.limit = event.rows;
      this.fetchList();
    },
    resetAll() {
      this.isReset = true;
      // Reset sorting
      this.keyword = "";
      this.orderBy = "created_at"; // Default sort field
      this.sortBy = "desc";
      this.fetchList(true);
      setTimeout(() => {
        this.isReset = false;
      }, 100);
    },
    editItem(item) {
      this.isModalVisible = true;
      this.selectedItem = item.data;
    },
    editRoleItem(item) {
      this.isModaRolelVisible = true;
      this.selectedItem = item.data;
    },
    onRemove(selectedId) {
      const index = this.items.findIndex((curr) => curr.id === selectedId);
      if (index !== -1) this.items.splice(index, 1);
    },
    approveRemove(id) {
      // If no selected
      this.isDeleting = true;
      const callback = (response) => {
        this.onRemove(id);
        this.isDeleting = false;
        const message = response.message;
        this.__showNotif("success", this.$t('Success'), message);
      };
      const errorCallback = (error) => {
        const message = error.response.data.message;
        this.__showNotif("error", this.$t('Error'), message);
        this.isDeleting = false;
      };
      roleApi.delete(id, callback, errorCallback);
    },
    deleteItem(item) {
      this.$confirm.require({
        message: `${this.$t("Do you want to delete this")} ${item.name}?`,
        header: this.$t("Delete Role"),
        icon: "pi pi-info-circle",
        rejectProps: {
          label: this.$t("Cancel"),
          severity: "secondary",
          outlined: true,
        },
        acceptProps: {
          label: this.$t("Delete"),
          severity: "danger",
        },
        accept: () => {
          this.approveRemove(item.id);
        },
        reject: () => {},
      });
    },
    showCreate() {
      this.selectedItem = null;
      this.isModalVisible = true;
    },
    onUpdate(item) {
      const itemIndex = this.items.findIndex((curr) => curr.id === item.id);
      if (itemIndex !== -1) Object.assign(this.items[itemIndex], item);
      this.closeModal();
    },
    onCreate(item) {
      if (this.sortBy === "desc") this.items.unshift(item);
      else if (this.sortBy === "asc") this.items.push(item);
      this.closeModal();
    },
    closeModal() {
      this.isModalVisible = false;
      this.isModaRolelVisible = false;
      this.selected = null;
    },
  },
};
</script>
