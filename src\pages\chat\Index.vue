<script setup>
import { ref } from 'vue';

const messages = ref([
  {
    id: 1,
    sender: '<PERSON>',
    message: 'Hello! How can I help you today?',
    time: '10:30 AM',
    isOwn: false
  },
  {
    id: 2,
    sender: 'You',
    message: 'Hi! I have a question about the new features.',
    time: '10:32 AM',
    isOwn: true
  },
  {
    id: 3,
    sender: '<PERSON>',
    message: 'Sure! What would you like to know?',
    time: '10:33 AM',
    isOwn: false
  },
  {
    id: 4,
    sender: 'You',
    message: 'Can you explain how the new dashboard works?',
    time: '10:35 AM',
    isOwn: true
  }
]);

const newMessage = ref('');

const sendMessage = () => {
  if (newMessage.value.trim()) {
    messages.value.push({
      id: messages.value.length + 1,
      sender: 'You',
      message: newMessage.value,
      time: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
      isOwn: true
    });
    newMessage.value = '';
  }
};
</script>

<template>
  <div class="chat-page">
    <div class="chat-container">
      <div class="chat-messages">
        <div 
          v-for="message in messages" 
          :key="message.id"
          :class="[
            'chat-message',
            { 'chat-message-own': message.isOwn }
          ]"
        >
          <div class="chat-message-content">
            <div class="chat-message-text">{{ message.message }}</div>
            <div class="chat-message-meta">
              <span class="chat-message-sender">{{ message.sender }}</span>
              <span class="chat-message-time">{{ message.time }}</span>
            </div>
          </div>
        </div>
      </div>
      
      <div class="chat-input-container">
        <div class="chat-input-wrapper">
          <input
            v-model="newMessage"
            type="text"
            placeholder="Type a message..."
            class="chat-input"
            @keyup.enter="sendMessage"
          />
          <button 
            class="chat-send-button"
            @click="sendMessage"
            :disabled="!newMessage.trim()"
          >
            <i class="pi pi-send" />
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.chat-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f9fafb;
}

.chat-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  max-width: 100%;
  margin: 0 auto;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 20px 16px;
  padding-bottom: 100px; /* Space for input and bottom nav */
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.chat-message {
  display: flex;
  align-items: flex-end;
}

.chat-message-own {
  justify-content: flex-end;
}

.chat-message-content {
  max-width: 80%;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 16px;
  padding: 12px 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.chat-message-own .chat-message-content {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.chat-message-text {
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 4px;
}

.chat-message-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 11px;
  opacity: 0.7;
  gap: 8px;
}

.chat-message-sender {
  font-weight: 500;
}

.chat-message-time {
  font-weight: 400;
}

.chat-input-container {
  position: fixed;
  bottom: 60px; /* Above bottom navigation */
  left: 0;
  right: 0;
  background: white;
  border-top: 1px solid #e5e7eb;
  padding: 12px 16px;
  z-index: 100;
}

.chat-input-wrapper {
  display: flex;
  gap: 8px;
  align-items: center;
  max-width: 100%;
  margin: 0 auto;
}

.chat-input {
  flex: 1;
  padding: 12px 16px;
  border: 1px solid #e5e7eb;
  border-radius: 24px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s ease;
}

.chat-input:focus {
  border-color: #3b82f6;
}

.chat-send-button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #3b82f6;
  color: white;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.chat-send-button:hover:not(:disabled) {
  background: #2563eb;
}

.chat-send-button:disabled {
  background: #d1d5db;
  cursor: not-allowed;
}

/* Desktop styles */
@media (min-width: 992px) {
  .chat-container {
    max-width: 800px;
  }
  
  .chat-messages {
    padding: 40px;
    padding-bottom: 120px;
  }
  
  .chat-input-container {
    position: relative;
    bottom: auto;
    padding: 20px 40px;
  }
  
  .chat-message-content {
    max-width: 70%;
  }
  
  .chat-message-text {
    font-size: 16px;
  }
  
  .chat-input {
    font-size: 16px;
    padding: 14px 20px;
  }
}
</style>
