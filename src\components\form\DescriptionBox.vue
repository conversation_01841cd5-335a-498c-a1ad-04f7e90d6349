<template>
  <div>
    <div class="main-container">
      <div
        ref="editorContainerElement"
        class="editor-container editor-container_classic-editor"
      >
        <div class="editor-container__editor">
          <div
            ref="editorElement"
            :class="{
              'display-mode ': isDisplay,
              'long-ck': isLong,
              'general-ck': !isLong && !isDisplay,
              'medium-height': isMediumHeight,
              'description-ck': !isMobile,
              'description-ck-mobile': isMobile
            }"
          >
            <!-- Move class here -->
            <ckeditor
              v-if="isLayoutReady && !isDisplay"
              ref="editor"
              :modelValue="computedDescription"
              :editor="editor"
              :config="config"
              :class="{ 'display-mode': isDisplay }"
              @update:model-value="emitUpdateDescription"
              @blur="onEditorBlur"
              @ready="onEditorReady"
            />

            <!-- only for display purposes -->
            <div v-if="isDisplay">
              <ckeditor
                v-if="isLayoutReady"
                ref="editor"
                :modelValue="
                  isShowMoreButton ? displayedDescription : computedDescription
                "
                :editor="editor"
                :config="config"
                :class="{ 'display-mode': isDisplay }"
                @update:model-value="emitUpdateDescription"
                @blur="onEditorBlur"
                @ready="onEditorReady"
              />
              <Button
                v-if="showReadMoreButton && isShowMoreButton"
                severity="contrast"
                variant="outlined"
                :label="!showFullDescription ? $t('Show more') : $t('Show less')"
                class="ml-4"
                :class="{ 'mt-4': showFullDescription }"
                @click="showFullDescription = !showFullDescription"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { delay } from "@/libraries/helper";
import {
  ClassicEditor,
  AccessibilityHelp,
  FontColor,
  Font,
  Alignment,
  Autoformat,
  AutoImage,
  Autosave,
  BlockQuote,
  Bold,
  Essentials,
  Heading,
  ImageBlock,
  ImageCaption,
  ImageInline,
  ImageInsert,
  ImageInsertViaUrl,
  ImageResize,
  ImageStyle,
  ImageTextAlternative,
  ImageToolbar,
  ImageUpload,
  Indent,
  IndentBlock,
  Italic,
  Link,
  LinkImage,
  List,
  ListProperties,
  MediaEmbed,
  Mention,
  Paragraph,
  PasteFromOffice,
  PictureEditing,
  SelectAll,
  Table,
  TableCaption,
  TableCellProperties,
  TableColumnResize,
  TableProperties,
  TableToolbar,
  TextTransformation,
  TodoList,
  Underline,
  Undo,
} from "ckeditor5";

import "ckeditor5/ckeditor5.css";

/**
 * Please update the following values with your actual tokens.
 * Instructions on how to obtain them: https://ckeditor.com/docs/trial/latest/guides/real-time/quick-start.html
 */
const CKBOX_TOKEN_URL = "<YOUR_CKBOX_TOKEN_URL>";

export default {
  name: "App",
  props: {
    ph: {
      type: String,
      default: "",
    },
    description: {
      type: String,
      default: "",
    },
    users: {
      type: Array,
      default: () => [],
    },
    collabs: {
      type: Array,
      default: () => [],
    },
    isDisplay: {
      type: Boolean,
      default: () => false,
    },
    isShowMoreButton: {
      type: Boolean,
      default: () => false,
    },
    isLong: {
      type: Boolean,
      default: () => false,
    },
    isMediumHeight: {
      type: Boolean,
      default: () => false,
    },
  },
  emits: ["update:description"],
  data() {
    return {
      isLayoutReady: false,
      config: null, // CKEditor needs the DOM tree before calculating the configuration.
      editor: ClassicEditor,
      showFullDescription: false, // Add this for "Read More"
      showReadMoreButton: false, // Add this for button visibility
      displayedDescription: "", // For truncated/full text
      windowWidth: 0,
    };
  },
  computed: {
    // Computed property to ensure CKEditor always receives a string
    computedDescription() {
      return this.description || ""; // Fallback to empty string if description is null or undefined
    },
    isMobile() {
      return this.windowWidth < 991;
    },
  },
  watch: {
    description() {
      this.calculateDisplayedDescription(); // Recalculate when description prop changes
    },
    showFullDescription() {
      this.calculateDisplayedDescription(); // Recalculate when "Read More" is clicked
    },
  },
  created() {},
  mounted() {
    window.addEventListener("resize", this.updateWindowWidth);
    this.updateWindowWidth(); // Set initial value on mount
    this.config = {
      toolbar: {
        items: [
          "heading",
          "bold", // Bold text
          "italic", // Italic text
          "underline", // Underline text
          "|",
          "fontColor", // Text color
          "fontBackgroundColor", // Background color for text
          "|",
          "bulletedList", // Bulleted list
          "numberedList", // Numbered list
          "|",
          "alignment", // Clear formatting
        ],
        shouldNotGroupWhenFull: true,
      },
      plugins: [
        AccessibilityHelp,
        FontColor,
        Font,
        Alignment,
        Autoformat,
        AutoImage,
        Autosave,
        BlockQuote,
        Bold,
        Essentials,
        Heading,
        ImageBlock,
        ImageCaption,
        ImageInline,
        ImageInsert,
        ImageInsertViaUrl,
        ImageResize,
        ImageStyle,
        ImageTextAlternative,
        ImageToolbar,
        ImageUpload,
        Indent,
        IndentBlock,
        Italic,
        Link,
        LinkImage,
        List,
        ListProperties,
        MediaEmbed,
        Mention,
        Paragraph,
        PasteFromOffice,
        PictureEditing,
        SelectAll,
        Table,
        TableCaption,
        TableCellProperties,
        TableColumnResize,
        TableProperties,
        TableToolbar,
        TextTransformation,
        TodoList,
        Underline,
        Undo,
      ],
      heading: {
        options: [
          {
            model: "paragraph",
            title: "Paragraph",
            class: "ck-heading_paragraph",
          },
          {
            model: "heading1",
            view: "h1",
            title: "Heading 1",
            class: "ck-heading_heading1",
          },
          {
            model: "heading2",
            view: "h2",
            title: "Heading 2",
            class: "ck-heading_heading2",
          },
          {
            model: "heading3",
            view: "h3",
            title: "Heading 3",
            class: "ck-heading_heading3",
          },
          {
            model: "heading4",
            view: "h4",
            title: "Heading 4",
            class: "ck-heading_heading4",
          },
          {
            model: "heading5",
            view: "h5",
            title: "Heading 5",
            class: "ck-heading_heading5",
          },
          {
            model: "heading6",
            view: "h6",
            title: "Heading 6",
            class: "ck-heading_heading6",
          },
        ],
      },
      image: {
        toolbar: [
          "toggleImageCaption",
          "imageTextAlternative",
          "|",
          "imageStyle:inline",
          "imageStyle:wrapText",
          "imageStyle:breakText",
          "|",
          "resizeImage",
          "|",
        ],
      },
      initialData: "",
      link: {
        addTargetToExternalLinks: true,
        defaultProtocol: "https://",
        decorators: {
          toggleDownloadable: {
            mode: "manual",
            label: "Downloadable",
            attributes: {
              download: "file",
            },
          },
        },
      },
      list: {
        properties: {
          styles: true,
          startIndex: true,
          reversed: true,
        },
      },
      mention: {
        feeds: [
          {
            marker: "@",
            feed: this.getUsers, // Provide users for mentions
            minimumCharacters: 1,
            itemRenderer: this.customMentionRenderer,
          },
        ],
      },
      placeholder: this.ph,
      table: {
        contentToolbar: [
          "tableColumn",
          "tableRow",
          "mergeTableCells",
          "tableProperties",
          "tableCellProperties",
        ],
      },
    };

    // configUpdateAlert(this.config);

    // this.config.readOnly = true;

    this.isLayoutReady = true;
  },
  beforeUnmount() {
    window.removeEventListener("resize", this.updateWindowWidth);
  },
  methods: {
    updateWindowWidth() {
      this.windowWidth = window.innerWidth;
    },
    calculateDisplayedDescription() {
      const plainText = this.description
        ? this.description
          .replace(/<[^>]*>/g, "")
          .replace(/&nbsp;/g, " ")
          .trim()
        : ""; // Plain text for counting

      const hasNewLines = /[\r\n]+|\n|\r/.test(plainText); // Check for newlines in plain text

      if (plainText.length > 300 || hasNewLines) {
        this.showReadMoreButton = true;

        let cutoff = 300;

        if (hasNewLines) {
          const newlineIndex = plainText.search(/[\r\n]+|\n|\r/);

          //if there is new line and char length more than 300, get the minimum index between new line index and 300
          if (newlineIndex !== -1 && plainText.length > 300) {
            cutoff = Math.min(newlineIndex, 300);
          } else if (newlineIndex !== -1) {
            cutoff = newlineIndex;
          }
        }

        cutoff = Math.min(cutoff, plainText.length);

        // Find the corresponding HTML index based on plain text cutoff
        let htmlCutoff = 0;
        let plainTextIndex = 0;
        for (let i = 0; i < this.description.length; i++) {
          if (this.description[i] === "<") {
            while (this.description[i] !== ">") {
              i++;
            }
            i++; // Skip the closing '>'
          } else if (this.description[i] === "&") {
            while (this.description[i] !== ";") {
              i++;
            }
            i++;
          } else {
            plainTextIndex++;
          }

          if (plainTextIndex >= cutoff) {
            htmlCutoff = i + 1; // +1 to include the char at cutoff
            break;
          }
        }

        this.displayedDescription = this.showFullDescription
          ? this.description
          : this.description.substring(0, htmlCutoff).trim() + "...";
      } else {
        this.showReadMoreButton = false;
        this.displayedDescription = this.description;
      }
    },

    onEditorBlur() {
      this.isFocus = false;
      this.$emit("onBlur");
    },
    emitUpdateDescription(value) {
      // Debounce the emit to avoid frequent updates
      this.$emit("update:description", value);
    },
    // Provide the list of users for the mention dropdown
    getUsers(queryText) {
      return new Promise((resolve) => {
        const matchedUsers = this.users
          .filter((user) => user.fullName.toLowerCase().includes(queryText.toLowerCase()))
          .map((user) => ({
            id: `@${user.id}`, // Pass a string id with '@' marker (required format)
            text: user.fullName, // This will be shown to the user in the mention dropdown
          }));
        resolve(matchedUsers); // Return array of { id, text } objects
      });
    },
    // Custom mention item renderer to show fullName only in the dropdown
    customMentionRenderer(item) {
      const itemElement = document.createElement("span");
      itemElement.textContent = item.text; // Show the fullName in dropdown
      return itemElement;
    },
    // Set up the document event listener when the editor is ready
    onEditorReady(editor) {
      // Enable read-only mode
      if (this.isDisplay) editor.enableReadOnlyMode("custom-readonly");

      const model = editor.model.document;
      this.calculateDisplayedDescription();
      // Listen to changes in the editor document
      model.on("change:data", () => {
        if (!editor.isReadOnly) {
          // Only detect mentions when not read-only
          const content = editor.getData();
          this.detectMentions(content); // Check the editor content for mentions
        }
      });
    },

    // Detect mentions by parsing the HTML content
    detectMentions(content) {
      // Parse the content string into an HTML document
      const parser = new DOMParser();
      const doc = parser.parseFromString(content, "text/html");

      // Find all <span> elements with class 'mention'
      const mentionElements = doc.querySelectorAll("span.mention");

      mentionElements.forEach((el) => {
        const fullName = el.textContent.trim(); // Get the displayed full name (text inside the span)
        // Try to find the mentioned user based on the fullName
        const mentionedUser = this.users.find((user) => user.fullName === fullName);

        if (mentionedUser) {
          this.triggerApiForUser(mentionedUser); // Trigger API call with user ID
        } else {
        }
      });
    },

    // Trigger the API call using the user ID
    triggerApiForUser(mentionedUserResult) {
      const checkCollabs = this.collabs.find(
        (user) => user.id === mentionedUserResult.id
      );
      if (!checkCollabs) {
        const finalCollab = this.collabs;
        finalCollab.push(mentionedUserResult);
        this.$emit("select", finalCollab);
      }
    },
  },
};

/**
 * This function exists to remind you to update the config needed for premium features.
 * The function can be safely removed. Make sure to also remove call to this function when doing so.
 */
function configUpdateAlert(config) {
  if (configUpdateAlert.configUpdateAlertShown) {
    return;
  }

  const isModifiedByUser = (currentValue, forbiddenValue) => {
    if (currentValue === forbiddenValue) {
      return false;
    }

    if (currentValue === undefined) {
      return false;
    }

    return true;
  };

  const valuesToUpdate = [];

  configUpdateAlert.configUpdateAlertShown = true;

  if (!isModifiedByUser(config.ckbox?.tokenUrl, "<YOUR_CKBOX_TOKEN_URL>")) {
    valuesToUpdate.push("CKBOX_TOKEN_URL");
  }

  if (valuesToUpdate.length) {
    window.alert(
      [
        "Please update the following values in your editor config",
        "in order to receive full access to the Premium Features:",
        "",
        ...valuesToUpdate.map((value) => ` - ${value}`),
      ].join("\n")
    );
  }
}
</script>
<style>
/* This CSS code applies custom styles to the CKEditor component used in the CommentBox component. The styles are used to:
  - Hide the "Powered by Balloon" text in the CKEditor balloon panel
  - Set a fixed height and enable vertical scrolling for the CKEditor editable area
  - Set the color and underline style for links in the CKEditor content
  - Set the color and underline style for selected links in the CKEditor content
  - Set the font size for placeholder text in the CKEditor content
  - Add a border radius to the CKEditor editable area
  - Hide the CKEditor top toolbar */
.description-ck .ck-editor__editable_inline {
  max-height: 357px !important; /* Set your custom height */
  overflow-y: auto !important;
  padding: 10px 26px !important;
}

.description-ck-mobile .ck-editor__editable_inline {
  overflow-y: auto !important;
  padding: 10px 26px !important;
}

.description-ck .ck-editor__editable_inline .ck-heading_heading1 {
  font-size: 2em !important; /* Example style */
  /* Add other heading styles here */
}

.medium-height.description-ck .ck-editor__editable_inline {
  min-height: 150px; /* Adjust the minimum height as needed */
  max-height: 357px !important; /* Set your custom height */
  overflow-y: auto !important;
}

.long-ck.description-ck .ck-editor__editable_inline {
  max-height: none !important; /* Set your custom height */
  padding: 10px 26px !important;
}
.ck-editor__editable a {
  color: blue !important;
  text-decoration: underline !important;
}
.ck-link_selected {
  color: blue !important;
  text-decoration: underline !important;
}
.ck-placeholder {
  font-size: 14px !important;
}
.ck.ck-editor__main > .ck-editor__editable {
  border-radius: 4px !important;
}
.ck.ck-editor__top.ck-reset_all {
}
.ck.ck-list__item {
  min-width: 0 !important;
}
.ck.ck-list__item > .ck-button.ck-on:not(.ck-list-item-button) {
  display: flex !important;
}
.display-mode .ck.ck-editor__main > .ck-editor__editable:not(.ck-focused) {
  background-color: transparent !important;
  border: none !important;
  margin: 0px;
  margin-left: -10px;
}

.display-mode .ck.ck-editor__main > .ck-editor__editable.ck-focused {
  border: none !important;
  margin: 0px;
  margin-left: -10px;
  box-shadow: none;
}

.display-mode .ck.ck-editor__top.ck-reset_all {
  display: none !important;
}

.ck .ck-powered-by {
  display: none !important;
}
.ck.ck-dropdown.ck-heading-dropdown .ck-dropdown__button .ck-button__label {
  width: 5em !important;
}
</style>
