<template>
  <div class="min-h-screen bg-white flex">
    <div
      class="flex-1 flex flex-col justify-center py-12 px-4 sm:px-6 lg:flex-none lg:px-20 xl:px-24"
    >
      <div class="mx-auto w-full max-w-sm lg:w-96">
        <div>
          <img :key="siteLogo" class="h-12 w-auto" :src="dynamicSiteLogo" alt="LMS">
          
          <h2 class="mt-6 text-3xl font-extrabold text-gray-900">Verify Account</h2>
        </div>

        <div class="mt-8">
          <div v-if="isVerifying" class="mt-6">Veriyfing account...</div>
          <div v-if="!isVerifying && !isError" class="mt-6">
            All set and ready to go,
            <router-link
              to="/login"
              class="font-medium text-primary-600 hover:text-primary-500"
            >
              Back to Login
            </router-link>
          </div>
          <div v-if="!isVerifying && isError" class="mt-6">
            Your token is invalid / Expired,
            <router-link
              to="/login"
              class="font-medium text-primary-600 hover:text-primary-500"
            >
              Back to Login
            </router-link>
          </div>
        </div>
      </div>
    </div>
    <div class="hidden lg:block relative w-0 flex-1">
      <!-- <img
				class="absolute h-full w-full object-cover"
				src="@/assets/images/verify-account-wide.png"
				alt=""
			> -->
    </div>
  </div>
</template>

<script>
import authApi from "@/api/auth";
import { mapActions, mapGetters } from "vuex";

export default {
  components: {},
  setup() {
    return {};
  },
  data() {
    return {
      email: null,
      isVerifying: false,
      isError: false,
    };
  },
  computed: {
    ...mapGetters({
      siteLogo: "auth/siteLogo",
    }),
    isValidEmailAddress() {
      return isValidEmail(this.email);
    },
    dynamicSiteLogo() {
      return this.siteLogo ? this.siteLogo : "https://minio.beta.mylms.no/betalms/siteLogo/images/812-300.png";
    },
    isFormValid() {
      return this.isValidEmailAddress;
    },
    token() {
      const url = this.$route.redirectedFrom.fullPath; // Get the current URL
      const match = url.match(/[?&]token=([^&]+)/);
      const token = match ? match[1] : null;
      return token;
    },
  },
  mounted() {
    this.verifying();
  },
  created() {},
  methods: {
    ...mapActions({
      fetchUser: "auth/fetchUser",
    }),
    verifying() {
      this.isVerifying = true;
      this.isError = false;
      const callback = (response) => {
        this.$store.dispatch("auth/clearAuth");
        setTimeout(() => {
          this.$store.dispatch("auth/setSession", response.authToken);
          this.$router.push("/");
          this.fetchUser();
          this.isVerifying = false;
          this.isError = false;
        }, 400);
      };
      const errorCallback = (error) => {
        const message = "Error Verifying Account";
        this.__showNotif("error", this.$t('Error'), message);
        this.isVerifying = false;
        this.isError = true;
        this.doLogout();
      };
      const params = {
        token: this.token,
      };
      authApi.verify(params, callback, errorCallback);
    },
    doLogout() {
      this.$store.dispatch("auth/clearAuth");
    },
  },
};
</script>
