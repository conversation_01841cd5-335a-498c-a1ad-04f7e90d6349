<template>
	<ColorPicker
		:id="elementId"
		v-model:pureColor="color"
		v-model:gradientColor="gradientColor"
		format="hex8"
	/>
</template>

<script>
// import { ColorPicker } from "vue3-colorpicker";
import { ColorPicker } from "../global/ColorPicker";
import "vue3-colorpicker/style.css";
import { generateRandomId, delay } from '@/libraries/helper';


export default {
	components: {
		ColorPicker,
	},
	props: {
		value: {
			type: String,
			default: () => '#FFFFFFFF',
			required: true,
		},
		itemId: {
			type: String,
			default: () => '',
			required: true,
		},
		itemType: {
			type: String,
			default: () => 'txt',
			required: true,
		}
	},
	data() {
		return {
			isColorPickerVisible: false,
			color: '',
			localColor: '',
			gradientColor: '',
			uuid: '',
		};
	},
	computed: {
		elementId() {
			return `color-${this.uuid}`;
		},
	},
	watch: {
		color(prev, next) {
			if (this.value !== this.color) {
				this.change();
			}
		},
	},
	created() {
		this.initData();
		window.addEventListener('click', this.onClick);
	},
	mounted() {},
	beforeUnmount() {
		window.removeEventListener('click', this.onClick);
	},
	methods: {
		change() {
			delay(() => {
				this.$emit('input', this.color, this.itemId, this.itemType);
			}, 300);
		},
		initData() {
			this.uuid = generateRandomId();
			if (this.value) this.color = this.value;
		},
		onColorChange(color) {
			this.color = color;
		},
	},
};
</script>
