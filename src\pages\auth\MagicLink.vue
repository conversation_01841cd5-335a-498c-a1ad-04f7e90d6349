<template>
  <div class="min-h-full flex flex-col justify-center py-40 sm:px-6 lg:px-8">
    <div class="sm:mx-auto sm:w-full sm:max-w-lg">
      <img :key="siteLogo" class="mx-auto h-12 mb-5 w-auto" :src="dynamicSiteLogo" alt="LMS">
    </div>
    <span class="text-center mb-6">
      {{ $t("We will send the magic link for log in to your email") }}</span>

    <div class="mt-2 sm:mx-auto sm:w-full lg:max-w-md drop-shadow-xl">
      <div class="bg-white py-12 px-14 shadow sm:rounded-xl lg:px-14 md:px-14">
        <form class="space-y-2" @submit.prevent="submit">
          <div>
            <div class="mt-1">
              <div class="mb-2">Email Address</div>
              <t-input
                v-model="email"
                :type="`email`"
                :value="email"
                class="w-full mb-2"
              />
              <span
                v-if="!isValidEmailAddress && email && email.length !== 0"
                class="text-red-500 text-xs"
              >Invalid Email Address</span>
            </div>
          </div>

          <div class="flex items-center justify-end">
            <div class="text-sm mb-5">
              <router-link to="/login" class="font-medium text-blue">
                Back to login
              </router-link>
            </div>
          </div>

          <div>
            <t-button
              :type="'submit'"
              :color="`primary-solid`"
              class="w-full"
              :isDisabled="!isFormValid || isSendingEmail"
              :isLoading="isSendingEmail"
            >
              Send Email
            </t-button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import authApi from "@/api/auth";
import TInput from "@/components/form/Input.vue";
import TButton from "@/components/global/Button.vue";
import { isValidEmail } from "@/libraries/helper";
import { mapGetters } from "vuex";

export default {
  components: {
    TInput,
    TButton,
  },
  setup() {
    return {};
  },
  data() {
    return {
      email: null,
      isSendingEmail: false,
    };
  },
  computed: {
    ...mapGetters({
      siteLogo: "auth/siteLogo",
    }),
    isValidEmailAddress() {
      return isValidEmail(this.email);
    },
    isFormValid() {
      return this.isValidEmailAddress;
    },
    dynamicSiteLogo() {
      return this.siteLogo ? this.siteLogo : "https://minio.beta.mylms.no/betalms/siteLogo/images/812-300.png";
    },
  },
  mounted() {},
  methods: {
    submit() {
      this.isSendingEmail = true;

      this.isSaving = true;
      const callback = (response) => {
        const message = response.message;
        this.__showNotif("success", this.$t('User'), message);
        this.isSendingEmail = false;
        this.email = "";
      };
      const errorCallback = (error) => {
        const message = error.response.data.message;
        this.__showNotif("error", this.$t('Error'), message);
        this.isSendingEmail = false;
      };
      const params = {
        email: this.email,
      };
      authApi.magicLink(params, callback, errorCallback);
    },
  },
};
</script>
