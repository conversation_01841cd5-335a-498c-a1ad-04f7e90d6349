<template>
  <div class="bg-[#222222] min-h-screen flex flex-col justify-center py-[24vh] overflow-hidden">
    <div class="mx-auto w-1/6">
      <div class="w-full text-center">
        <t-button
          :color="`primary-solid`"
          class="w-full mt-3"
          :isLoading="isVerifying"
          @click="reset"
        >
          Reset Data
        </t-button>
        <div
          v-if="!user"
          class="mt-4 text-white"
        >
          Back to <span
            class="text-primary-600 pointer font-medium"
            @click="() => {$router.push('/login')}"
          >Login</span>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import localforage from 'localforage';
import { mapGetters, mapActions } from 'vuex';
import TInput from '@/components/form/Input.vue';
import TButton from '@/components/global/Button.vue';
import { duplicateVar } from '@/libraries/helper';

export default {
  name: 'PreLive',
  components: {
    TButton,
    TInput
  },
  props: {
    item: {
      type: Object,
    },
    status: {
      type: String,
    },
  },
  data() {
    return {
    };
  },
  created() {
  },
  beforeUnmount() {
  },
  mounted() {
  },
  sockets: {
		
  },
  methods: {
    reset() {
      localStorage.clear();
      localforage.clear();
      this.__showNotif('success', this.$t('Reset'), this.$t('Reset Success'));
      this.$router.push('/login');
    },
  },
  computed: {
    ...mapGetters({
      user: 'auth/user',
      getToken: 'auth/getToken',
    }),
  },
  watch: {
  },
	
};
</script>