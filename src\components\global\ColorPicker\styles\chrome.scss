.bee-chrome-colorPicker {
  position: relative;
  box-sizing: border-box;
  border-radius: 3px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.15);
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  background-color: white;
  width: 250px;
  padding-bottom: 20px;

  &-body {
    padding: 0 12px;
    background-color: #fff;

    .chrome-controls {
      display: flex;

      .chrome-color-wrap {
        position: relative;
        width: 36px;
        height: 36px;
        border-radius: 50%;
        overflow: hidden;
        z-index: 1;

        .current-color {
          width: 100%;
          height: 100%;
        }
      }

      .chrome-sliders {
        flex: 1;
        margin-left: 10px;
      }
    }
  }
}
