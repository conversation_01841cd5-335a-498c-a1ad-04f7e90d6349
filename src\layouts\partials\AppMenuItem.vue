<script setup>
import { useLayout } from "@/layouts/partials/composables/layout";
import { onBeforeMount, ref, watch, computed } from "vue";
import { useRoute } from "vue-router";
import { useStore } from "vuex"; // Import useStore from Vuex

const route = useRoute();
const store = useStore();

const { layoutState, setActiveMenuItem, onMenuToggle } = useLayout();

const props = defineProps({
  item: {
    type: Object,
    default: () => ({}),
  },
  index: {
    type: Number,
    default: 0,
  },
  root: {
    type: Boolean,
    default: true,
  },
  parentItemKey: {
    type: String,
    default: null,
  },
});

const isActiveMenu = ref(false);
const itemKey = ref(null);

onBeforeMount(() => {
  itemKey.value = props.parentItemKey
    ? props.parentItemKey + "-" + props.index
    : String(props.index);

  const activeItem = layoutState.activeMenuItem;

  isActiveMenu.value =
    activeItem === itemKey.value || activeItem
      ? activeItem.startsWith(itemKey.value + "-")
      : false;
});

watch(
  () => layoutState.activeMenuItem,
  (newVal) => {
    isActiveMenu.value =
      newVal === itemKey.value || newVal.startsWith(itemKey.value + "-");
  }
);

function itemClick(event, item) {
  if (item.disabled) {
    event.preventDefault();
    return;
  }

  if (
    (item.to || item.url) &&
    (layoutState.staticMenuMobileActive || layoutState.overlayMenuActive)
  ) {
    onMenuToggle();
  }

  if (item.command) {
    item.command({ originalEvent: event, item: item });
  }

  const foundItemKey = item.items
    ? isActiveMenu.value
      ? props.parentItemKey
      : itemKey
    : itemKey.value;

  setActiveMenuItem(foundItemKey);
}

function checkActiveRoute(item) {
  return route.path === item.to || route.name === item?.to?.name;
}

const userRole = computed(() => store.getters["auth/userRole"]); // Access Vuex getter
</script>

<template>
  <li :class="{ 'layout-root-menuitem': root, 'active-menuitem': isActiveMenu }">
    <div
      v-if="root && item.visible !== false"
      class="layout-menuitem-root-text flex items-center"
    >
      {{ item && item.label ? $t(item.label) : item.label }}
    </div>
    <a
      v-if="(!item.to || item.items) && item.visible !== false"
      :href="item.url"
      :class="item.class"
      :target="item.target"
      tabindex="0"
      @click="itemClick($event, item, index)"
    >
      <div v-if="item?.icon?.includes('<svg')" class="mr-2" v-html="item.icon" />
      <i v-else :class="item.icon" class="layout-menuitem-icon" />
      <span class="layout-menuitem-text">
        {{ item && item.label ? $t(item.label) : item.label }}
      </span>
      <i v-if="item.items" class="pi pi-fw pi-angle-down layout-submenu-toggler" />
    </a>
    <router-link
      v-if="
        item?.to &&
          !item.items &&
          item.visible !== false &&
          (item?.roles?.length === 0 || item?.roles?.includes(userRole.right))
      "
      :class="[item.class, { 'active-route': checkActiveRoute(item) }]"
      tabindex="0"
      :to="item.to !== 'disabled' ? item.to : $router.push($route)"
      @click="itemClick($event, item, index)"
    >
      <div v-if="item?.icon?.includes('<svg')" class="mr-2" v-html="item.icon" />
      <i :class="item.icon" class="layout-menuitem-icon" />
      <span class="layout-menuitem-text">
        {{ item && item.label ? $t(item.label) : item.label }}
      </span>
      <i v-if="item.items" class="pi pi-fw pi-angle-down layout-submenu-toggler" />
    </router-link>

    <Transition v-if="item.items && item.visible !== false" name="layout-submenu">
      <ul v-show="root ? true : isActiveMenu" class="layout-submenu">
        <app-menu-item
          v-for="(child, i) in item.items"
          :key="child"
          :index="i"
          :item="child"
          :parentItemKey="itemKey"
          :root="false"
        />
      </ul>
    </Transition>
  </li>
</template>
