<template>
	<button
		class="inline-flex items-center text-sm font-medium"
		:type="type"
		:class="theClass"
		:disabled="isDisabled"
		:data-test="dataTest"
	>
		<svg
			v-if="isLoading"
			class="animate-spin -ml-1 h-5 w-5 "
			:class="loadingStyle"
			xmlns="http://www.w3.org/2000/svg"
			fill="none"
			viewBox="0 0 24 24"
		>
			<circle
				class="opacity-25"
				cx="12"
				cy="12"
				r="10"
				stroke="currentColor"
				stroke-width="4"
			/>
			<path
				class="opacity-75"
				fill="currentColor"
				d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
			/>
		</svg>
		<slot />
	</button>
</template>

<script>
export default {
	components: {
	},
	props: {
		type: {
			type: String,
			default: () => 'button',
		},
		isDisabled: {
			type: Boolean,
			default: () => false,
		},
		isLoading: {
			type: Boolean,
			default: () => false,
		},
		color: {
			type: String,
			default: () => '',
		},
		customClass: {
			type: String,
			default: () => 'default',
		},
		loadingStyle: {
			type: String,
			default: () => 'text-primary-600 mr-3',
		},
		dataTest: {
			type: String,
			default: () => '',
		},
	},
	data() {
		return {
		};
	},
	computed: {
		theClass() {
			let btnClass = `focus:ring-offset-gray-50 focus:ring-pink-500 border-gray-300 text-gray-700 bg-white hover:bg-gray-50 h-10 drop-shadow-sm`;
			switch (this.color) {
			case 'primary-solid':
				btnClass = `px-4 py-2 justify-center border shadow-sm rounded-md focus:ring-primary-500 text-white bg-primary-600 hover:bg-primary-700 h-10 drop-shadow-sm`;
				break;
			case 'red-solid':
				btnClass = `px-4 py-2 justify-center border shadow-sm rounded-md focus:ring-red-500 text-white bg-red-600 hover:bg-red-700 h-10 drop-shadow-sm`;
				break;
			case 'secondary-solid':
				btnClass = `px-4 py-2 justify-center border shadow-sm rounded-md focus:ring-primary-500 border-[#D2D6DC]-300 bg-[#EEEEEE] hover:bg-white h-10 drop-shadow-sm`;
				break;
			case 'primary-solid-tour':
				btnClass = `px-4 py-2 justify-center border shadow-sm rounded-md focus:ring-primary-500 text-white text-xs bg-primary-600 hover:bg-primary-700 h-10 drop-shadow-sm`;
				break;
			case 'secondary-border-tour':
				btnClass = `px-4 py-2 justify-center border shadow-sm rounded-md focus:ring-primary-500 border-[#D2D6DC]-300 text-xs bg-transparent hover:bg-gray-300 hover:text-white  h-10 drop-shadow-sm`;
				break;
			case 'primary-solid-flat':
				btnClass = `px-4 py-2 justify-center border shadow-sm rounded-b-md focus:ring-transparent text-white bg-primary-600 hover:bg-primary-700 h-10 drop-shadow-sm`;
				break;
			case 'clean-white-flat':
				btnClass = `px-4 py-2 text-white hover:bg-[#F3F4F6]`;
				break;
			case 'primary-solid-icon':
				btnClass = `px-0 py-0 justify-center border shadow-sm rounded-md focus:ring-primary-500 text-white bg-primary-600 hover:bg-primary-700 h-10 drop-shadow-sm`;
				break;
			case 'secondary-solid-icon':
				btnClass = `px-0 py-0 justify-center border shadow-sm rounded-md focus:ring-primary-500 border-[#D2D6DC]-300 bg-[#EEEEEE] hover:bg-white h-10 drop-shadow-sm`;
				break;
			case 'primary-white':
				btnClass = `bg-[#FFFFFF] border-[#D2D6DC] border drop-shadow-sm hover:bg-primary-600 hover:text-white rounded-md `;
				break;
			case 'rounded-white':
				btnClass = `bg-[#FFFFFF] hover:bg-gray-600 hover:text-white rounded-full py-2 px-4`;
				break;
			case 'rounded-primary':
				btnClass = `text-white bg-primary-600 hover:bg-primary-600 hover:text-white rounded-full py-2 px-4 `;
				break;
			case 'rounded-alternative':
				btnClass = `text-white bg-primary-600 hover:bg-primary-600 hover:text-white rounded-full py-2 px-4 `;
				break;
			}
			let theClass = `${btnClass} ${this.customClass}`;
			if (this.isDisabled) {
				btnClass = `px-4 py-2 justify-center rounded-md focus:ring-primary-500 border-[#D2D6DC]-300 bg-[#D2D6DC] hover:bg-white h-10 drop-shadow-sm`;
				theClass = `${btnClass} ${this.customClass}`;
			}
			return theClass;
		},
		
	},
	watch: {},
	created() {},
	mounted() {},
	beforeUnmount() {},
	methods: {},
};
</script>